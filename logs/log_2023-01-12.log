
[2023-01-12 15:05:57] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-12 15:07:13] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-12 15:09:20] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-13","date_departure":"2023-01-15","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-12 23:08:10] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-12 23:10:43] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-14","date_departure":"2023-01-16","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-12 23:15:09] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2023-01-12 23:16:42] INFO Processing new AJAX Request width payload {"code":"9695","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2023-01-12 23:17:09] INFO Processing new AJAX Request width payload {"code":"9655","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2023-01-12 23:19:37] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2023-01-14","date_departure":"2023-01-16","promocodevalue":"undefined","product_id":"6","adult_count":"1","child_count":"0","first_name":"Testeur","last_name":"Client","email":"<EMAIL>","phone":"0654254365","country":"CM","notes":"Nothing","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"2","wrs_action":"save_order","action":"wrs_process_ajax"}
[2023-01-12 23:22:23] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"6L466822LT334552S","id":"59","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2023-01-12 23:42:05] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-14","date_departure":"2023-01-16","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-12 23:42:44] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-17","date_departure":"2023-01-18","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-12 23:57:36] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-12 23:58:03] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-14","date_departure":"2023-01-16","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-12 23:58:44] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2023-01-12 23:59:20] INFO Processing new AJAX Request width payload {"code":"9655","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2023-01-12 23:59:50] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2023-01-14","date_departure":"2023-01-16","promocodevalue":"undefined","product_id":"4","adult_count":"1","child_count":"0","first_name":"Testeur","last_name":"Client","email":"<EMAIL>","phone":"0654254365","country":"DE","notes":"Nothing","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"2","wrs_action":"save_order","action":"wrs_process_ajax"}