<?php
/**
 * AByster WPForms Payments Uninstall
 *
 * Uninstalling AByster WPForms Payments deletes tables, and options.
 *
 * @package Medcamer_Payment
 * @version 1.0.0
 */

defined('WP_UNINSTALL_PLUGIN') || exit;

global $wpdb, $wp_version;

$tables = [
  "wrs_products",
  "wrs_locations",
  "wrs_currencies",
  "wrs_languages",
  "wrs_configurations",
  "wrs_promotional_codes",
  "wrs_persons",
  "wrs_reservations",
  "wrs_reservation_items",
  "wrs_reservation_payments",
  "wrs_product_block",
  "wrs_promotional_code_products"
];
$wpdb->query("SET FOREIGN_KEY_CHECKS=0");
foreach ($tables as $value) {
  $table = "{$wpdb->prefix}wrs_products";
  $wpdb->query("DROP TABLE IF EXISTS {$value}");
}
$wpdb->query("SET FOREIGN_KEY_CHECKS=1");

wp_cache_flush();