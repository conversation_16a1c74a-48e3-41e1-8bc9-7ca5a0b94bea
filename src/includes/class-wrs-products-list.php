<?php

/**
 * WodaabeReservationsProducts_Table class will create the page to load the table
 */
class WodaabeReservationsProducts_Table {

  /**
   * Display the list table page
   *
   * @return Void
   */
  public function list_table_page() {
    if (isset($_GET["action"])) {
      $action = $_GET["action"];
      if ("show" == $action) {
        return $this->show_details_page();
      } else if ("new" == $action) {
        return $this->show_create_page();
      } else if ("delete" == $action) {
        return $this->do_delete();
      } else if ("edit" == $action) {
        return $this->show_edit_page();
      }
    }
    $listTable = new WodaabeReservationsProducts_List_Table();
    $listTable->prepare_items();
    $new_link = $_SERVER['REQUEST_URI'] . "&action=new";
    ?>
      <div class="wrap">
          <h2 style="display: inline-block;">Products</h2>
          <a style="display: inline-block;" href="<?php echo $new_link ?>" class="page-title-action">Add New</a>
          <p>Products are appartments and studios that can be subject to reservation.</p>
          <?php $listTable->display();?>
      </div>
    <?php
}

  public function do_delete() {
    global $wpdb, $wrsModule;
    $id = $_GET["id"];

    $wpdb->delete(
      'wp_wrs_products',
      ['id' => $id],
      ['%d'],
    );
    wp_redirect("/wp-admin/admin.php?page=wodaabe-reservations-products");

  }

  public function sync_prices() {
      global $wpdb;

      wrs_log("Starting prices syncing...");
      $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE;

      $products = $wpdb->get_results($sql, 'ARRAY_A');
      //wrs_log($products);

      $price_listings = $this->get_pricelabs_listings();
      //wrs_log($price_listings);

      $tablename = $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE;

      foreach ( $products as $product ) {
          wrs_log("Evaluating product with ID " . $product["id"]);
          if ( $product["price_listing_id"] !== null ) {
              wrs_log("Product got a price sync id");
            foreach ( $price_listings as $price ) {
                wrs_log("Price listing ID: " . $price["id"] . " product['price_listing_id']: " . $product["price_listing_id"]);
                wrs_log("Is equal: ");
                wrs_log($product["price_listing_id"] === $price["id"]);
                if ( $product["price_listing_id"] === $price["id"] ) {
                    wrs_log("Price listing ID: " . $price["id"] . " = product['price_listing_id']: " . $product["price_listing_id"] . " Updating the price from " . $product["price_per_night"] . " to " . $price["base"]);
                    $wpdb->update(
                      $tablename,
                      array(
                        'price_per_night' => is_numeric($price["base"]) ? $price["base"] : $product['price_per_night'],
                      ),
                      array('id' => $product["id"])
                    );
                    break;
                }
            }
          }
      }
      wrs_log("Prices synced successfully");
  }

  public function get_pricelabs_listings() {
        $prices = null;

        $api_url = 'https://api.pricelabs.co/v1/listings?skip_hidden=true&only_syncing_listings=true';
        $api_key = $_ENV['PRICELABS_API_KEY'];

        // Initialize cURL session
        $ch = curl_init();

        // Set the cURL options
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'accept: application/json',
            'X-API-Key: ' . $api_key
        ));

        // Execute the cURL request
        $response = curl_exec($ch);
        //wrs_log("Listing: ");
        //wrs_log($response);

        // Check for errors
        if ($response === false) {
            echo 'cURL error: ' . curl_error($ch);
            wrs_log("curl_error: ");
            wrs_log(curl_error($ch));
        } else {
            // Decode the JSON response
            $data = json_decode($response, true);
            //wrs_log("Listing data : ");
            //wrs_log($data);

            // Check if the response is valid
            if ($data) {
                // Process the response data here
                $prices = $data;
            } else {
                echo 'Failed to decode JSON response.';
                wrs_log('Failed to decode JSON response.');
            }
        }

        // Close cURL session
        curl_close($ch);

        return $prices["listings"];
  }

  public function show_details_page() {
    global $wpdb, $wrsModule;
    $id = $_GET["id"];

    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE . " WHERE id = " . $id;

    $item = $wpdb->get_row($sql, 'ARRAY_A');

    $sql2 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE . " WHERE id = " . $item["location_id"];

    $item2 = $wpdb->get_row($sql2, 'ARRAY_A');

    $items = array(
      '#' => $item["id"],
      'Title' => $item["title"],
      'Picture' => "<a href=" . $item["picture"] . " target='_blank'>View</a>",
      'Capacity' => $item["capacity"],
      'Description' => $item["description"],
      'Price per night' => $item["price_per_night"],
      'Rate text' => $item["rate_text"],
      'Date created' => $item["date_add"] ? \date("d/m/Y H:i:s", \strtotime($item["date_add"])) : "<b>-</b>",
      'Last update' => $item["date_add"] ? \date("d/m/Y H:i:s", \strtotime($item["date_add"])) : "<b>-</b>",
      'Location' => $item2["title"],
    );

    ?>
  <div class="wrap">
      <h2>Products</h2>
      <p>Find below the details about the product #<?php echo $id ?> </p>
      <table class="wp-list-table widefat  striped table-view-excerpt reservations_page_wodaabe-reservations-reservations">


<tbody id="the-list">
  <?php
foreach ($items as $key => $value) {?>
      <tr>
      <td class=" has-row-actions column-primary" data-colname="ID">
        <strong>
          <?php echo $key ?>
        </strong>
      </td>
      <td class="date_arrive column-date_arrive" data-colname="Arrival date">
        <?php echo $value ?>
      </td>
      <?php }?>
    </table>
  </div>
<?php
}

  public function show_create_page() {
    global $wpdb, $wrsModule;

    if ("POST" == $_SERVER['REQUEST_METHOD']) {
      $wpdb->insert('wp_wrs_products',
        array(
          'title' => $_POST['Title'],
          'picture' => $_POST['Picture'],
          'capacity' => $_POST['Capacity'],
          'description' => $_POST['Description'],
          'price_per_night' => $_POST['Price_per_night'],
          'rate_text' => $_POST['Rate_text'],
          'location_id' => $_POST['Location'],
          'listingMapId' => isset($_POST['listingMapId']) ? $_POST['listingMapId'] : null,
        )
      );
      wp_redirect("/wp-admin/admin.php?page=wodaabe-reservations-products");
    }
    $sql2 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE;

    $item2 = $wpdb->get_results($sql2, 'ARRAY_A');

    $items = array(
      'Title' => "",
      'Picture' => "",
      'Capacity' => "",
      'Price per night' => "",

    );
    $items2 = array(
      'Description' => "",
      'Rate text' => "",
    );
    $items3 = array(
      'Location' => "",
    );

    ?>
<div class="wrap">
<form method="POST">
    <h2>Products</h2>
    <p>Here you can create a new products </p>
    <table class="wp-list-table widefat fixed striped table-view-excerpt reservations_page_wodaabe-reservations-reservations">


<tbody id="the-list">
<?php
foreach ($items as $key => $value) {?>
    <tr>
    <td class=" has-row-actions column-primary" style="width: 20%">
      <strong>
        <?php echo $key ?>
      </strong>
    </td>

</tr>
<tr>
    <td class="date_arrive column-date_arrive">
      <input style="width: 50%" name="<?php echo $key ?>" type="text" />
    </td>
</tr>
    <?php }?>
    <?php
foreach ($items3 as $key => $value) {?>
    <tr>
    <td class=" has-row-actions column-primary" style="width: 20%">
      <strong>
        <?php echo $key ?>
      </strong>
    </td>

</tr>
<tr>

    <td class="date_arrive column-date_arrive">
      <select style="width: 50%" name="<?php echo $key ?>" >
<?php
foreach ($item2 as $val) {?>
   <option value="<?php echo $val["id"] ?>"> <?php echo $val["title"] ?> </option>
    <?php }?>

</select>
    </td>
</tr>
    <?php }?>
    <tr>
    <td class=" has-row-actions column-primary" style="width: 20%">
      <strong>
        Hostaway API Integration
      </strong>
    </td>
</tr>
<tr>
    <td class="date_arrive column-date_arrive">
      <p>Enter the Hostaway listing ID to enable API synchronization:</p>
      <input type="number" name="listingMapId" style="width: 50%" placeholder="Enter Hostaway listing ID (integer)" />
      <p><small>This ID is used to map this product to a listing in Hostaway for API synchronization. This is a required field for API synchronization and works independently from iCal synchronization.</small></p>
    </td>
</tr>
    <?php
foreach ($items2 as $key => $value) {?>
    <tr>
    <td class=" has-row-actions column-primary" style="width: 20%">
      <strong>
        <?php echo $key ?>
      </strong>
    </td>

</tr>
<tr>

    <td class="date_arrive column-date_arrive">
      <textarea rows="5" style="width: 100%" name="<?php echo $key ?>"></textarea>
    </td>
</tr>
    <?php }?>
  </table>
  <div style="margin-top:1rem">
	<input type="submit" name="publish" id="publish" class="button button-primary button-large" value="Publish">
  </div>

</form>
        </div>
<?php
}

  public function show_edit_page() {
    global $wpdb, $wrsModule;
    $id = $_GET["id"];
    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE . " WHERE id = " . $id;

    $item = $wpdb->get_row($sql, 'ARRAY_A');

    if ("POST" == $_SERVER['REQUEST_METHOD']) {
      $wpdb->update('wp_wrs_products',
        array(
          'title' => $_POST['Title'],
          'picture' => $_POST['Picture'],
          'capacity' => $_POST['Capacity'],
          'description' => stripslashes($_POST['Description']),
          'price_per_night' => $_POST['Price_per_night'],
          'rate_text' => stripslashes($_POST['Rate_text']),
          'location_id' => $_POST['Location'],
          'price_listing_id' => isset($_POST['enable_price_sync']) ? $_POST['listing_sync'] : null,
          'listingMapId' => isset($_POST['listingMapId']) ? $_POST['listingMapId'] : null,
        ),
        array('id' => $id)
      );
      wp_redirect("/wp-admin/admin.php?page=wodaabe-reservations-products");
    }
    $sql2 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE;

    $item2 = $wpdb->get_results($sql2, 'ARRAY_A');

    $items = array(
      'Title' => $item["title"],
      'Picture' => $item["picture"],
      'Capacity' => $item["capacity"],
      'Price per night' => $item["price_per_night"],
    );
    $items2 = array(
      'Description' => $item["description"],
      'Rate text' => $item["rate_text"],
    );
    wrs_log(print_r($items2, true));
    $items3 = array(
      'Location' => $item["location_id"],
    );
    $price_listings = $this->get_pricelabs_listings();

    ?>
<div class="wrap">
<form method="POST">
  <h2>Products</h2>
  <p>Here you can edit a product </p>
  <table class="wp-list-table widefat fixed striped table-view-excerpt reservations_page_wodaabe-reservations-reservations">


<tbody id="the-list">
<?php
foreach ($items as $key => $value) {?>
  <tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      <?php echo $key ?>
    </strong>
  </td>

</tr>
<tr>
  <td class="date_arrive column-date_arrive">
    <input value="<?php echo $value ?>" style="width: 50%" name="<?php echo $key ?>" type="text" />
  </td>
</tr>
  <?php }?>
  <?php
foreach ($items3 as $key => $value) {?>
  <tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      <?php echo $key ?>
    </strong>
  </td>

</tr>
<tr>

  <td class="date_arrive column-date_arrive">
    <select style="width: 50%" name="<?php echo $key ?>" >
<?php
foreach ($item2 as $val) {?>
 <option value="<?php echo $val["id"] ?>" <?php if ($val["id"] == $item["location_id"]) {?> selected="true" <?php }?>> <?php echo $val["title"] ?> </option>
  <?php }?>

</select>
  </td>
</tr>
  <?php }?>

  <?php
    foreach ($items3 as $key => $value) {?>
  <tr>
    <td class=" has-row-actions column-primary" style="width: 20%">
      <strong>
        Price syncing
      </strong>
    </td>
  </tr>
  <tr>
    <td class="date_arrive column-date_arrive">
     <input type="checkbox" name="enable_price_sync" onchange="document.getElementById('listing_sync').disabled = !this.checked" <?php echo !($item["price_listing_id"] === NULL) ? "checked" : ""; ?> />
      <select style="width: 100%" name="listing_sync" id="listing_sync" <?php echo ($item["price_listing_id"] === NULL) ? "disabled" : ""; ?>>
      <?php
      foreach ($price_listings as $price) {?>
      <option value="<?php echo $price["id"] ?>" <?php if ($price["id"] == $item["price_listing_id"]) {?> selected="true" <?php }?>> <?php echo $price["base"] . ' - ' . $price["name"] ?> </option>
        <?php }?>

      </select>
    </td>
  </tr>
  <tr>
    <td class=" has-row-actions column-primary" style="width: 20%">
      <strong>
        Hostaway API Integration
      </strong>
    </td>
  </tr>
  <tr>
    <td class="date_arrive column-date_arrive">
      <p>Enter the Hostaway listing ID to enable API synchronization:</p>
      <input type="number" name="listingMapId" style="width: 100%" value="<?php echo $item["listingMapId"] ?? ''; ?>" placeholder="Enter Hostaway listing ID (integer)" />
      <p><small>This ID is used to map this product to a listing in Hostaway for API synchronization. This is a required field for API synchronization and works independently from iCal synchronization.</small></p>
    </td>
  </tr>
  <?php }?>
  <?php
foreach ($items2 as $key => $value) {?>
  <tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      <?php echo $key ?>
    </strong>
  </td>

</tr>
<tr>

  <td class="date_arrive column-date_arrive">
    <textarea rows="5" style="width: 100%" name="<?php echo $key ?>"><?php echo $value ?></textarea>
  </td>
</tr>
  <?php }?>
</table>
<div style="margin-top:1rem">
        <input type="submit" name="publish" id="publish" class="button button-primary button-large" value="Publish">
</div>

</form>
      </div>
<?php
}

}

// WP_List_Table is not loaded automatically so we need to load it in our application
if (!class_exists('WP_List_Table')) {
  require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';
}

/**
 * Create a new table class that will extend the WP_List_Table
 */
class WodaabeReservationsProducts_List_Table extends WP_List_Table {

  /**
   * Returns the count of records in the database.
   *
   * @return null|string
   */
  public static function record_count() {
    global $wpdb;

    $sql = "SELECT COUNT(*) FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE;
    return $wpdb->get_var($sql);
  }

  /**
   * Retrieve customer’s data from the database
   *
   *
   * @return mixed
   */
  public function get_items($orderby = "id", $order = "desc") {
    global $wpdb, $wrsModule;

    $columns = array('id', 'titre', 'picture', 'btn_text', 'link', 'date_add', 'date_upd');
    if (!in_array($orderby, $columns)) {
      $orderby = "id";
    } else {
      $orderby = $orderby;
    }
    $orders = array('asc', 'desc');
    if (!in_array($order, $orders)) {
      $order = "desc";
    }

    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE . " ORDER BY " . $orderby . " " . $order;

    $result = $wpdb->get_results($sql, 'ARRAY_A');

    return $result;
  }

  /**
   * Prepare the items for the table to process
   *
   * @return Void
   */
  public function prepare_items() {

    $columns = $this->get_columns();
    $hidden = $this->get_hidden_columns();
    $sortable = $this->get_sortable_columns();

    $perPage = 10;
    $currentPage = $this->get_pagenum();

    // Set defaults
    $orderby = 'id';
    $order = 'desc';

    // If orderby is set, use this as the sort column
    if (!empty($_GET['orderby'])) {
      $orderby = $_GET['orderby'];
    }

    // If order is set use this as the order
    if (!empty($_GET['order'])) {
      $order = $_GET['order'];
    }

    $data = $this->get_items($orderby, $order);

    $data = is_array($data) ? $data : array();
    $totalItems = $this->record_count();

    $this->set_pagination_args(array(
      'total_items' => $totalItems,
      'per_page' => $perPage,
    ));

    $data = array_slice($data, (($currentPage - 1) * $perPage), $perPage);

    $this->_column_headers = array($columns, $hidden, $sortable);
    $this->items = $data;
  }

  /**
   * Override the parent columns method. Defines the columns to use in your listing table
   *
   * @return Array
   */
  public function get_columns() {
    $columns = array(
      'id' => 'ID',
      'title' => 'Titre',
      'picture' => 'Cover picture',
      'capacity' => 'Capacity',
      'price_per_night' => 'Cost per night',
      'location' => 'Location',
      'date_add' => 'Date de création',
      'date_upd' => 'Dernière mise à jour',
    );
    return $columns;
  }

  /**
   * Define which columns are hidden
   *
   * @return Array
   */
  public function get_hidden_columns() {
    return array();
  }

  /**
   * Define the sortable columns
   *
   * @return Array
   */
  public function get_sortable_columns() {
    return array(
      'id' => array('id', false),
      'date_add' => array('date_add', false),
      'titre' => array('titre', false),
    );
  }

  /**
   * Define what data to show on each column of the table
   *
   * @param  Array $item        Data
   * @param  String $column_name - Current column name
   *
   * @return Mixed
   */
  public function column_default($item, $column_name) {
    global $wpdb;
    switch ($column_name) {
    case 'id':
      return "<strong>#{$item[$column_name]}</strong>";
    case 'price_per_night':
      return "<strong>{$item[$column_name]} €</strong>";

    case 'picture':
      return "<a href='{$item[$column_name]}' target='_blank'>Show Image</a>";
    case 'link':
      return $item[$column_name] == "inline" ? "Start reservation process" : "Send to contact page";

    case 'date_add':
      return $item[$column_name] ? \date("d/m/Y H:i:s", \strtotime($item[$column_name])) : "<b>-</b>";
    case 'date_upd':
      return $item[$column_name] ? \date("d/m/Y H:i:s", \strtotime($item[$column_name])) : "<b>-</b>";
    case 'location':
      $sql3 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE . " WHERE id = " . $item["location_id"];

      $item3 = $wpdb->get_row($sql3, 'ARRAY_A');

      return "<strong>{$item3["title"]}</strong>";

    default:
      return $item[$column_name];
    }
  }

  public function column_title($item) {
    $path = $_SERVER['REQUEST_URI'];
    $edit_link = $path . "&action=edit&id=" . $item["id"];
    $view_link = $path . "&action=show&id=" . $item["id"];
    $delete_link = $path . "&action=delete&id=" . $item["id"];
    $output = '';

    // Title.
    $output .= '<strong><a href="' . esc_url($edit_link) . '" class="row-title">' . esc_html($item["title"]) . '</a></strong>';

    // Get actions.
    $actions = array(
      'view' => '<a href="' . esc_url($view_link) . '">' . esc_html__('View', 'my_plugin') . '</a>',
      'edit' => '<a href="' . esc_url($edit_link) . '">' . esc_html__('Edit', 'my_plugin') . '</a>',
      'delete' => '<a href="' . esc_url($delete_link) . '">' . esc_html__('Delete', 'my_plugin') . '</a>',
    );

    $row_actions = array();

    foreach ($actions as $action => $link) {
      $row_actions[] = '<span class="' . esc_attr($action) . '">' . $link . '</span>';
    }

    $output .= '<div class="row-actions visible">' . implode(' | ', $row_actions) . '</div>';

    return $output;
  }

  /**
   * Allows you to sort the data by the variables set in the $_GET
   *
   * @return Mixed
   */
  private function sort_data($a, $b) {
    // Set defaults
    $orderby = 'id';
    $order = 'desc';

    // If orderby is set, use this as the sort column
    if (!empty($_GET['orderby'])) {
      $orderby = $_GET['orderby'];
    }

    // If order is set use this as the order
    if (!empty($_GET['order'])) {
      $order = $_GET['order'];
    }

    $result = strcmp($a[$orderby], $b[$orderby]);

    if ($order === 'asc') {
      return $result;
    }

    return -$result;
  }
}
?>