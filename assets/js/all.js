(function (e, w, config) {
  'use strict';
  var $ = e,
    wrs = w.wrs || {},
    state = { ...(wrs.state || {}), config };

  wrs.state = function () {
    return state;
  };

  wrs.handleSuccess = function (res) {
    console.log(res);
    if (res && res.success) {
      if (res.data && res.data.status === 'success') {
        return res.data.data;
      }
    }
    return false;
  };

  wrs.handleError = function (res) {
    console.error(res);
    return false;
  };

  wrs.transformRequest = function (jsonData) {
    //console.log("transformRequest JsonData: ", jsonData);
    return Object.entries(jsonData)
      .map(function (x) {
        return `${encodeURIComponent(x[0])}=${encodeURIComponent(x[1])}`;
      })
      .join('&');
  };

  wrs.payload = function (wrs_action) {
    const data = typeof wrs_action === 'string' ? { wrs_action } : wrs_action;
    //console.log("payload Data: ", data);
    return wrs.transformRequest({
      ...data,
      action: 'wrs_process_ajax',
    });
  };

  wrs.ajax = function (payload, cb) {
    //console.log("ajax Payload: ", payload);
    //console.log("ajax Callback: ", cb);
    return new Promise(function (resolve) {
      $.post(config.ajaxUrl, payload)
        .done(function (res) {
          resolve(wrs.handleSuccess(res));
        })
        .fail(function (res) {
          resolve(wrs.handleError(res));
        });
    }).then(function (res) {
      if (cb) cb(res);
      return res;
    });
  };

  wrs.getLocations = function (cb) {
    return wrs.ajax(wrs.payload('get_locations'), cb);
  };

  wrs.getProducts = function (data, cb) {
    return wrs.ajax(wrs.payload({ ...data, wrs_action: 'get_products' }), cb);
  };

  // Add a new function in your 'wrs' module to get calendar events based on filters
  wrs.getCalendarEvents = function (filters, cb) {
    // Send an AJAX request to the server
    wrs.ajax(
      wrs.payload({
        wrs_action: 'update_product_reservation_calendars',
      })
    );
  };

  // Add a new function in your 'wrs' module to get calendar events based on filters
  wrs.syncExternalReservations = function (data, cb) {
    console.log('syncExternalReservations starting . . .');
    console.log('Data id: ', data.id);
    console.log('Data link: ', data.link);

    // Send an AJAX request to the server
    return wrs.ajax(
      wrs.payload({
        wrs_action: 'sync_external_reservation_calendar',
        id: data.id,
        link: data.link,
      })
    );
  };

  wrs.unsyncExternalReservations = function (data) {
    wrs.ajax(
      wrs.payload({
        wrs_action: 'unsync_external_reservation_calendar',
        id: data,
      })
    );
  };

  wrs.getPromoDetails = async function (data) {
    try {
      const response = await wrs.ajax(
        wrs.payload({
          wrs_action: 'get_promo_details',
          promocodevalue: data,
        })
      );
      return response;
    } catch (error) {
      console.error('Error fetching promo details:', error);
    }
  };

  wrs.getBlockedPeriods = async function () {
    try {
      console.log('fetching blocked_periods');
      const response = await wrs.ajax(
        wrs.payload({
          wrs_action: 'get_blocked_periods', // Action to retrieve blocked periods
        })
      );
      return response;
    } catch (error) {
      console.error('Error fetching blocked periods:', error);
    }
  };

  // Function to save Hostaway listing ID for a product
  wrs.saveHostawayListingId = async function (data) {
    try {
      console.log('Saving Hostaway listing ID');
      console.log('Product ID:', data.product_id);
      console.log('Listing Map ID:', data.listing_map_id);

      const response = await wrs.ajax(
        wrs.payload({
          wrs_action: 'save_hostaway_listing_id',
          product_id: data.product_id,
          listing_map_id: data.listing_map_id,
        })
      );
      return response;
    } catch (error) {
      console.error('Error saving Hostaway listing ID:', error);
      return false;
    }
  };

  // Test function to retrieve some data on the backend
  wrs.getData = function (data, cb) {
    console.log('Get Data starting . . .');
    //console.log("Data : " , data);

    // Send an AJAX request to the server
    const res = wrs.ajax(
      wrs.payload({
        wrs_action: 'get_data',
      })
    );

    return res;
  };

  w.wrs = wrs;
})(jQuery, window, wrsJsData);
