(function (w) {
  var wrs = w.wrs || {};

  // Function to apply the promo code
  function applyPromoCode(total, promoDetails) {
    let discountedTotal = total;
    let discountAmount = 0;

    if (promoDetails.discount_type === 'flat_amount') {
      discountAmount = parseFloat(promoDetails.value);
      discountedTotal -= discountAmount;
    } else if (promoDetails.discount_type === 'percentage') {
      discountAmount = (total * parseFloat(promoDetails.value)) / 100;
      discountedTotal -= discountAmount;
    }

    // Ensure the total doesn't go below zero
    discountedTotal = Math.max(0, discountedTotal);

    return {
      discountedTotal,
      discountAmount,
    };
  }

  function updateDiscountedTotal(promoDetails, isEligible) {
    // Convert the expiry date string to a Date object
    const expiryDate = new Date(promoDetails.date_expiry);

    // Get today's date
    const today = new Date();

    // Set the time to midnight for both dates to compare only the dates
    expiryDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    // Check if the expiry date is inferior or equal to today
    const isExpired = expiryDate <= today;

    console.log(`Is the promo code expired? ${isExpired}`);

    wrs.setStateWithoutRerender({
      promocodeIsValid: !isExpired,
    });

    if (!isExpired) {
      // Calculate initial total
      const initialTotal = wrs.state().category.prices
        ? wrs
            .state()
            .category.prices.reduce(
              (accumulator, currentValue) => accumulator + currentValue.price,
              0
            )
        : wrs.state().nbDays * wrs.state().category.price_per_night;

      // Apply promo code
      const { discountedTotal, discountAmount } = applyPromoCode(
        initialTotal,
        promoDetails
      );
      console.log('Discounted total: ', discountedTotal);

      // Set state without rerender
      wrs.setStateWithoutRerender({
        total: discountedTotal,
        promocodeIsEligible: isEligible,
        promoDetails,
        discountAmount,
      });

      console.log(
        `Discounted Total: ${discountedTotal}, Discount Amount: ${discountAmount}`
      );
      console.log(`Code is eligible: `, wrs.state().promocodeIsEligible);
    }
  }

  function updateTotal() {
    const total = wrs.state().category.prices
      ? wrs
          .state()
          .category.prices.reduce(
            (accumulator, currentValue) => accumulator + currentValue.price,
            0
          )
      : wrs.state().nbDays * wrs.state().category.price_per_night;
    wrs.setStateWithoutRerender({
      promocodeIsEligible: false,
      total,
    });
  }

  wrs.step4 = function (data) {
    const {
      category,
      departureDayWeekShort,
      departureDateFr,
      arrivalDayWeekShort,
      arrivalDateFr,
      adultCount,
      childCount,
      nbDays,
      promocodevalue,
    } = data;
    if (!category) return '';

    if (promocodevalue) {
      // Fetch promo details and process the result
      wrs
        .getPromoDetails(promocodevalue)
        .then((promoDetails) => {
          console.log('Promo detail: ', promoDetails);

          // Check if product is eligible to given code
          const isEligible = promoDetails.product_ids.includes(category.id);
          console.log(`Is the promo code eligible ? ${isEligible}`);

          if (isEligible) {
            updateDiscountedTotal(promoDetails, isEligible);
          } else {
            updateTotal();
          }
        })
        .catch((error) => {
          console.error('Error fetching promo details: ', error);
        });
    } else {
      updateTotal();
    }

    return `
    
    <div class="Stack-sc-hsu31d-0 hggCUz">
    <h1
      class="PageTitle-sc-1agnq51-0 bdmeit"
      style="font-size: 2rem; padding-top: 24px"
    >
      <span>Summary</span>
    </h1>
  </div>
  <div
    class="components__MarginBase-sc-1fprdjl-0 components__MediumMargin-sc-1fprdjl-3 eWHTIh hQUnoS"
  ></div>
  <div class="Stack-sc-hsu31d-0 fJdyDT">
    <div class="Card-sc-j4gsfs-0 ftIjDA">
      <div
        class="Stack-sc-hsu31d-0 cALERv"
        style="display: flex"
      >
        <div
          width="36%"
          data-test-id="summary-card-image"
          class="Stack__StackItem-sc-hsu31d-1 kohOFB"
          style="min-width: 36%"
        >
          <div
            aria-disabled="false"
            class="style__CarouselContainer-sc-1gvjjfx-0 geZMSr"
          >
            <div
              aria-label="Gallery"
              role="region"
              class="style__CarouselWrapperElement-sc-1gvjjfx-1 hCJQrM"
            >
              <div
                class="style__CarouselSwipeableElement-sc-1gvjjfx-7 dGYbUL"
              >
                <div
                  class="style__CarouselTransitionGroupElement-sc-1gvjjfx-2 fOBuvM"
                >
                <a
                  href="${category.picture}"
                  target="_blank"
                  class="style__CarouselImageWrapperElement-sc-1gvjjfx-3 dUDoAH"
                >
                  <img
                    class="style__ImageElement-sc-1izpjq4-0 jrVkPR style__StyledMewsImage-sc-1yepnva-0 jqNDgS"
                    src="${category.picture}"
                    alt="${category.title}"
                  />
                </a>
                </div>
              </div>
              <div
                class="style__ImageAmountWrapperElement-sc-bgcs2a-0 jhRLmg"
              >
                <span
                  class="style__IconWrapper-sc-147rnkr-0 bqXwWb notranslate"
                  data-test-icon="camera"
                  ><span
                    aria-hidden="true"
                    class="style__IconValue-sc-147rnkr-1 eHzrow"
                    >camera</span
                  ></span
                >
                <div
                  class="style__ImageAmountTextElement-sc-bgcs2a-1 uzTIA"
                >
                  1
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="Stack-sc-hsu31d-0 gBGbt"
          style="
            margin-left: 1rem;
            display: flex;
            justify-content: space-between;
            width: 100%;
          "
        >
          <div class="Stack-sc-hsu31d-0 hggCUz">
            <h2
              data-test-id="summary-card-name"
              class="SectionTitle-sc-12rw2o6-0 iuNawe"
              style="font-size: 1.43rem"
            >
              <span>${category.title}</span>
            </h2>
            <div
              data-test-id="summary-card-date"
              class="style__IconTextContainerElement-sc-1dtma5g-1 cubvrI"
              style="display: flex"
            >
              <img
                src="/wp-content/reservations/calendar2.png"
                style="max-width: 32px; margin-right: 8px"
              />
              <div class="Label-sc-ruhf0l-0 cnMcWn">
              <span>
              <span>
                <span>${arrivalDayWeekShort}</span>
                <span>${arrivalDateFr}</span> </span
              >&nbsp;‐&nbsp;
              <span>
                <span>${departureDayWeekShort}</span>
                <span>${departureDateFr}</span>
              </span>
              </span>
              </div>
            </div>
            <div
              class="style__IconTextContainerElement-sc-1dtma5g-1 cubvrI"
              style="display: flex; margin-top: 8px"
            >
              <img
                src="/wp-content/reservations/person.png"
                style="max-width: 32px; margin-right: 8px"
              />
              <div class="Stack-sc-hsu31d-0 qAjjL">
                <div class="Stack-sc-hsu31d-0 iEVyvT">
                ${
                  childCount
                    ? `
                  <div class="Label-sc-ruhf0l-0 cnMcWn">
                  Children (0-18 yrs):&nbsp;${childCount}
                </div>
                  `
                    : ''
                }
                ${
                  adultCount
                    ? `
                  <div class="Label-sc-ruhf0l-0 cnMcWn">
                  Adults:&nbsp;${adultCount}
                </div>
                  `
                    : ''
                }
                 
                </div>
              </div>
            </div>
            <div
              class="style__IconTextContainerElement-sc-1dtma5g-1 cubvrI"
              style="display: flex; margin-top: 8px"
            >
              <img
                src="/wp-content/reservations/rate2.png"
                style="max-width: 32px; margin-right: 8px"
              />
              <div>
                <div class="Label-sc-ruhf0l-0 cnMcWn">
                  <span>Rate</span>:&nbsp;<span
                    >Standard Rate</span
                  >
                </div>
              </div>
            </div>
            
            <div>
            <div
              direction="Vertical"
              class="style__ExpandableBoxElement-sc-1ykgft8-0 bUKMFb expandable"
              style="display: none"
            >
              <div
                class="style__ContentContainer-sc-lz1s5p-1 heDHSw"
              >
                <div>
                  <div
                    class="SmallParagraph-sc-19vlf3w-0 gHmYtb"
                  >
                    <p
                      data-test-id="category-detail-description"
                      class="SmallParagraph-sc-19vlf3w-0 fkWnIn"
                    >
                      <span
                        >${category.rate_text}
                      </span>
                    </p>
                  </div>
                  <div
                    class="style__GradientElement-sc-lz1s5p-2 clvleL"
                  ></div>
                </div>
              </div>
            </div>
              <div
                class="Stack-sc-hsu31d-0 bORlsy clickable pointer"
                style="display: inline-flex"
                data-process-id="onMore"
              >
                <img
                  src="/wp-content/reservations/chevron_down.png"
                  style="max-width: 32px"
                />
                <div class="Label-sc-ruhf0l-0 cnMcWn">
                  <span>More</span>
                </div>
              </div>
                <span
                class="style__ToggleContainer-sc-lz1s5p-0 kakYEL expandable"
                style="display: none"
              >
                <div
                  class="Stack-sc-hsu31d-0 bORlsy pointer clickable"
                  style="display: inline-flex"
                  data-process-id="onLess"
                >
                  <img
                    src="/wp-content/reservations/chevron_up.png"
                    style="max-width: 32px"
                  />
                  <div class="Label-sc-ruhf0l-0 cnMcWn">
                    <span>Less</span>
                  </div>
                </div>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="Stack-sc-hsu31d-0 jHoMkh">
      <div
        class="Stack-sc-hsu31d-0 irmQpm"
        style="justify-content: center"
      >
        <button
          data-process-id="onStep2"
          class="style__ButtonElement-sc-jv24-0 hngsID clickable"
          style="display: none"
        >
          <span>Choose another item</span>
        </button>
      </div>
    </div>
    <div class="Card-sc-j4gsfs-0 ftIjDA">
      <div
        class="View__TransitionViewWrapper-sc-pmvrpt-1 fRFRZt"
      >
        <div
          class="Transition__TransitionElement-sc-lqfmkq-0 GKVsD"
        >
          <div aria-live="assertive"></div>
          <div aria-busy="false">
            <div class="Stack-sc-hsu31d-0 hggCUz">
              <div
                class="Stack-sc-hsu31d-0 hggCUz"
                style="margin-bottom: 8px"
              >
                <div class="Stack-sc-hsu31d-0 btuPPv">
                  <div
                    class="Stack__StackItem-sc-hsu31d-1 ffuMMd"
                  >
                    <div
                      data-test-id="additional-info"
                      class="SmallLabel-sc-1xd6i7w-0 cNnLGm"
                    >
                      Number of nights :  <span class="number_nights">4</span>
                    </div>
                    ${
                      promocodevalue
                        ? wrs.state().promocodeIsValid
                          ? `<div
                          data-test-id="additional-info"
                          class="SmallLabel-sc-1xd6i7w-0 cNnLGm"
                          style="display: flex; justify-content: space-between;"
                        >
                          <span>Discount from your promo code (${promocodevalue}) :</span><span style="font-weight: bold;">€${
                              wrs.state().discountAmount ?? ''
                            }</span>
                        </div>`
                          : `<div
                          data-test-id="additional-info"
                          class="SmallLabel-sc-1xd6i7w-0 cNnLGm"
                          style="display: flex; justify-content: space-between;"
                        >
                          <span>Your promo code (${promocodevalue}) has already expired.</span>
                        </div>`
                        : ''
                    }
                    ${
                      promocodevalue
                        ? !wrs.state().promocodeIsEligible
                          ? `<div
                          data-test-id="additional-info"
                          class="SmallLabel-sc-1xd6i7w-0 cNnLGm"
                          style="display: flex; justify-content: space-between;"
                        >
                          <span>Your promo code (${promocodevalue}) is not applicable to ${
                              wrs.state().category.title
                            }.</span>
                        </div>`
                          : ''
                        : ''
                    }
                  </div>
                </div>
              </div>
              <div
                class="style__DividerContainer-sc-qud6in-0 ftPbiR d-none"
              >
                <div
                  class="style__DividerElement-sc-qud6in-1 krAfZo"
                ></div>
                <div
                  class="style__DividerElement-sc-qud6in-1 krAfZo"
                ></div>
              </div>
              <div
                class="Stack-sc-hsu31d-0 iEVyvT d-none"
                style="margin: 8px 0"
              >
                <div class="Stack-sc-hsu31d-0 hggCUz">
                  <div
                    class="Stack-sc-hsu31d-0 iqtvBB"
                    style="
                      display: flex;
                      justify-content: space-between;
                    "
                  >
                    <div
                      data-test-id="tax-rate"
                      class="Label-sc-ruhf0l-0 iaRBLf"
                    >
                      ALV <span>10%</span>
                    </div>
                    <div class="Label-sc-ruhf0l-0 iaRBLf">
                      €${(category.price_per_night / 10).toFixed(2)}
                    </div>
                  </div>
                </div>
                <div
                  class="SmallLabel-sc-1xd6i7w-0 jxPsaa"
                  style="
                    display: flex;
                    justify-content: flex-end;
                  "
                >
                  <span>Taxes included in price</span>
                </div>
              </div>
              <div
                class="style__DividerContainer-sc-qud6in-0 ftPbiR"
              >
                <div
                  class="style__DividerElement-sc-qud6in-1 krAfZo"
                ></div>
                <div
                  class="style__DividerElement-sc-qud6in-1 krAfZo"
                ></div>
              </div>
              <div class="Stack-sc-hsu31d-0 hggCUz">
                <div
                  class="Stack-sc-hsu31d-0 iqtvBB"
                  style="
                    display: flex;
                    justify-content: space-between;
                    margin-top: 8px;
                  "
                >
                  <strong
                    class="ModerateHighlight-sc-1az58ah-0 gqykjU"
                    ><span>Total</span></strong
                  ><strong
                    data-test-id="total-bar-total-value"
                    class="ModerateHighlight-sc-1az58ah-0 gqykjU"
                    ><span><span>€${wrs.state().total}</span></span></strong
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="Stack-sc-hsu31d-0 iqBJJL"
      style="justify-content: end; display: flex"
    >
      <button
        data-process-id="onStep5"
        class="style__ButtonElement-sc-jv24-0 iliXYr clickable"
      >
        <span>Continue</span>
      </button>
    </div>
  </div>
  <div
    class="components__MarginBase-sc-1fprdjl-0 components__LargeMargin-sc-1fprdjl-4 eWHTIh kElSVh"
  ></div>
    `;
  };
  wrs.step3 = function (data) {
    const {
      category,
      departureDayWeekShort,
      departureDateFr,
      arrivalDayWeekShort,
      arrivalDateFr,
      adultCount,
      childCount,
    } = data;
    const capacity = Number(category ? category.capacity || '0' : '0');
    const total = adultCount + childCount;
    const isFull = total >= capacity;

    if (!category) return '';
    return `
      <div class="Stack-sc-hsu31d-0 hggCUz">
      <h1
        data-test-id="select-rate-heading"
        class="PageTitle-sc-1agnq51-0 bdmeit"
        style="font-size: 2rem; padding-top: 24px"
      >
        <span>Select rate</span>
      </h1>
      <div class="Stack-sc-hsu31d-0 irmQpm">
        <button
          data-process-id="onStep1"
          class="style__ButtonElement-sc-jv24-0 hngsID clickable"
        >
          <span
            class="style__IconWrapper-sc-147rnkr-0 boqJiL notranslate"
            data-test-icon="calendar"
          >
            <span
              aria-hidden="true"
              class="style__IconValue-sc-147rnkr-1 eHzrow"
              >calendar</span
            >
          </span>
          <span>
          <span>
            <span>${arrivalDayWeekShort}</span>
            <span>${arrivalDateFr}</span> </span
          >&nbsp;‐&nbsp;
          <span>
            <span>${departureDayWeekShort}</span>
            <span>${departureDateFr}</span>
          </span>
          </span>
        </button>
      </div>
    </div>
    <div
      class="components__MarginBase-sc-1fprdjl-0 components__LargeMargin-sc-1fprdjl-4 eWHTIh kElSVh"
    ></div>
    <div class="Stack-sc-hsu31d-0 fJdyDT">
      <div
        data-test-id="category-detail-card"
        class="Card-sc-j4gsfs-0 ftIjDA"
      >
        <div
          class="Stack-sc-hsu31d-0 kWxlsS"
          style="
            display: flex;
            flex-direction: row;
            justify-content: space-between;
          "
        >
          <div
            style="min-width: 45%"
            class="Stack__StackItem-sc-hsu31d-1 ASAqh"
          >
            <div
              aria-disabled="false"
              class="style__CarouselContainer-sc-1gvjjfx-0 geZMSr"
            >
              <div
                aria-label="Gallery"
                role="region"
                class="style__CarouselWrapperElement-sc-1gvjjfx-1 hCJQrM"
              >
                <div
                  class="style__CarouselSwipeableElement-sc-1gvjjfx-7 dGYbUL"
                >
                  <div
                    class="style__CarouselTransitionGroupElement-sc-1gvjjfx-2 fOBuvM"
                  >
                    <a
                      href="${category.picture}"
                      target="_blank"
                      class="style__CarouselImageWrapperElement-sc-1gvjjfx-3 dUDoAH"
                    >
                      <img
                        class="style__ImageElement-sc-1izpjq4-0 jrVkPR style__StyledMewsImage-sc-1yepnva-0 jqNDgS"
                        src="${category.picture}"
                        alt="${category.title}"
                      />
                    </a>
                  </div>
                </div>
                <div
                  class="style__ImageAmountWrapperElement-sc-bgcs2a-0 jhRLmg"
                >
                  <span
                    class="style__IconWrapper-sc-147rnkr-0 bqXwWb notranslate"
                    data-test-icon="camera"
                  >
                    <span
                      aria-hidden="true"
                      class="style__IconValue-sc-147rnkr-1 eHzrow"
                      >camera</span
                    >
                  </span>
                  <div
                    class="style__ImageAmountTextElement-sc-bgcs2a-1 uzTIA"
                  >
                    1
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="Stack-sc-hsu31d-0 jStchL"
            style="padding-left: 1rem"
          >
            <h3 class="SubsectionTitle-sc-kn5y6d-0 dXepgu">
              <span>${category.title}</span>
            </h3>
            <div class="Stack-sc-hsu31d-0 jStchL">
              <div class="Stack-sc-hsu31d-0 dzeJtU">
                <div
                  data-test-id="category-detail-max-persons"
                  class="style__IconTextContainerElement-sc-1dtma5g-1 cubvrI"
                  style="display: flex"
                >
                  <img
                    src="/wp-content/reservations/person.png"
                    style="max-width: 32px"
                  />
                  <div>
                    <span>Maximum persons</span>:&nbsp;${category.capacity}
                  </div>
                </div>
              </div>
              <div>
                <div
                  direction="Vertical"
                  class="style__ExpandableBoxElement-sc-1ykgft8-0 bUKMFb expandable-short"
                >
                  <div
                    class="style__ContentContainer-sc-lz1s5p-1 heDHSw"
                  >
                    <div>
                      <div
                        class="SmallParagraph-sc-19vlf3w-0 gHmYtb"
                      >
                        <p
                          data-test-id="category-detail-description"
                          class="SmallParagraph-sc-19vlf3w-0 fkWnIn"
                        >
                          <span
                            >${category.description.substring(0, 100)}</span
                          >
                        </p>
                      </div>
                      <div
                        class="style__GradientElement-sc-lz1s5p-2 clvleL"
                      ></div>
                    </div>
                  </div>
                </div>
                <div
                  direction="Vertical"
                  class="style__ExpandableBoxElement-sc-1ykgft8-0 bUKMFb expandable"
                  style="display: none"
                >
                  <div
                    class="style__ContentContainer-sc-lz1s5p-1 heDHSw"
                  >
                    <div>
                      <div
                        class="SmallParagraph-sc-19vlf3w-0 gHmYtb"
                      >
                        <p
                          data-test-id="category-detail-description"
                          class="SmallParagraph-sc-19vlf3w-0 fkWnIn"
                        >
                          <span
                            >${category.description}
                          </span>
                        </p>
                      </div>
                      <div
                        class="style__GradientElement-sc-lz1s5p-2 clvleL"
                      ></div>
                    </div>
                  </div>
                </div>
                <span
                  class="style__ToggleContainer-sc-lz1s5p-0 kakYEL"
                >
                  <div
                    class="Stack-sc-hsu31d-0 bORlsy pointer clickable"
                    style="display: inline-flex"
                    data-process-id="onMore"
                  >
                    <img
                      src="/wp-content/reservations/chevron_down.png"
                      style="max-width: 32px"
                    />
                    <div class="Label-sc-ruhf0l-0 cnMcWn">
                      <span>More</span>
                    </div>
                  </div>
                </span>
                <span
                  class="style__ToggleContainer-sc-lz1s5p-0 kakYEL expandable"
                  style="display: none"
                >
                  <div
                    class="Stack-sc-hsu31d-0 bORlsy pointer clickable"
                    style="display: inline-flex"
                    data-process-id="onLess"
                  >
                    <img
                      src="/wp-content/reservations/chevron_up.png"
                      style="max-width: 32px"
                    />
                    <div class="Label-sc-ruhf0l-0 cnMcWn">
                      <span>Less</span>
                    </div>
                  </div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="style__BaseLayout-sc-17dphdl-3 TwoColumnsLayout-sc-16awrfa-0 kjGUHG lgmnzy fullsection"
        style="margin-top: 1rem"
      >
        <div class="style__Column-sc-17dphdl-0 jsIfmS ecranmax1">
          <div data-test-id="occupancy-container"> 
            <h2 class="SectionTitle-sc-12rw2o6-0 eHUYcd">
              <span>Occupancy</span>
            </h2>
            <div
              class="components__MarginBase-sc-1fprdjl-0 components__MediumMargin-sc-1fprdjl-3 eWHTIh hQUnoS"
            ></div>
            <div class="Card-sc-j4gsfs-0 ftIjDA">
              <div class="Stack-sc-hsu31d-0 kkqFwN">
                <div
                  data-test-id="occupancy-rooms-selector"
                  class="Stack__StackItem-sc-hsu31d-1 ffuMMd"
                >
                  <div
                    class="style__FieldContainer-sc-1nv04ex-0 ckIMMf"
                  >
                    <div
                      class="style__FieldHeader-sc-1nv04ex-1 dCdeYC"
                    >
                      <label
                        for="ApartmentPlural"
                        class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"
                      >
                        <span>Apartments</span>
                      </label>
                      <div
                        class="Stack-sc-hsu31d-0 kzckpq"
                      ></div>
                    </div>
                    <div
                      class="style__InputWrapper-sc-1nv04ex-3 eaagmD"
                    >
                      <div
                        class="style__ContainerElement-sc-6ou9ca-0 dHomJu"
                      >
                        <span
                          style="cursor: pointer"
                          tabindex="-1"
                          class="style__BareIconButtonElement-sc-1vm303b-0 YVhge"
                        >
                          <img
                            src="/wp-content/reservations/minus_inactive.png"
                            style="max-width: 32px"
                          />
                        </span>
                        <div
                          class="style__FlexInputContainer-sc-6ou9ca-1 jyRZtS"
                        >
                          <div
                            class="style__InputContainer-sc-1hz6a59-0 gqVxEp"
                          >
                            <div
                              class="style__InputWrapper-sc-1hz6a59-3 kYwDEo"
                            >
                              <input
                                class="style__InputElement-sc-1hz6a59-1 iDOcKT"
                                value="1"
                                readonly="true"
                              />
                            </div>
                          </div>
                        </div>
                        <span
                          tabindex="-1"
                          class="style__BareIconButtonElement-sc-1vm303b-0 YVhge"
                        >
                          <img
                            src="/wp-content/reservations/plus_inactive.png"
                            style="max-width: 32px"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="Stack-sc-hsu31d-0 hggCUz">
                  <div class="Stack-sc-hsu31d-0 iEVyvT">
                    <h3
                      class="SubsectionTitle-sc-kn5y6d-0 dXepgu"
                    >
                      <span>Apartment</span> :
                    </h3>
                    <div class="Stack-sc-hsu31d-0 eRUhcZ">
                      <div
                        width="160px"
                        data-test-id="age-category-occupancy-field"
                        class="Stack__StackItem-sc-hsu31d-1 cbnkcJ"
                      >
                        <div
                          class="style__FieldContainer-sc-1nv04ex-0 ckIMMf"
                        >
                          <div
                            class="style__FieldHeader-sc-1nv04ex-1 dCdeYC"
                          >
                            <label
                              for="feec3149-440f-4be6-bbf7-ade400cf524b"
                              class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"
                              >Adults</label
                            >
                            <div
                              class="Stack-sc-hsu31d-0 kzckpq"
                            ></div>
                          </div>
                          <div
                            class="style__InputWrapper-sc-1nv04ex-3 eaagmD"
                          >
                            <div
                              class="style__ContainerElement-sc-6ou9ca-0 dHomJu"
                            >
                              <span
                                style="cursor: pointer"
                                tabindex="-1"
                                class="style__BareIconButtonElement-sc-1vm303b-0 YVhge ${
                                  adultCount > 0 ? 'clickable' : ''
                                } "
                                data-process-id="onMinusAdult"
                              >
                                <img
                                  src="/wp-content/reservations/minus_${
                                    adultCount > 0 ? 'active' : 'inactive'
                                  }.png"
                                  style="max-width: 32px"
                                />
                              </span>
                              <div
                                class="style__FlexInputContainer-sc-6ou9ca-1 jyRZtS"
                              >
                                <div
                                  class="style__InputContainer-sc-1hz6a59-0 gqVxEp"
                                >
                                  <div
                                    class="style__InputWrapper-sc-1hz6a59-3 kYwDEo"
                                  >
                                    <input
                                      class="style__InputElement-sc-1hz6a59-1 iDOcKT"
                                      value="${adultCount}"
                                    />
                                  </div>
                                </div>
                              </div>
                              <span
                                style="cursor: pointer"
                                tabindex="-1"
                                class="style__BareIconButtonElement-sc-1vm303b-0 YVhge ${
                                  !isFull ? 'clickable' : ''
                                }"
                                data-process-id="onPlusAdult"
                              >
                                <img
                                  src="/wp-content/reservations/plus_${
                                    !isFull ? 'active' : 'inactive'
                                  }.png"
                                  style="max-width: 32px"
                                />
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        width="160px"
                        data-test-id="age-category-occupancy-field"
                        class="Stack__StackItem-sc-hsu31d-1 cbnkcJ"
                      >
                        <div
                          class="style__FieldContainer-sc-1nv04ex-0 ckIMMf"
                        >
                          <div
                            class="style__FieldHeader-sc-1nv04ex-1 dCdeYC"
                          >
                            <label
                              for="a1dcc2a0-fd09-4e5a-8eb4-ade400cf524b"
                              class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"
                            >
                              <span
                                >Children (0-18 yrs)</span
                              >
                            </label>
                            <div
                              class="Stack-sc-hsu31d-0 kzckpq"
                            ></div>
                          </div>
                          <div
                            class="style__InputWrapper-sc-1nv04ex-3 eaagmD"
                          >
                            <div
                              class="style__ContainerElement-sc-6ou9ca-0 dHomJu"
                            >
                              <span
                                style="cursor: pointer"
                                tabindex="-1"
                                class="style__BareIconButtonElement-sc-1vm303b-0 YVhge ${
                                  childCount > 0 ? 'clickable' : ''
                                } "
                                data-process-id="onMinusChild"
                              >
                                <img
                                  src="/wp-content/reservations/minus_${
                                    childCount > 0 ? 'active' : 'inactive'
                                  }.png"
                                  style="max-width: 32px"
                                />
                              </span>
                              <div
                                class="style__FlexInputContainer-sc-6ou9ca-1 jyRZtS"
                              >                                                                                                                              
                                <div
                                  class="style__InputContainer-sc-1hz6a59-0 gqVxEp"
                                >
                                  <div
                                    class="style__InputWrapper-sc-1hz6a59-3 kYwDEo"
                                  >
                                    <input
                                      class="style__InputElement-sc-1hz6a59-1 iDOcKT"
                                      value="${childCount}"
                                    />
                                  </div>
                                </div>
                              </div>
                              <span
                                style="cursor: pointer"
                                tabindex="-1"
                                class="style__BareIconButtonElement-sc-1vm303b-0 YVhge ${
                                  !isFull ? 'clickable' : ''
                                }"
                                data-process-id="onPlusChild"
                              >
                                <img
                                  src="/wp-content/reservations/plus_${
                                    !isFull ? 'active' : 'inactive'
                                  }.png"
                                  style="max-width: 32px"
                                />
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="style__Column-sc-17dphdl-0 jsIfmS"
          style="margin-left: 1rem; display: ${total > 0 ? 'block' : 'none'}"
        >
          <div class="View__ViewWrapper-sc-pmvrpt-2 kevpdN">
            <div
              class="Transition__TransitionElement-sc-lqfmkq-0 gUsybP"
            >
              <div
                aria-live="assertive"
                class="View-sc-pmvrpt-0 jjAKSs"
              ></div>
              <div
                aria-busy="false"
                class="View-sc-pmvrpt-0 jjAKSs"
              >
                <div
                  data-test-id="rates-container"
                  class="Stack-sc-hsu31d-0 jStchL margemobile"
                  style="margin-left: 1rem"
                >
                  <div>
                    <h2
                      data-test-id="rates-heading"
                      class="SectionTitle-sc-12rw2o6-0 eHUYcd margemobile"
                    >
                      <span>Rates</span>
                    </h2>
                    <div
                      class="components__MarginBase-sc-1fprdjl-0 components__MediumMargin-sc-1fprdjl-3 eWHTIh hQUnoS"
                    ></div>
                    <div class="Card-sc-j4gsfs-0 ftIjDA formwidth">
                      <ul class="Stack-sc-hsu31d-0 jStchL">
                        <li
                          data-test-id="rate-item"
                          class="Stack-sc-hsu31d-0 jStchL"
                        >
                          <div>
                            <h3
                              data-test-id="rate-item-name"
                              class="SubsectionTitle-sc-kn5y6d-0 dXepgu"
                            >
                              <span>Standard Rate</span>
                            </h3>
                            <div>
                <div
                  direction="Vertical"
                  class="style__ExpandableBoxElement-sc-1ykgft8-0 bUKMFb expandable-short"
                >
                  <div
                    class="style__ContentContainer-sc-lz1s5p-1 heDHSw"
                  >
                    <div>
                      <div
                        class="SmallParagraph-sc-19vlf3w-0 gHmYtb"
                      >
                        <p
                          data-test-id="category-detail-description"
                          class="SmallParagraph-sc-19vlf3w-0 fkWnIn"
                        >
                          <span
                            >${category.rate_text.substring(0, 100)}</span
                          >
                          <span>
                            ${
                              category.prices
                                ? `${category.prices[0].date} : €${category.prices[0].price}`
                                : ''
                            }
                          </span>
                        </p>
                      </div>
                      <div
                        class="style__GradientElement-sc-lz1s5p-2 clvleL"
                      ></div>
                    </div>
                  </div>
                </div>
                <div
                  direction="Vertical"
                  class="style__ExpandableBoxElement-sc-1ykgft8-0 bUKMFb expandable"
                  style="display: none"
                >
                  <div
                    class="style__ContentContainer-sc-lz1s5p-1 heDHSw"
                  >
                    <div>
                      <div
                        class="SmallParagraph-sc-19vlf3w-0 gHmYtb"
                      >
                        <p
                          data-test-id="category-detail-description"
                          class="SmallParagraph-sc-19vlf3w-0 fkWnIn"
                        >
                          <span
                            >${category.rate_text}
                          </span>
                          <ul>
                            ${
                              category.prices
                                ? category.prices
                                    .map((price, index) => {
                                      return `<li>${price.date} : €${price.price}</li>`;
                                    })
                                    .join('')
                                : '€' + wrs.state().category.price_per_night
                            }
                        </ul>
                        </p>
                      </div>
                      <div
                        class="style__GradientElement-sc-lz1s5p-2 clvleL"
                      ></div>
                    </div>
                  </div>
                </div>
                <span
                  class="style__ToggleContainer-sc-lz1s5p-0 kakYEL"
                >
                  <div
                    class="Stack-sc-hsu31d-0 bORlsy pointer clickable"
                    style="display: inline-flex"
                    data-process-id="onMore"
                  >
                    <img
                      src="/wp-content/reservations/chevron_down.png"
                      style="max-width: 32px"
                    />
                    <div class="Label-sc-ruhf0l-0 cnMcWn">
                      <span>More</span>
                    </div>
                  </div>
                </span>
                <span
                  class="style__ToggleContainer-sc-lz1s5p-0 kakYEL expandable"
                  style="display: none"
                >
                  <div
                    class="Stack-sc-hsu31d-0 bORlsy pointer clickable"
                    style="display: inline-flex"
                    data-process-id="onLess"
                  >
                    <img
                      src="/wp-content/reservations/chevron_up.png"
                      style="max-width: 32px"
                    />
                    <div class="Label-sc-ruhf0l-0 cnMcWn">
                      <span>Less</span>
                    </div>
                  </div>
                </span>
              </div>
                          </div>
                          <div
                            class="Stack-sc-hsu31d-0 jfxUwO"
                            style="
                              display: flex;
                              justify-content: space-between;
                            "
                          >
                            <div
                              class="Stack__StackItem-sc-hsu31d-1 hEwxKL"
                            >
                              <div
                                data-test-id="from-price-wrapper"
                                class="Stack-sc-hsu31d-0 czSrlE"
                                style="
                                  display: flex;
                                  justify-content: space-between;
                                  align-items: end;
                                "
                              >
                                <button
                                  id= "Book-now"
                                  data-process-id="onStep4"
                                  class="style__ButtonElement-sc-jv24-0 iliXYr clickable"
                                 
                                >
                                  <span id= "Book-now-name" >Book</span>
                                </button>
                              </div>
                            </div>
                            
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div
                  class="components__MarginBase-sc-1fprdjl-0 components__LargeMargin-sc-1fprdjl-4 eWHTIh kElSVh"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="components__MarginBase-sc-1fprdjl-0 components__LargeMargin-sc-1fprdjl-4 eWHTIh kElSVh"
    ></div>
    `;
  };
  wrs.promocode = function (data) {
    if (typeof data.promocodevalue === 'undefined') return '';
    return `
        <div>
          <div
            direction="Vertical"
            class="style__ExpandableBoxElement-sc-1ykgft8-0 jLXXcJ"
            style="height: auto"
          >
            <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
              <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                <label
                  for="promocodevalue"
                  class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"
                  ><span>Promotional code</span></label
                >
                <div class="Stack-sc-hsu31d-0 kzckpq"></div>
              </div>
              <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                  <div class="style__InputWrapper-sc-1hz6a59-2 hYznmx">
                    <input
                      name="promocodevalue"
                      id="promocodevalue"
                      aria-invalid="false"
                      class="style__InputElement-sc-1hz6a59-1 iDOcKT changeable"
                      value="${data.promocodevalue}"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="display: flex; justify-content: flex-end">
            <span
              data-process-id="removepromo"
              class="clickable"
              style="font-size: 11px; color: red; cursor: pointer"
              >Cancel promotion code</span
            >
          </div>
        </div>
      `;
  };
  wrs.promocodebtn = function (data) {
    if (typeof data.promocodevalue !== 'undefined') return '';
    return `
        <div>
          <button
            data-process-id="addpromo"
            class="style__ButtonElement-sc-jv24-0 cizVkM clickable"
          >
            <span>Add promotional code</span>
          </button>
        </div>
      `;
  };

  wrs.dateselection = function (data) {
    const { name, label, value, margin, btn } = data;
    const tpl = `<div
              style="
                display: flex;
                justify-content: flex-end;
                margin-top: 8px;
              "
            >
              <button
              <button
data-process-id="hidedateselection"
class="style__ButtonElement-sc-jv24-0 kRwtGQ clickable"
><span>Validate selections</span>

</button>
            </div>`;
    return `
        <div>
          <div style="margin-top: ${margin}">
            <div
              direction="Vertical"
              class="style__ExpandableBoxElement-sc-1ykgft8-0 jLXXcJ"
              style="height: auto"
            >
              <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                  <label
                    for="${name}"
                    class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"
                    ><span>${label}</span></label
                  >
                </div>
                <div
                  class="style__InputWrapper-sc-1nv04ex-3 eaagmD"
                  style="border: none"
                >
                  <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                    <div
                      class="style__InputWrapper-sc-1hz6a59-2 hYznmx"
                      style="width: 100%"
                    >
                      <input
                        name="${name}"
                        id="${name}"
                        class="style__InputElement-sc-1hz6a59-1 iDOcKT changeable"
                        value="${value}"
                        type="date"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            ${btn ? tpl : ''}
          </div>
        </div>
      `;
  };

  wrs.dateselections = function (data) {
    const { arrivalDate, departureDate } = data;
    return `
        <div>
          ${wrs.dateselection({
            name: 'arrivalDate',
            label: 'Arrival date',
            value: arrivalDate,
          })}
          ${wrs.dateselection({
            name: 'departureDate',
            label: 'Departure date',
            value: departureDate,
            margin: '16px',
            btn: true,
          })}
        </div>
      `;
  };
  wrs.dateselected = function (data) {
    const {
      arrivalDay,
      arrivalMonth,
      arrivalDayWeek,
      departureDay,
      departureMonth,
      departureDayWeek,
    } = data;
    return `<div
          class="style__DateRangeWrapperElement-sc-1ffrbli-3 fIMCWj"
        >
          <div
            class="Stack-sc-hsu31d-0 style__DateRangeOpenedWrapper-sc-1ffrbli-1 eyUgGM jZsWXm"
          >
            <div
              class="style__ArrowIconWrapperElement-sc-1ffrbli-0 dLFyGP"
            >
              <svg
                viewBox="0 0 38 11"
                version="1.1"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                aria-hidden="true"
                class="style__SvgElement-sc-upojf2-1 iQzZyX"
              >
                <polygon
                  points="34.2554173 4.47867622 31.0195602 1.23476808 32.2534711 0 37.6034307 5.35367622 32.2534711 10.7073524 31.0195602 9.47258435 34.2554173 6.22867622 0 6.22867622 0 4.47867622"
                  transform="translate(-135.000000, -30.000000) translate(135.000000, 30.000000)"
                  fill-rule="nonzero"
                  class="style__PolygonElement-sc-upojf2-0 bmLGcy"
                ></polygon>
              </svg>
            </div>
            <div>
              <div
                class="style__DateRangeTopLabelElement-sc-1ffrbli-2 jvnuuH"
              >
                <span>Arrival</span>
              </div>
              <button
                type="button"
                aria-expanded="false"
                aria-haspopup="true"
                data-process-id="editdateselection"
                class="style__DateRangeButton-sc-1ffrbli-6 gkAxwe clickable"
              >
                <div
                  class="style__DateBlockElement-sc-1f3293j-0 juDRzz"
                >
                  <div
                    class="style__DateNumberElement-sc-1f3293j-1 ebrBxM"
                  >
                    ${arrivalDay}
                  </div>
                  <div
                    class="style__MonthNameElement-sc-1f3293j-2 dBNnar"
                  >
                  ${arrivalMonth}
                  </div>
                  <div
                    class="style__WeekdayElement-sc-1f3293j-3 jvSZiY"
                  >
                  ${arrivalDayWeek}
                  </div>
                </div>
              </button>
            </div>
            <div>
              <div
                class="style__DateRangeTopLabelElement-sc-1ffrbli-2 gnWpjP"
              >
                <span>Departure</span>
              </div>
              <button
                type="button"
                data-process-id="editdateselection"
                aria-expanded="false"
                aria-haspopup="true"
                class="style__DateRangeButton-sc-1ffrbli-6 hlGmvd clickable"
              >
                <div
                  class="style__DateBlockElement-sc-1f3293j-0 juDRzz"
                >
                  <div
                    class="style__DateNumberElement-sc-1f3293j-1 ebrBxM"
                  >
                  ${departureDay}
                  </div>
                  <div
                    class="style__MonthNameElement-sc-1f3293j-2 dBNnar"
                  >
                  ${departureMonth}
                  </div>
                  <div
                    class="style__WeekdayElement-sc-1f3293j-3 jvSZiY"
                  >
                  ${departureDayWeek}
                  </div>
                </div>
              </button>
            </div>
          </div>
        </div>
      `;
  };
  wrs.step1 = function (data) {
    const { step1BgImage, dateselection, step } = data;
    const dates = dateselection
      ? wrs.dateselections(data)
      : wrs.dateselected(data);
    /**/
    return `
     <div
          class="style__BackgroundImageElement-sc-m1iwxr-0 kmjYkF PositionContainer-sc-p1cnsx-0 fFoQxz"
          style="background-image: url(${step1BgImage});"
        >
        </div>
        <div
          role="region"
          class="ContentContainer-sc-xp7hsm-0 bWdVGU"
        >
          <div class="Stack-sc-hsu31d-0 hnVevJ">
            <div
              width="100%"
              class="PositionContainer-sc-p1cnsx-0 kknGbm"
            >
              <div class="Card-sc-j4gsfs-0 ftIjDA">
                <div class="Stack-sc-hsu31d-0 jStchL">
                  ${dates}
                  ${wrs.promocode(data)}
                  ${wrs.promocodebtn(data)}
                  <button
                    id="btnNext1"
                    data-process-id="onstep2"
                    class="style__ButtonElement-sc-jv24-0 kRwtGQ clickable"
                  >
                    <span>Next</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      `;
  };
  wrs.navigationItem = function (data, i, active = false) {
    const { img, label } = data;
    const state = wrs.state();
    return `
        <li>
          <div
            aria-current="step"
            class="style__StepElement-sc-1sy1og7-5 kigjlB ${
              active && !state.finish ? ' pointer clickable' : ''
            }"
            ${active && !state.finish ? 'data-process-id=onStep' + i : ''}
          >
            <img
              src="${img}"
              style="max-width: 36px"
            />
            <div class="style__StepTextWrapper-sc-1sy1og7-6 eQcwDJ">
              <div
                class="style__StepPrimaryTextElement-sc-1sy1og7-7 fbvjyv"
              >
                <span>${label}</span>
              </div>
              <div
                class="style__StepSecondaryTextElement-sc-1sy1og7-8 kVIJdD"
              ></div>
            </div>
          </div>
        </li>
        
      `;
  };
  wrs.navigation = function (data) {
    const { menus, step } = data;
    var s = Number(step.split('step')[1]) - 1;

    return `
      <ol class="style__StepBarContainer-sc-1sy1og7-0 hnbCHS">
        ${menus
          .map(function (m, i) {
            return wrs.navigationItem(m, i + 1, i < s);
          })
          .join(
            `<li aria-hidden="true" class="style__StepLineElement-sc-1sy1og7-11 hcbNEz"></li>`
          )}
        </ol>
      `;
  };
  w.wrs = wrs;
})(window);

(function (w) {
  var wrs = w.wrs || {};

  wrs.categoryItem = function (data) {
    console.log('Category item: ', data);
    const { picture, capacity, description, price_per_night, id, title } = data;
    /*
    let prices = wrs.state().categoryItems.filter(item => item.id === id)[0].prices;
    let min_price = await wrs.state().categoryItems.filter(item => item.id === id)[0].prices.reduce((min, item) => item.price < min.price ? item : min);
    while (!data.prices) {
        await new Promise(r => setTimeout(r, 2000));
    }
    console.log("Prices : ", data.prices);
    let min_price = categoryElement;
    */
    return `
        <li class="style__Column-sc-17dphdl-0 jsIfmS ecranmax">
          <div
            data-test-id="category-card"
            class="Card-sc-j4gsfs-0 ftIjDA "
          >
            <div class="Stack-sc-hsu31d-0 hggCUz">
              <div
                aria-disabled="false"
                class="style__CarouselContainer-sc-1gvjjfx-0 geZMSr"
              >
                <divApartment
                  aria-label="Gallery"
                  role="region"
                  class="style__CarouselWrapperElement-sc-1gvjjfx-1 hCJQrM"
                >
                  <div
                    class="style__CarouselSwipeableElement-sc-1gvjjfx-7 dGYbUL"
                  >
                    <div
                      class="style__CarouselTransitionGroupElement-sc-1gvjjfx-2 fOBuvM"
                    >
                      <a
                        href="${picture}"
                        target="_blank"
                        class="style__CarouselImageWrapperElement-sc-1gvjjfx-3 dUDoAH"
                      >
                        <img
                          sizes="(max-width: 960px) 100vw, (max-width: 1250px) 50vw, (max-width: 1600px) 40vw, 30vw"
                          class="style__ImageElement-sc-1izpjq4-0 jrVkPR style__StyledMewsImage-sc-1yepnva-0 jqNDgS"
                          src="${picture}"
                          alt="Space image 1 Of 1, View all photos"
                        />
                      </a>
                    </div>
                  </div>
                  <div
                    class="style__ImageAmountWrapperElement-sc-bgcs2a-0 jhRLmg"
                  >
                    <span
                      class="style__IconWrapper-sc-147rnkr-0 bqXwWb notranslate"
                      data-test-icon="camera"
                    >
                      <span
                        aria-hidden="true"
                        class="style__IconValue-sc-147rnkr-1 eHzrow"
                        >camera</span
                      >
                    </span>
                    <div
                      class="style__ImageAmountTextElement-sc-bgcs2a-1 uzTIA"
                    >
                      1
                    </div>
                  </div>
                </div>
              </div>
              <div
                aria-disabled="false"
                class="style__DisabledComponent-sc-rsg0en-0 coBmFE"
              >
                <div class="Stack-sc-hsu31d-0 jStchL">
                  <h3
                    data-test-id="category-card-name"
                    class="SubsectionTitle-sc-kn5y6d-0 gOyjfN"
                  >
                    <span>${title}</span>
                  </h3>
                  <div class="Stack-sc-hsu31d-0 DlGli">
                    <div
                      class="style__IconTextContainerElement-sc-1dtma5g-1 cubvrI"
                      style="display: flex"
                    >
                      <img
                        src="/wp-content/reservations/person.png"
                        style="max-width: 32px"
                      />
                      <div>
                        <span>Maximum persons</span>:&nbsp;${capacity}
                      </div>
                    </div>
                  </div>
                  <div>
                    <div
                      direction="Vertical"
                      class="style__ExpandableBoxElement-sc-1ykgft8-0 jLXXcJ expandable"
                      style="display:none"
                    >
                      <div
                        class="style__ContentContainer-sc-lz1s5p-1 heDHSw"
                      >
                        <div>
                          <div
                            class="SmallParagraph-sc-19vlf3w-0 gHmYtb"
                          >
                            <p
                              class="SmallParagraph-sc-19vlf3w-0 fkWnIn"
                            >
                              <span
                                >${description}</span
                              >
                            </p>
                          </div>
                          <div
                            class="style__GradientElement-sc-lz1s5p-2 buxESy"
                          ></div>
                        </div>
                      </div>
                    </div>
                    <span
                      data-test-toggle="true"
                      class="style__ToggleContainer-sc-lz1s5p-0 kakYEL pointer clickable"
                      data-process-id="onMore"
                    >
                      <div
                        class="Stack-sc-hsu31d-0 bORlsy"
                        style="display: inline-flex"
                      >
                        <img
                          src="/wp-content/reservations/chevron_down.png"
                          style="max-width: 32px"
                        />
                        <div class="Label-sc-ruhf0l-0 cnMcWn">
                          <span>More</span>
                        </div>
                      </div>
                    </span>
                    <span
                      class="style__ToggleContainer-sc-lz1s5p-0 kakYEL expandable"
                      style="display: none"
                    >
                      <div
                        class="Stack-sc-hsu31d-0 bORlsy pointer clickable"
                        style="display: inline-flex"
                        data-process-id="onLess"
                      >
                        <img
                          src="/wp-content/reservations/chevron_up.png"
                          style="max-width: 32px"
                        />
                        <div class="Label-sc-ruhf0l-0 cnMcWn">
                          <span>Less</span>
                        </div>
                      </div>
                    </span>
                  </div>
                  <div
                    class="Stack-sc-hsu31d-0 jfxUwO"
                    style="
                      display: flex;
                      justify-content: space-between;
                    "
                  >
                    <div
                      class="Stack__StackItem-sc-hsu31d-1 hEwxKL"
                    >
                      <div
                        data-test-id="from-price-wrapper"
                        class="Stack-sc-hsu31d-0 czSrlE"
                        style="display: flex"
                      >
                        <div>
                          <div
                            class="Label-sc-ruhf0l-0 iaRBLf"
                          >
                            <span>From</span>
                          </div>
                          <strong
                            data-test-id="from-price-value"
                            class="MajorHighlight-sc-6xr78h-0 ikqrFJ"
                          >
                            <span>€${price_per_night}</span>
                            <span>&nbsp;</span>
                          </strong>
                        </div>
                        <div class="Label-sc-ruhf0l-0 fuOsjJ">
                          <span id = "nifhtly4  nifhtly100" >nightly</span>
                        </div>
                      </div>
                    </div>
                    <button
                      id = "show-rates" 
                      data-process-id="onStep3"
                      data-id="${id}"
                      class="style__ButtonElement-sc-jv24-0 iliXYr clickable"
                    >
                      <span id ="show-rates-nom">rates</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>
      `;
  };

  wrs.step2 = function (data) {
    console.log('step2 data:', data);
    const {
      categoryItems,
      departureDayWeekShort,
      departureDateFr,
      arrivalDayWeekShort,
      arrivalDateFr,
      categoryItemsLoading,
    } = data;
    console.log('wrs.step2 categoryItems: ', categoryItems);
    return `
          <div class="Stack-sc-hsu31d-0 hggCUz">
            <h1
              class="PageTitle-sc-1agnq51-0 bdmeit"
              style="font-size: 2rem; padding-top: 24px"
            >
              <span>Select category</span>
            </h1>
            <div
              class="Stack-sc-hsu31d-0 irmQpm"
              style="justify-content: center"
            >
              <button
                data-process-id="onStep1"
                class="style__ButtonElement-sc-jv24-0 hngsID clickable"
              >
               
                <span>
                  <span>
                    <span>${arrivalDayWeekShort}</span>
                    <span>${arrivalDateFr}</span> </span
                  >&nbsp;‐&nbsp;
                  <span>
                    <span>${departureDayWeekShort}</span>
                    <span>${departureDateFr}</span>
                  </span>
                </span>
              </button>
            </div>
          </div>
          <div
            class="components__MarginBase-sc-1fprdjl-0 components__LargeMargin-sc-1fprdjl-4 eWHTIh kElSVh"
          ></div>
          ${
            categoryItemsLoading
              ? `<ul><li style="text-align: center">Loading, please wait ... </li></ul>`
              : categoryItems.length
              ? `
          <ul
              class="style__BaseLayout-sc-17dphdl-3 TwoColumnsLayout-sc-16awrfa-0 kjGUHG lgmnzy"
            >
            ${categoryItems
              .map(function (m) {
                return wrs.categoryItem(m);
              })
              .join('')}
            </ul>
          `
              : `<ul><li style="text-align: center">No available item. Please choose another dates or location </li></ul>`
          }
         
          <div
            class="components__MarginBase-sc-1fprdjl-0 components__LargeMargin-sc-1fprdjl-4 eWHTIh kElSVh"
          ></div>
      `;
  };

  wrs.location = function (item) {
    const { id, title, picture, btn_text, link, page } = item;

    return `
          <div class="buildings__list__item ${
            link === 'inline' ? 'js-mews-link' : ''
          }">
            <a href="${page}" class="pointer">
              <img style="width: 370px; height: 500px;" src="${picture}" class="attachment-vertical-img size-vertical-img wp-post-image" alt="${title}" loading="lazy">
              <h4>${title}</h4>
            </a>
            <p>
            ${
              link === 'inline'
                ? `<span data-id="${id}" data-process-id="onLocation" class="location-btn button button-mews clickable">${btn_text}</span>`
                : `<a href="/contact" class="button pointer">Contact us</a>`
            }
            </p>
          </div>
    `;
  };

  wrs.locations = function (items) {
    return `
        ${items
          .map(function (m) {
            return wrs.location(m);
          })
          .join('')}
    `;
  };

  w.wrs = wrs;
})(window);
(function (e, w, paypal) {
  'use strict';
  var $ = e,
    wrs = w.wrs || {},
    state = { ...(wrs.state || {}) },
    step1,
    step2,
    step3,
    step4,
    step5,
    wrapper,
    navigation,
    buildingsList,
    catTitle,
    catPrice,
    catPrice2,
    catPrice3,
    taxPrice,
    eventsMap;

  wrs.initRootFields = function () {
    step1 = $('#step1');
    step2 = $('#step2');
    step3 = $('#step3');
    step4 = $('#step4');
    step5 = $('#step5');
    catTitle = $('#cat-title');
    catPrice = $('#cat-price');
    catPrice2 = $('#cat-price2');
    catPrice3 = $('#cat-price3');
    taxPrice = $('#tax-price');
    wrapper = $('#reservation-wrapper');
    navigation = $('#navigation');
    buildingsList = $('#buildings_list');
  };

  wrs.initFields = function () {};

  wrs.onLocation = function (e, ev = null) {
    ev.preventDefault();
    const locationId = $(e).attr('data-id');
    const location = state.locations.find((l) => l.id == locationId);
    wrs.setState({ locationId, location, step1BgImage: location.picture });
    const newAction =
      'https://wodaabe-stays.com/' + wrs.normalizeString(location.title);
    const newContent =
      '<form action="' +
      newAction +
      '"><button id="button-accommodation" type="submit">ACCOMMODATION</button></form>';
    $('#location-details').html(newContent);
    $('#location-name').text('Wodaabe Stay');
    $('#location-name-2').text(location.title);
    $('#location-logo').html(`
      <a href="home"><img src="http://wodaabe-stays.com/wp-content/uploads/2024/07/cropped-woodabe2.8Plan-de-travail-1-8-1.png"></a><alt="" class="style__ImageElement-sc-1izpjq4-0 dafbhF">
    `);
    wrapper.fadeIn();
    $('html').css('overflow', 'hidden');
  };

  wrs.normalizeString = function (inputString) {
    // Convert the string to lowercase
    let normalizedString = inputString.toLowerCase();

    // Remove accents using a regular expression
    normalizedString = normalizedString
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');

    // Maintain the position of hyphens
    // Replace hyphens with a unique placeholder (e.g., '___HY___')
    normalizedString = normalizedString.replace(/-/g, '___HY___');

    // Remove the placeholder for hyphens at the beginning or end of the string
    normalizedString = normalizedString.replace(/^___HY___|___HY___$/g, '');

    // Replace the remaining placeholders with hyphens
    normalizedString = normalizedString.replace(/___HY___/g, '-');

    return normalizedString;
  };

  wrs.initReservation = function () {
    wrs.initRootFields();
    wrs.initState();
    wrs.render();
    wrs.initLocations();
    eventsMap = [
      { key: 'addpromo', handler: wrs.addPromo },
      { key: 'removepromo', handler: wrs.removePromo },
      { key: 'editdateselection', handler: wrs.editDates },
      { key: 'hidedateselection', handler: wrs.hideDates },
      { key: 'onstep2', handler: wrs.onStep2 },
      { key: 'onValidate', handler: wrs.onValidate },
    ];
  };

  wrs.initLocations = function () {
    wrs.getLocations().then(function (res) {
      if (Array.isArray(res)) {
        res.sort((a, b) => a.position - b.position);
        wrs.setState({ locations: res });
        buildingsList.append(wrs.locations(res));
      }
    });
  };

  wrs.defaultEventHandler = function (e, ev = null) {
    wrs.setState({ [$(e).attr('name')]: $(e).val() });
  };

  wrs.addPromo = function (e, ev = null) {
    wrs.setState({ promocodevalue: '' });
  };

  wrs.setMinDates = function () {
    $('#arrivalDate').attr('min', wrs.state().arrivalDateMin);
    $('#departureDate').attr('min', wrs.state().departureDateMin);
  };

  wrs.editDates = function (e, ev = null) {
    wrs.setState({ dateselection: true });
    wrs.setMinDates();
  };

  wrs.checkDates = function (dates) {
    var v = true;
    console.log(dates);
    if (!dates.arrivalDateValid) {
      v = false;
      alert('Arrival date must be greater or equal than the date of today');
    } else if (!dates.departureDateValid) {
      v = false;
      alert('Departure date must be greater than the date of today');
    } else if (!dates.departureDateValid2) {
      v = false;
      alert('Departure date must be greater than the arrival date');
    }
    return v;
  };

  wrs.hideDates = function (e, ev = null) {
    var dates = wrs.currentDates(state.arrivalDate, state.departureDate);
    if (!wrs.checkDates(dates)) return;
    wrs.setState({
      dateselection: false,
      ...dates,
    });
    var arrival = document.getElementById('arrivalDate');
    console.log(arrival);
  };

  wrs.removePromo = function (e, ev = null) {
    wrs.setState({ promocodevalue: undefined });
  };

  wrs.bookingSomeoneElse = function (e, ev = null) {
    $(e).addClass('active-btn');
    $('.bookingMyself').show();
    $('.bookingSomeoneElse').show();
    $('.myDetails').hide();
    $("button[data-process-id='bookingMyself']").removeClass('active-btn');
    wrs.setState({ bookingMyself: false, bookingSomeoneElse: true });
  };
  wrs.bookingMyself = function (e, ev = null) {
    $(e).addClass('active-btn');
    $('.bookingMyself').hide();
    $('.bookingSomeoneElse').hide();
    $("button[data-process-id='bookingSomeoneElse']").removeClass('active-btn');
    wrs.setState({ bookingMyself: true, bookingSomeoneElse: false });
  };

  wrs.setState = function (data, cb = null) {
    state = { ...state, ...data };
    wrs.render();
    if (cb) cb();
  };

  wrs.setStateWithoutRerender = function (data) {
    state = { ...state, ...data };
  };

  wrs.handleEvent = function (ev) {
    var e = $(ev.currentTarget);
    var processId = e.attr('data-process-id');
    var t = eventsMap.findIndex(function (e) {
      return e.key === processId;
    });

    if (t > -1) {
      ev.preventDefault();
      eventsMap[t].handler(e);
    } else if (typeof wrs[processId] === 'function') {
      wrs[processId](e, ev);
    } else {
      wrs.defaultEventHandler(e);
    }
  };
  wrs.registerListeners = function (p) {
    $(w.document).on('change', '.changeable', wrs.handleEvent);
    $(w.document).on('click', '.clickable', wrs.handleEvent);
  };

  wrs.onStep1 = function () {
    var currentStep = 'step1';
    wrs.setState({
      step: currentStep,
      menus: wrs.getMenus(currentStep),
    });
    wrs.render();
    step1.show();
    step3.hide();
    step4.hide();
    step2.hide();
    step5.hide();
  };

  wrs.onStep2 = function (e, ev) {
    var currentStep = 'step2';
    wrs.setState({
      step: currentStep,
      menus: wrs.getMenus2(currentStep),
      categoryItems: [],
      categoryItemsLoading: true,
    });
    wrs.render();

    step1.hide();
    step3.hide();
    step4.hide();
    step2.show();
    step5.hide();

    wrs
      .getProducts({
        location_id: state.locationId,
        date_arrive: state.arrivalDate,
        date_departure: state.departureDate,
      })
      .then(async function (res) {
        if (Array.isArray(res)) {
          console.log(res);

          var listings = await wrs.ajax(
            wrs.payload({
              wrs_action: 'getPriceLabsListings',
            })
          );

          console.log(listings);

          // Use map to create an array of promises
          const promises = res.map(async (product) => {
            console.log('Product listing: ', product.price_listing_id);
            if (product.price_listing_id) {
              let sync_item = listings.find(
                (item) => item.id == product.price_listing_id
              );
              console.log('Sync item: ', sync_item);
              const results = await wrs.ajax(
                wrs.payload({
                  id: product.price_listing_id,
                  pms: sync_item?.pms,
                  dateFrom: state.arrivalDate,
                  dateTo: state.departureDate,
                  reason: true,
                  wrs_action: 'fetchPriceLabsData',
                })
              );

              const resultObj = JSON.parse(results);
              console.log(resultObj);

              // Use reduce to find the item with the lowest price
              let itemWithLowestPrice = resultObj[0].data
                .slice(0, -1)
                .reduce((lowest, item) => {
                  return item.price < lowest.price ? item : lowest;
                }, resultObj[0].data[0]);

              product.price_per_night = itemWithLowestPrice.price;
              product.prices = resultObj[0].data.slice(0, -1);
              console.log('Final product: ', product);
            }
          });

          // Wait for all promises to resolve
          await Promise.all(promises);

          console.log('Final res: ', res);

          wrs.setState({
            categoryItems: res,
            categoryItemsLoading: false,
          });
        } else {
          wrs.setState({
            categoryItemsLoading: false,
          });
        }

        wrs.render(); // Render after all async operations are done
      })
      .catch((error) => {
        console.error('An error occurred during the async operations:', error);
        wrs.setState({
          categoryItemsLoading: false,
        });
        wrs.render();
      });

    console.log('Category items: ', wrs.state().categoryItems);
    wrs.render();
  };

  wrs.onStep3 = function (e, ev) {
    const categoryId = $(e).attr('data-id');
    const category = state.categoryItems.find((l) => l.id == categoryId);

    var currentStep = 'step3';
    wrs.setState({
      step: currentStep,
      menus: wrs.getMenus3(currentStep),
      categoryId: categoryId || state.categoryId,
      category: category || state.category,
    });
    wrs.render();
    step1.hide();
    step2.hide();
    step4.hide();
    step3.show();
    step5.hide();
  };

  wrs.onStep4 = function () {
    console.log('onStep4');
    var currentStep = 'step4';
    wrs.setState({
      step: currentStep,
      menus: wrs.getMenus4(currentStep),
    });
    wrs.render();
    step1.hide();
    step2.hide();
    step3.hide();
    step4.show();
    step5.hide();
  };

  wrs.onStep5 = function () {
    var currentStep = 'step5';
    wrs.setState({
      step: currentStep,
      menus: wrs.getMenus5(currentStep),
    });
    console.log('ON STEP 5');
    console.log('State.step: ' + wrs.state().step);
    $('#btnv').hide();
    wrs.render();
    step1.hide();
    step2.hide();
    step3.hide();
    step4.hide();
    step5.show();
    if (state.category) {
      catTitle.text(state.category.title);
      catPrice.text(
        wrs.state().discountAmount
          ? 'Your discount: €' + wrs.state().discountAmount
          : ''
      );
      catPrice2.text('€' + state.category.price_per_night);
      catPrice3.text('€' + wrs.state().total);
      taxPrice.text('€' + (state.category.price_per_night / 10).toFixed(2));
    }
  };

  wrs.onMore = function (e, ev = null) {
    $(e).hide();
    $(e).parent().parent().find('.expandable-short').hide();
    $(e).parent().parent().find('.expandable').slideDown();
    $(e).parent().parent().find("[data-process-id='onLess']").show();
  };

  wrs.onLess = function (e, ev = null) {
    $(e).hide();
    $(e).parent().parent().find('.expandable').slideUp();
    $(e).parent().parent().find('.expandable-short').show();
    $(e).parent().parent().find("[data-process-id='onMore']").show();
  };

  wrs.onExit = function (e, ev = null) {
    if (!wrs.state().finish) {
      wrs.deleteBooking(e);
    }

    if (wrs.state().reservationId) {
      return w.location.reload();
    }
    $('html').css('overflow', 'scroll');
    wrapper.fadeOut();
    wrs.initState();
  };

  wrs.state = function () {
    return state;
  };

  wrs.monthNames = function (index) {
    const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    try {
      return monthNames[index];
    } catch (e) {
      return '';
    }
  };

  wrs.dayNames = function (index, short = false) {
    const dayNames = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];

    try {
      const n = dayNames[index];
      if (short) return n.substring(0, 3);
      return n;
    } catch (e) {
      return '';
    }
  };

  wrs.formatDate = function (dt) {
    const y = dt.getFullYear();
    const m = dt.getMonth() + 1; // Adding 1 to the month to get the correct month (January is 0, February is 1, etc.)
    const d = dt.getDate();
    const dw = dt.getDay();
    const mSting = m.toString().length < 2 ? `0${m}` : m;
    const dSting = d.toString().length < 2 ? `0${d}` : d;

    return {
      date: `${y}-${mSting}-${dSting}`,
      dateN: Number(`${y}${mSting}${dSting}`),
      dateFr: `${dSting}/${mSting}/${y}`,
      day: dSting,
      month: `${wrs.monthNames(m - 1)} ${y}`, // Subtract 1 from m to get the correct month name
      dayWeek: wrs.dayNames(dw),
      dayWeekShort: wrs.dayNames(dw, true),
    };
  };

  wrs.currentDates = function (t1 = null, t2 = null) {
    const dt = new Date();
    const dt1 = t1
      ? new Date(t1)
      : new Date(dt.getTime() + 1 * 24 * 60 * 60 * 1000);
    const dt2 = t2
      ? new Date(t2)
      : new Date(dt1.getTime() + 2 * 24 * 60 * 60 * 1000);

    const dt3 = new Date(dt1.getTime() + 1 * 24 * 60 * 60 * 1000);

    const dtF = wrs.formatDate(dt);
    const dt1F = wrs.formatDate(dt1);
    const dt2F = wrs.formatDate(dt2);
    const dt3F = wrs.formatDate(dt3);

    const date1 = new Date(dt1F.date);
    const date2 = new Date(dt2F.date);
    const diffTime = Math.abs(date2 - date1);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    console.log(diffTime + ' milliseconds');
    console.log(diffDays + ' days');

    return {
      arrivalDateValid: dt1F.dateN >= dtF.dateN,
      departureDateValid: dt2F.dateN > dtF.dateN,
      departureDateValid2: dt2F.dateN > dt1F.dateN,
      nbDays: diffDays,
      arrivalDateMin: dt1F.date,
      arrivalDate: dt1F.date,
      arrivalDateObject: dt1,
      arrivalDateFr: dt1F.dateFr,
      arrivalDay: dt1F.day,
      arrivalMonth: dt1F.month,
      arrivalDayWeek: dt1F.dayWeek,
      arrivalDayWeekShort: dt1F.dayWeekShort,
      departureDate: dt2F.date,
      departureDateMin: dt3F.date,
      departureDateObject: dt2,
      todayDateObject: dt,
      departureDateFr: dt2F.dateFr,
      departureDay: dt2F.day,
      departureMonth: dt2F.month,
      departureDayWeek: dt2F.dayWeek,
      departureDayWeekShort: dt2F.dayWeekShort,
    };
  };

  wrs.initState = function () {
    var currentStep = 'step1';
    state = {
      ...state,
      ...wrs.currentDates(),
      step: currentStep,
      promocodevalue: undefined,
      promocodeIsValid: undefined,
      promocodeIsEligible: undefined,
      dateselection: false,
      step1BgImage: '',
      menus: wrs.getMenus(currentStep),
      categoryItems: [],
      categoryItemsLoading: true,
      adultCount: 1,
      childCount: 0,
      currencyId: 1,
      languageId: 1,
      bookingMyself: true,
      bookingSomeoneElse: false,
      finish: false,
    };
    sessionStorage.removeItem('wrs_reservation_id');
    wrs.onStep1();
  };

  wrs.getMenus = function (step) {
    return [
      { img: '/wp-content/reservations/calendar.png', label: 'Dates' },
      {
        img: '/wp-content/reservations/category_idle.png',
        label: 'Categories',
      },
      { img: '/wp-content/reservations/rate_inactive.png', label: 'Rates' },
      {
        img: '/wp-content/reservations/list_inactive.png',
        label: 'Summary',
      },
      {
        img: '/wp-content/reservations/profile_inactive.png',
        label: 'Details',
      },
    ];
  };
  wrs.getMenus2 = function (step) {
    return [
      { img: '/wp-content/reservations/check.png', label: 'Dates' },
      {
        img: '/wp-content/reservations/category_active.png',
        label: 'Categories',
      },
      { img: '/wp-content/reservations/rate_inactive.png', label: 'Rates' },
      {
        img: '/wp-content/reservations/list_inactive.png',
        label: 'Summary',
      },
      {
        img: '/wp-content/reservations/profile_inactive.png',
        label: 'Details',
      },
    ];
  };

  wrs.getMenus3 = function (step) {
    return [
      { img: '/wp-content/reservations/check.png', label: 'Dates' },
      {
        img: '/wp-content/reservations/check.png',
        label: 'Categories',
      },
      { img: '/wp-content/reservations/rate_active.png', label: 'Rates' },
      {
        img: '/wp-content/reservations/list_inactive.png',
        label: 'Summary',
      },
      {
        img: '/wp-content/reservations/profile_inactive.png',
        label: 'Details',
      },
    ];
  };

  wrs.getMenus4 = function (step) {
    return [
      { img: '/wp-content/reservations/check.png', label: 'Dates' },
      {
        img: '/wp-content/reservations/check.png',
        label: 'Categories',
      },
      { img: '/wp-content/reservations/check.png', label: 'Rates' },
      {
        img: '/wp-content/reservations/list_active.png',
        label: 'Summary',
      },
      {
        img: '/wp-content/reservations/profile_inactive.png',
        label: 'Details',
      },
    ];
  };

  wrs.getMenus5 = function (step) {
    return [
      { img: '/wp-content/reservations/check.png', label: 'Dates' },
      {
        img: '/wp-content/reservations/check.png',
        label: 'Categories',
      },
      { img: '/wp-content/reservations/check.png', label: 'Rates' },
      {
        img: '/wp-content/reservations/check.png',
        label: 'Summary',
      },
      {
        img: '/wp-content/reservations/profile_active.png',
        label: 'Details',
      },
    ];
  };

  wrs.collectValues = function () {};

  wrs.cleanup = function () {
    $(w.document).off('change', '.changeable', wrs.handleEvent);
    $(w.document).off('click', '.clickable', wrs.handleEvent);
  };

  wrs.emptyElements = function () {
    wrs.collectValues();
    wrs.cleanup();
    step1.empty();
    step2.empty();
    navigation.empty();
  };

  wrs.render = function () {
    console.log('Rendering ...');
    wrs.emptyElements();
    step1.html(wrs.step1(state));
    step2.html(wrs.step2(state));
    step3.html(wrs.step3(state));
    step4.html(wrs.step4(state));
    navigation.html(wrs.navigation(state));
    wrs.initFields();
    wrs.registerListeners();
    wrs.renderCallBack();
  };

  wrs.nbNights = function () {
    if (!isNaN(Number(state.nbDays))) {
      return Number(state.nbDays);
    }
    return 0;
  };

  wrs.renderCallBack = function () {
    $('.number_nights').html(wrs.nbNights());
  };

  wrs.onPlusAdult = function () {
    const capacity = Number(state.category.capacity);
    let adultCount = state.adultCount;
    let childCount = state.childCount;
    const total = adultCount + childCount;

    if (capacity > total) {
      wrs.setState({ adultCount: adultCount + 1 });
    }
  };

  wrs.onPlusChild = function () {
    const capacity = Number(state.category.capacity);
    let adultCount = state.adultCount;
    let childCount = state.childCount;
    const total = adultCount + childCount;

    if (capacity > total) {
      wrs.setState({ childCount: childCount + 1 });
    }
  };

  wrs.onMinusAdult = function () {
    let adultCount = state.adultCount;
    if (adultCount > 0) {
      wrs.setState({ adultCount: adultCount - 1 });
    }
  };

  wrs.onMinusChild = function () {
    let childCount = state.childCount;
    if (childCount > 0) {
      wrs.setState({ childCount: childCount - 1 });
    }
  };

  wrs.validate = function () {
    const requiredFields = ['lastName', 'email'];
    if (state.bookingSomeoneElse) {
      requiredFields.push('myEmail');
      requiredFields.push('myLastName');
    }
    for (let index = 0; index < requiredFields.length; index++) {
      const e = requiredFields[index];
      if (!$('#' + e).val()) return false;
    }
    return true;
  };

  $(document).ready(function () {
    console.log('Document ready');

    wrs.sliderHome();
    wrs.sliderKnowMore();
    wrs.sliderFAQ();
    $(document).on('click', '#btnv', function (e) {
      wrs.onValidate(e.currentTarget, e);
    });

    $(document).on('change', '#arrivalDate', function (e) {
      console.log('Arrival date changed');
      // Get the value of the date input with ID '#arrivalDate'
      const arrivalDateInput = document.getElementById('arrivalDate');
      const departureDateInput = document.getElementById('departureDate');

      // Function to set the minimum value for the departure date based on the arrival date
      function setMinDepartureDate() {
        // Get the value of the arrival date input
        const arrivalDateValue = arrivalDateInput.value;
        console.log("Arrival :'" + arrivalDateValue + "'");
        console.log("Departure :'" + departureDateInput.value + "'");

        // Calculate the day after the arrival date
        const arrivalDate = new Date(arrivalDateValue);
        const departureDate = new Date(
          arrivalDate.getTime() + 24 * 60 * 60 * 1000
        ); // Adding one day
        console.log('Departure min: ' + departureDateInput.min);

        // Format the departure date as a string in 'yyyy-mm-dd' format
        const formattedDepartureDate = departureDate
          .toISOString()
          .split('T')[0];
        console.log(formattedDepartureDate);

        // Set the minimum value for the departure date input
        departureDateInput.min = formattedDepartureDate;
        console.log('Departure min: ' + departureDateInput.min);

        // Check if the current value of departureDateInput is earlier than the new minimum value
        const currentDepartureDate = new Date(departureDateInput.value);
        if (currentDepartureDate < departureDate) {
          // Update the value of departureDateInput to the day after the arrival date
          departureDateInput.value = formattedDepartureDate;
          departureDateInput.min = formattedDepartureDate;
          console.log('Departure min: ' + departureDateInput.min);
        }

        var dates = wrs.currentDates(
          arrivalDateInput.value,
          departureDateInput.value
        );
        if (!wrs.checkDates(dates)) return;
        wrs.setState({
          ...dates,
        });

        //wrs.setMinDates();

        arrivalDateInput.min = wrs.state().arrivalDateMin;
        departureDateInput.min = wrs.state().departureDateMin;

        console.log('Arrival min: ' + arrivalDateInput.min);
        console.log('Departure min: ' + departureDateInput.min);
      }

      setMinDepartureDate();

      // Attach an event listener to the arrival date input to listen for changes
      //arrivalDateInput.addEventListener('change', setMinDepartureDate);
    });

    if ($('#btnp').length) {
      wrs.initReservation();
      /* Uncomment to enable paypal default payment buttons
      if (!(wrs.nbNights() > 0)) return;
      paypal
        .Buttons({
          style: {
            shape: 'rect',
            color: 'silver',
            layout: 'vertical',
            label: 'checkout',
          },
          onInit: function (data, actions) {
            actions.disable();
            $('input, select, textarea').on('change', function (e) {
              wrs.validate() ? actions.enable() : actions.disable();
            });
          },
          onClick: function () {
            if (!wrs.validate()) {
              w.alert('Please fill all required fields!');
            }
          },
          onError: function (err) {
            console.error(err);
            //wrs.deleteBooking(err);
            w.alert('An error occured during payment, please try again later');
          },
          onCancel: function (data) {
            console.log(data);
            //wrs.deleteBooking(data);
            w.alert('You have cancel your payment');
          },
          onApprove: function (data, actions) {
            return actions.order.capture().then(function (details) {
              console.log(data, details);
              console.log('Transaction completed successfully ! Thanks');
              wrs.updatePaymentStatus(details);
              $('#btnp').hide();
              $('#contact-pay').hide();
              $('#btnp')
                .parent()
                .prepend(
                  $(`
              <h6 style="color: green;">Transaction completed successfully ! Thanks</h6>
            `)
                );
              wrs.setState({
                finish: true,
              });
            });
          },
          createOrder: function (data, actions) {
            return wrs.saveOrder().then(function (res) {
              try {
                if (!res || Number(res.reservationId) < 1)
                  throw new Error('An error occured');
                wrs.setState({
                  reservationId: res.reservationId,
                  reservationUuid: res.reservationUuid,
                });
                console.log(res);
                sessionStorage.setItem('wrs_reservation_id', res.reservationId);
                return actions.order.create({
                  purchase_units: [
                    {
                      invoice_id: res.reservationUuid,
                      amount: {
                        value: wrs.nbNights() * state.category.price_per_night,
                      },
                    },
                  ],
                });
              } catch (error) {
                console.error(error);
                throw new Error('An error occured');
              }
            });
          },
        })
        .render('#btnp');
        */
      wrs.initStripe();
    }
  });

  wrs.createOrder = function () {
    return wrs.saveOrder().then(function (res) {
      try {
        if (!res || Number(res.reservationId) < 1)
          throw new Error('An error occured');
        wrs.setState({
          reservationId: res.reservationId,
          reservationUuid: res.reservationUuid,
        });
        sessionStorage.setItem('wrs_reservation_id', res.reservationId);
        return res;
      } catch (error) {
        console.error(error);
        throw new Error('An error occured');
      }
    });
  };

  wrs.initStripeSession = async function () {
    console.log('Stripe Payment initiated...');

    try {
      if (!wrs.validate()) {
        w.alert('Please fill all required fields!');
        return;
      }
      let order = await wrs.createOrder();
      console.log(order);
      const session = await wrs.ajax(
        wrs.payload({
          ...wrs.serializeReservation(),
          id: order.reservationId,
          wrs_action: 'create_checkout_session',
        })
      );

      console.log('Stripe Session: ', session);
      var stripe = Stripe(
        'pk_test_51NNeEFBE58mBvRf7kAPH2jdAxKrezEqwUQ1rHoRWsuv9O9FU5fqvQIWImRLEtlTbDy0aXjJPFtVyY8PLbCVwGGU900cFClLYqj'
      );

      stripe
        .redirectToCheckout({
          sessionId: session.id,
        })
        .then(function (result) {
          if (result.error) {
            console.error('Error: ', result.error);
          }
        });
    } catch (error) {
      console.error('Error initiating Stripe Session: ', error);
    }
  };

  wrs.initStripe = function () {
    var container = $('#btnp');

    // Create a button for stripe payment
    var checkoutButton = $('<button>', {
      id: 'checkout-button',
      html: 'PAYMENT',
      click: wrs.initStripeSession,
    });

    $('#checkout-button').on({
      mousedown: function () {
        $(this).addClass('active-btn');
      },
      mouseup: function () {
        $(this).removeClass('active-btn');
      },
    });

    checkoutButton.css({
      width: '100%',
      'max-width': '750px',
    });

    // Append the button to the 'stripe' div
    container.append(checkoutButton);
    container.css({
      display: 'flex',
      'flex-direction': 'column',
      'align-items': 'center',
      'justify-content': 'center',
    });
  };

  wrs.serializeReservation = function () {
    return {
      id: state.reservationId,
      locationId: state.location.id,
      currency_id: state.currencyId,
      language_id: state.languageId,
      date_arrive: state.arrivalDate,
      date_departure: state.departureDate,
      promocodevalue: state.promocodevalue,
      promocodeIsValid: state.promocodeIsValid,
      promocodeIsEligible: state.promocodeIsEligible,
      product_id: state.category.id,
      adult_count: state.adultCount,
      child_count: state.childCount,
      first_name: $('#firstName').val(),
      last_name: $('#lastName').val(),
      email: $('#email').val(),
      phone: $('#phone').val(),
      country: $('#country').val(),
      notes: $('#notes').val(),
      buyer_first_name: $('#myFirstName').val(),
      buyer_last_name: $('#myLastName').val(),
      buyer_email: $('#myEmail').val(),
      buyer_phone: $('#myPhone').val(),
      is_guess: state.bookingSomeoneElse,
      agree_policy: false,
      receive_marketing_update: false,
      quantity: state.nbDays,
      total: state.total,
    };
  };
  wrs.saveOrder = function () {
    return new Promise(function (resolve) {
      wrs
        .ajax(
          wrs.payload({
            ...wrs.serializeReservation(),
            wrs_action: 'save_order',
          })
        )
        .then(function (res) {
          resolve(res);
        });
    });
  };
  wrs.updatePaymentStatus = function (details) {
    return new Promise(function (resolve) {
      wrs
        .ajax(
          wrs.payload({
            status: details.status,
            transaction_id: details.id,
            id: sessionStorage.getItem('wrs_reservation_id'),
            wrs_action: 'update_payment_status',
          })
        )
        .then(function (res) {
          resolve(res);
        });
    });
  };

  wrs.deleteBooking = function (details) {
    return new Promise(function (resolve) {
      wrs
        .ajax(
          wrs.payload({
            id: sessionStorage.getItem('wrs_reservation_id'),
            wrs_action: 'delete_reservation',
          })
        )
        .then(function (res) {
          sessionStorage.removeItem('wrs_reservation_id');
          resolve(res);
        });
    });
  };
  wrs.onValidate = async function () {
    if (!wrs.validate()) {
      return w.alert('Please fill all required fields!');
    }
    $('#btnp').css('display', 'flex');
    $('#btnw').css('display', 'none');
    //if (wrs.state().bookingMyself) await wrs.onValidateEmail($("#email").val());
    // if (wrs.state().bookingSomeoneElse) await wrs.onValidateEmail($("#myEmail").val());
  };

  /* wrs.onValidateEmail = async function (email) {
    if (!email) return;
    await new Promise(function (resolve) {
      wrs
        .ajax(
          wrs.payload({
            email: email,
            wrs_action: "sent_code",
          })
        )
        .then(function (res) {
          resolve(res);
        });
    });

    let v = true;
    while (v) {
      const result = w.prompt(
        "Please enter validation we have sent to this email address ( " +
          email +
          " ) "
      );

      if (result != null) {
        const res = await new Promise(function (resolve) {
          wrs
            .ajax(
              wrs.payload({
                code: result,
                wrs_action: "verify_code",
              })
            )
            .then(function (res) {
              resolve(res);
            });
        });
        console.log(res);
        if (Number(res.id) > 0) {
          v = false;
          $("#btnp").css("display", "flex");
          $("#btnw").css("display", "none");
        } else {
          w.alert("Invalid code");
        }
      } else {
        v = false;
      }
    }
  };*/

  wrs.sliderHome = function () {
    sessionStorage.setItem('slider_1_position', 0);
    sessionStorage.setItem('slider_1_position_index', 1);

    $('.testimonials__carousel .button.button--next').on('click', function (e) {
      const item0 = $('.testimonials__carousel').find('.owl-stage');
      const item1 = $('.testimonials__carousel').find('.owl-item.active');
      const item2 = $(item1).next('.owl-item');
      const item3 = $('.testimonials__carousel__nav__numbers');
      let pos = Number(sessionStorage.getItem('slider_1_position'));
      let posIndex = Number(sessionStorage.getItem('slider_1_position_index'));
      if (isNaN(pos)) pos = 0;
      if (isNaN(posIndex)) posIndex = 1;

      if (posIndex < 8) {
        const newPos = pos + 832;
        const newPosIndex = posIndex + 1;
        sessionStorage.setItem('slider_1_position', newPos);
        sessionStorage.setItem('slider_1_position_index', newPosIndex);
        item0.attr(
          'style',
          `transform: translate3d(-${newPos}px, 0px, 0px); transition: all 0.25s ease 0s; width: 6831px;`
        );
        item1.removeClass('active');
        item2.addClass('active');
        item3.html(`${newPosIndex} / 8`);
      }
    });
    $('.testimonials__carousel .button.button--prev').on('click', function (e) {
      const item0 = $('.testimonials__carousel').find('.owl-stage');
      const item1 = $('.testimonials__carousel').find('.owl-item.active');
      const item2 = $(item1).prev('.owl-item');
      const item3 = $('.testimonials__carousel__nav__numbers');
      let pos = Number(sessionStorage.getItem('slider_1_position'));
      let posIndex = Number(sessionStorage.getItem('slider_1_position_index'));
      if (isNaN(pos)) pos = 0;
      if (isNaN(posIndex)) posIndex = 1;

      if (posIndex > 1) {
        const newPos = pos - 832;
        const newPosIndex = posIndex - 1;
        sessionStorage.setItem('slider_1_position', newPos);
        sessionStorage.setItem('slider_1_position_index', newPosIndex);
        item0.attr(
          'style',
          `transform: translate3d(-${newPos}px, 0px, 0px); transition: all 0.25s ease 0s; width: 6831px;`
        );
        item1.removeClass('active');
        item2.addClass('active');
        item3.html(`${newPosIndex} / 8`);
      }
    });
  };

  wrs.sliderFAQ = function () {
    $('.et_pb_accordion_item').on('click', function (e) {
      $('.et_pb_accordion_item').find('.et_pb_toggle_content').slideUp();
      if ($(this).hasClass('open')) {
        $(this).find('.et_pb_toggle_content').slideUp();
        $(this).removeClass('open');
        $('.et_pb_accordion_item').removeClass('open');
      } else {
        $(this).find('.et_pb_toggle_content').slideDown();
        $('.et_pb_accordion_item').removeClass('open');
        $(this).addClass('open');
      }
    });
  };
  wrs.sliderKnowMore = function () {
    sessionStorage.setItem('slider_2_position', 0);
    sessionStorage.setItem('slider_2_position_index', 1);

    $('.image-carousel .button.button--next').on('click', function (e) {
      const item0 = $('.image-carousel').find('.owl-stage');
      const item1 = $('.image-carousel').find('.owl-item.active');
      const item2 = $(item1).next('.owl-item');
      const item3 = $('.image-carousel__nav__numbers');
      let pos = Number(sessionStorage.getItem('slider_2_position'));
      let posIndex = Number(sessionStorage.getItem('slider_2_position_index'));
      if (isNaN(pos)) pos = 0;
      if (isNaN(posIndex)) posIndex = 1;

      if (posIndex < 20) {
        const newPos = pos + 1211;
        const newPosIndex = posIndex + 1;
        sessionStorage.setItem('slider_2_position', newPos);
        sessionStorage.setItem('slider_2_position_index', newPosIndex);
        item0.attr(
          'style',
          `transform: translate3d(-${newPos}px, 0px, 0px); transition: all 0.25s ease 0s; width: 24227px;`
        );
        item1.removeClass('active');
        item2.addClass('active');
        item3.html(`${newPosIndex} / 20`);
      }
    });
    $('.image-carousel .button.button--prev').on('click', function (e) {
      const item0 = $('.image-carousel').find('.owl-stage');
      const item1 = $('.image-carousell').find('.owl-item.active');
      const item2 = $(item1).prev('.owl-item');
      const item3 = $('.image-carousel__nav__numbers');
      let pos = Number(sessionStorage.getItem('slider_2_position'));
      let posIndex = Number(sessionStorage.getItem('slider_2_position_index'));
      if (isNaN(pos)) pos = 0;
      if (isNaN(posIndex)) posIndex = 1;

      if (posIndex > 1) {
        const newPos = pos - 1211;
        const newPosIndex = posIndex - 1;
        sessionStorage.setItem('slider_2_position', newPos);
        sessionStorage.setItem('slider_2_position_index', newPosIndex);
        item0.attr(
          'style',
          `transform: translate3d(-${newPos}px, 0px, 0px); transition: all 0.25s ease 0s; width: 24227px;`
        );
        item1.removeClass('active');
        item2.addClass('active');
        item3.html(`${newPosIndex} / 20`);
      }
    });
  };
  w.wrs = wrs;
})(jQuery, window, paypal);
