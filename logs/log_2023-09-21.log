
[2023-09-21 01:48:50] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 01:49:22] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-21","date_departure":"2023-09-23","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-21 01:49:28] INFO Processing new AJAX Request width payload {"id":"null","wrs_action":"delete_reservation","action":"wrs_process_ajax"}
[2023-09-21 08:11:40] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 08:13:57] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 08:14:36] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-22","date_departure":"2023-09-30","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-21 08:14:44] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 08:16:25] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 08:18:08] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-22","date_departure":"2023-09-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-21 08:18:26] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 08:19:37] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 08:20:42] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 09:36:08] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 09:37:27] INFO Processing new AJAX Request width payload {"id":"null","wrs_action":"delete_reservation","action":"wrs_process_ajax"}
[2023-09-21 09:37:32] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 09:37:44] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 09:38:10] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 09:38:50] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax","customized":"{\\\"astra-settings[footer-bg-obj-responsive]\\\":{\\\"desktop\\\":{\\\"background-color\\\":\\\"var(--ast-global-color-5)\\\",\\\"background-image\\\":\\\"\\\",\\\"background-repeat\\\":\\\"repeat\\\",\\\"background-position\\\":\\\"center center\\\",\\\"background-size\\\":\\\"auto\\\",\\\"background-attachment\\\":\\\"scroll\\\",\\\"background-type\\\":\\\"color\\\",\\\"background-media\\\":\\\"\\\"},\\\"tablet\\\":{\\\"background-color\\\":\\\"\\\",\\\"background-image\\\":\\\"\\\",\\\"background-repeat\\\":\\\"repeat\\\",\\\"background-position\\\":\\\"center center\\\",\\\"background-size\\\":\\\"auto\\\",\\\"background-attachment\\\":\\\"scroll\\\",\\\"background-media\\\":\\\"\\\"},\\\"mobile\\\":{\\\"background-color\\\":\\\"\\\",\\\"background-image\\\":\\\"\\\",\\\"background-repeat\\\":\\\"repeat\\\",\\\"background-position\\\":\\\"center center\\\",\\\"background-size\\\":\\\"auto\\\",\\\"background-attachment\\\":\\\"scroll\\\",\\\"background-media\\\":\\\"\\\"}},\\\"astra-settings[footer-desktop-items]\\\":{\\\"above\\\":{\\\"above_1\\\":[\\\"widget-1\\\"],\\\"above_2\\\":[\\\"widget-2\\\"],\\\"above_3\\\":[\\\"widget-3\\\"],\\\"above_4\\\":[\\\"widget-4\\\",\\\"social-icons-1\\\"],\\\"above_5\\\":[],\\\"above_6\\\":[]},\\\"primary\\\":{\\\"primary_1\\\":[\\\"html-2\\\"],\\\"primary_2\\\":[\\\"copyright\\\"],\\\"primary_3\\\":[\\\"html-1\\\"],\\\"primary_4\\\":[],\\\"primary_5\\\":[],\\\"primary_6\\\":[]},\\\"below\\\":{\\\"below_1\\\":[],\\\"below_2\\\":[],\\\"below_3\\\":[],\\\"below_4\\\":[],\\\"below_5\\\":[],\\\"below_6\\\":[]},\\\"group\\\":\\\"astra-settings[footer-desktop-items]\\\",\\\"rows\\\":[\\\"above\\\",\\\"primary\\\",\\\"below\\\"],\\\"zones\\\":{\\\"above\\\":{\\\"above_1\\\":\\\"Above Section 1\\\",\\\"above_2\\\":\\\"Above Section 2\\\",\\\"above_3\\\":\\\"Above Section 3\\\",\\\"above_4\\\":\\\"Above Section 4\\\",\\\"above_5\\\":\\\"Above Section 5\\\",\\\"above_6\\\":\\\"Above Section 6\\\"},\\\"primary\\\":{\\\"primary_1\\\":\\\"Primary Section 1\\\",\\\"primary_2\\\":\\\"Primary Section 2\\\",\\\"primary_3\\\":\\\"Primary Section 3\\\",\\\"primary_4\\\":\\\"Primary Section 4\\\",\\\"primary_5\\\":\\\"Primary Section 5\\\",\\\"primary_6\\\":\\\"Primary Section 6\\\"},\\\"below\\\":{\\\"below_1\\\":\\\"Below Section 1\\\",\\\"below_2\\\":\\\"Below Section 2\\\",\\\"below_3\\\":\\\"Below Section 3\\\",\\\"below_4\\\":\\\"Below Section 4\\\",\\\"below_5\\\":\\\"Below Section 5\\\",\\\"below_6\\\":\\\"Below Section 6\\\"}},\\\"layouts\\\":{\\\"above\\\":{\\\"column\\\":\\\"4\\\",\\\"layout\\\":{\\\"desktop\\\":\\\"4-equal\\\",\\\"tablet\\\":\\\"4-equal\\\",\\\"mobile\\\":\\\"full\\\",\\\"flag\\\":false}},\\\"primary\\\":{\\\"column\\\":\\\"3\\\",\\\"layout\\\":{\\\"desktop\\\":\\\"3-equal\\\",\\\"tablet\\\":\\\"3-lheavy\\\",\\\"mobile\\\":\\\"full\\\",\\\"flag\\\":true}},\\\"below\\\":{\\\"column\\\":2,\\\"layout\\\":{\\\"mobile\\\":\\\"full\\\",\\\"tablet\\\":\\\"2-equal\\\",\\\"desktop\\\":\\\"2-lheavy\\\"}}},\\\"status\\\":{\\\"above\\\":true,\\\"primary\\\":true,\\\"below\\\":true},\\\"flag\\\":false,\\\"popup\\\":{\\\"popup_content\\\":[]}},\\\"astra-settings[hbb-footer-column]\\\":\\\"2\\\",\\\"astra-settings[hbb-footer-layout]\\\":{\\\"desktop\\\":\\\"2-lheavy\\\",\\\"tablet\\\":\\\"2-equal\\\",\\\"mobile\\\":\\\"full\\\",\\\"flag\\\":false}}"}
[2023-09-21 09:46:57] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 09:48:23] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax","customized":"{\\\"sidebars_widgets[footer-widget-1]\\\":[\\\"text-1\\\",\\\"block-8\\\",\\\"block-9\\\",\\\"block-10\\\",\\\"block-11\\\",\\\"block-14\\\"],\\\"widget_block[14]\\\":{\\\"encoded_serialized_instance\\\":\\\"YToxOntzOjc6ImNvbnRlbnQiO3M6OTA6IjwhLS0gd3A6aW1hZ2UgLS0+CjxmaWd1cmUgY2xhc3M9IndwLWJsb2NrLWltYWdlIj48aW1nIGFsdD0iIi8+PC9maWd1cmU+CjwhLS0gL3dwOmltYWdlIC0tPiI7fQ==\\\",\\\"title\\\":\\\"\\\",\\\"is_widget_customizer_js_value\\\":true,\\\"instance_hash_key\\\":\\\"589cd61288289a47f35ee38bad132fe4\\\",\\\"raw_instance\\\":{\\\"content\\\":\\\"<!-- wp:image -->\\\\n<figure class=\\\\\\\"wp-block-image\\\\\\\"><img alt=\\\\\\\"\\\\\\\"\/><\/figure>\\\\n<!-- \/wp:image -->\\\"}}}"}
[2023-09-21 09:49:16] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 09:51:16] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 09:53:10] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax","customized":"{\\\"astra-settings[hbb-stack]\\\":{\\\"desktop\\\":\\\"inline\\\",\\\"tablet\\\":\\\"stack\\\",\\\"mobile\\\":\\\"stack\\\"}}"}
[2023-09-21 09:53:17] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 14:36:54] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 14:38:14] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 14:44:28] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-22","date_departure":"2023-09-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-21 14:45:14] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 14:45:17] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 14:45:42] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-22","date_departure":"2023-09-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-21 14:45:51] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 14:46:12] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-21","date_departure":"2023-09-23","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-21 15:12:00] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 15:21:11] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 15:27:37] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 21:20:40] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 21:27:42] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-21 21:27:51] INFO Processing new AJAX Request width payload {"location_id":"7","date_arrive":"2023-09-22","date_departure":"2023-09-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-21 21:28:05] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}