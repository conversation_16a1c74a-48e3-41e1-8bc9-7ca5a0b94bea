<div id="reservation-wrapper">
  <div style="margin: 0px; padding: 0px; overflow-x: hidden; height: 100%">
    <div id="distributor" style="height: 100%">
      <div class="AppLayout__AppCanvas-sc-1n6pq6g-0 ktFePO">
        <div width="100%" class="PositionContainer-sc-p1cnsx-0 ggXAzT">
          <div role="banner" id="toolbar" class="style__AppbarContainer-sc-10aszh-0 cmeXPA">
            <div style="
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
              ">
              <div>
                <div class="Stack-sc-hsu31d-0 jIiwPp">
                  <div class="Stack-sc-hsu31d-0 gykQbo">
                    <div class="style__ImageWrap-sc-tlov0v-0 dAijgu" id="location-logo"></div>
                    <div style="margin-left: 4px; margin-bottom: 8px" class="style__PropertyName-sc-tlov0v-1 BCYKq"
                      id="location-name"></div>
                  </div>
                </div>
              </div>
              <div>
                <span id="location-name-2" style="font-weight: bold; display : none;"></span>
              </div>




              <div style="display: flex; align-items: center">
                <div style="margin-right: 1rem; display: none">
                  <select>
                    <option>EUR</option>
                  </select>
                </div>
                <div style="margin-right: 1rem; display: none">
                  <select>
                    <option>English</option>
                  </select>
                </div>
                <div class="Stack-sc-hsu31d-0 style__ActionElementsContainer-sc-10aszh-1 jIiwPp cYuerP">
                  <button
                    class="style__ButtonElement-sc-jv24-0 fUxyGn style__IconButtonElement-sc-66974x-0 kUkETl clickable"
                    data-process-id="onExit">
                    x
                  </button>
                </div>
              </div>
            </div>
          </div>
          <nav id="navigation" class="Navbar-sc-1ommzqz-0 bFHuOj liste-mobile"></nav>
        </div>
        <main offset="132" id="main" class="AppLayout__AppView-sc-1n6pq6g-1 epeqRw">
          <div>
            <div width="100%"
              class="Transition__TransitionElement-sc-lqfmkq-0 XqjEM PositionContainer-sc-p1cnsx-0 fsMpal">
              <div aria-busy="true" class="AppLayout__AppContent-sc-1n6pq6g-2 ebjVgV">
                <div aria-live="assertive" height="100%" class="PositionContainer-sc-p1cnsx-0 cELOjf"></div>
                <div class="View__TransitionViewWrapper-sc-pmvrpt-1 fRFRZt">
                  <div class="Transition__TransitionElement-sc-lqfmkq-0 GKVsD">
                    <div aria-live="assertive" class="View-sc-pmvrpt-0 eOhtQL"></div>
                    <div aria-busy="false" id="booking-datails" class="View-sc-pmvrpt-0 eOhtQL">
                      <!-- STEP 2 -->
                      <div id="step2" role="region" class="ContentContainer-sc-xp7hsm-0 jxFcPm rooms-view"
                        style="display: none"></div>
                      <!-- STEP 3 -->
                      <div id="step3" data-test-id="rates-view" role="region"
                        class="ContentContainer-sc-xp7hsm-0 jxFcPm" style="display: none"></div>

                      <!-- STEP 1 -->
                      <div id="step1" height="100%" data-test-id="dates-view"
                        class="PositionContainer-sc-p1cnsx-0 fURGZJ"></div>

                      <!-- STEP 4 -->
                      <div id="step4" class="ContentContainer-sc-xp7hsm-0 jxFcPm summary-view" role="region"
                        style="display: none"></div>

                      <!-- STEP 5 -->
                      <div id="step5" class="ContentContainer-sc-xp7hsm-0 jxFcPm summary-view" role="region">
                        <div class="Stack-sc-hsu31d-0 hggCUz">
                          <h1 class="PageTitle-sc-1agnq51-0 bdmeit" style="font-size: 2rem; padding-top: 24px">
                            <span>Contact &amp; payment details</span>
                          </h1>
                        </div>
                        <div
                          class="components__MarginBase-sc-1fprdjl-0 components__MediumMargin-sc-1fprdjl-3 eWHTIh hQUnoS">
                        </div>
                        <div class="Stack-sc-hsu31d-0 fJdyDT">
                          <div class="Card-sc-j4gsfs-0 ftIjDA" id="contact-pay">
                            <div class="Stack-sc-hsu31d-0 fJdyDT">
                              <div data-test-id="booker-selection" style="margin-top: 16px">
                                <div id="booking1" class="style__TextSwitchElement-sc-1qi2iud-0 eDGaTZ" role="tablist">
                                  <button id="booking2" tabindex="0" role="tab" aria-selected="true"
                                    class="style__TextSwitchItemElement-sc-9vqzqc-0 jTMBaw clickable active-btn"
                                    data-process-id="bookingMyself">
                                    <span id="booking">booking for myself</span></button><button id="booking3"
                                    tabindex="0" role="tab" aria-selected="false"
                                    class="style__TextSwitchItemElement-sc-9vqzqc-0 huvReg clickable"
                                    style="margin-left: 1rem" data-process-id="bookingSomeoneElse">
                                    <span id="booking4">booking for someone else</span>
                                  </button>
                                </div>
                              </div>
                              <h3 style="display: none" class="SubsectionTitle-sc-kn5y6d-0 bYpOvh bookingSomeoneElse">
                                <span>Your details</span>
                              </h3>
                              <form id="contact-details" aria-label="Your details" class="bookingSomeoneElse"
                                style="display: none">
                                <div class="Stack-sc-hsu31d-0 jStchL">
                                  <div class="Stack-sc-hsu31d-0 fmfdRL" style="display: flex">
                                    <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                                      <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                                        <label for="myFirstName"
                                          class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"><span>First
                                            name</span></label>
                                        <div class="Stack-sc-hsu31d-0 kzckpq"></div>
                                      </div>
                                      <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                                        <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                                          <div class="style__InputWrapper-sc-1hz6a59-2 hYznmx">
                                            <input name="myFirstName" autocomplete="given-name" id="myFirstName"
                                              class="style__InputElement-sc-1hz6a59-1 iDOcKT" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                                      <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                                        <label for="myLastName"
                                          class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"><span>Last
                                            name</span><span
                                            class="FormLabel__Asterisk-sc-z93c1x-1 kVMTZe">*</span></label>
                                        <div class="Stack-sc-hsu31d-0 kzckpq"></div>
                                      </div>
                                      <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                                        <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                                          <div class="style__InputWrapper-sc-1hz6a59-2 hYznmx">
                                            <input name="myLastName" autocomplete="family-name" id="myLastName"
                                              required="" class="style__InputElement-sc-1hz6a59-1 iDOcKT" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="Stack-sc-hsu31d-0 fmfdRL" style="display: flex">
                                    <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                                      <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                                        <label for="myEmail"
                                          class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"><span>Email</span><span
                                            aria-hidden="true"
                                            class="FormLabel__Asterisk-sc-z93c1x-1 kVMTZe">*</span></label>
                                        <div class="Stack-sc-hsu31d-0 kzckpq"></div>
                                      </div>

                                      <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                                        <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                                          <div class="style__InputWrapper-sc-1hz6a59-2 hYznmx">
                                            <input name="myEmail" autocomplete="email"
                                              data-test-id="checkout-field-email" id="myEmail" required=""
                                              class="style__InputElement-sc-1hz6a59-1 iDOcKT" />
                                          </div>
                                        </div>
                                      </div>
                                      <div aria-live="polite"
                                        class="Stack-sc-hsu31d-0 FormMessage__FormMessageElement-sc-gtqzzm-0 kzckpq hBGbSQ">
                                        <div>
                                          <span style="font-size: 0.75rem">We'll send a confirmation email to
                                            this address.</span>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                                      <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                                        <label for="myPhone"
                                          class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"><span>Phone</span></label>
                                        <div class="Stack-sc-hsu31d-0 kzckpq"></div>
                                      </div>
                                      <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                                        <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                                          <div class="style__InputWrapper-sc-1hz6a59-2 hYznmx">
                                            <input name="myPhone" autocomplete="tel" type="tel" id="myPhone"
                                              class="style__InputElement-sc-1hz6a59-1 iDOcKT" style="
                                                border: none;
                                                background: transparent;
                                              " />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </form>
                              <h3 class="SubsectionTitle-sc-kn5y6d-0 bYpOvh">
                                <span class="bookingSomeoneElse" style="display: none">Guest details</span>
                                <span class="bookingMyself">Your details</span>
                              </h3>
                              <form id="contact-details" aria-label="Your details">
                                <div class="Stack-sc-hsu31d-0 jStchL">
                                  <div class="Stack-sc-hsu31d-0 fmfdRL" style="display: flex">
                                    <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                                      <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                                        <label for="firstName"
                                          class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"><span>First
                                            name</span></label>
                                        <div class="Stack-sc-hsu31d-0 kzckpq"></div>
                                      </div>
                                      <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                                        <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                                          <div class="style__InputWrapper-sc-1hz6a59-2 hYznmx">
                                            <input name="firstName" autocomplete="given-name" id="firstName"
                                              class="style__InputElement-sc-1hz6a59-1 iDOcKT" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                                      <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                                        <label for="lastName"
                                          class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"><span>Last
                                            name</span><span aria-hidden="true"
                                            class="FormLabel__Asterisk-sc-z93c1x-1 kVMTZe">*</span></label>
                                        <div class="Stack-sc-hsu31d-0 kzckpq"></div>
                                      </div>
                                      <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                                        <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                                          <div class="style__InputWrapper-sc-1hz6a59-2 hYznmx">
                                            <input name="lastName" autocomplete="family-name" id="lastName" required=""
                                              class="style__InputElement-sc-1hz6a59-1 iDOcKT" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="Stack-sc-hsu31d-0 fmfdRL" style="display: flex">
                                    <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                                      <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                                        <label for="email"
                                          class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"><span>Email</span><span
                                            aria-hidden="true"
                                            class="FormLabel__Asterisk-sc-z93c1x-1 kVMTZe">*</span></label>
                                        <div class="Stack-sc-hsu31d-0 kzckpq"></div>
                                      </div>

                                      <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                                        <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                                          <div class="style__InputWrapper-sc-1hz6a59-2 hYznmx">
                                            <input name="email" autocomplete="email" id="email" required=""
                                              class="style__InputElement-sc-1hz6a59-1 iDOcKT" />
                                          </div>
                                        </div>
                                      </div>
                                      <div aria-live="polite" data-test-message-helper="true"
                                        class="Stack-sc-hsu31d-0 FormMessage__FormMessageElement-sc-gtqzzm-0 kzckpq hBGbSQ">
                                        <div>
                                          <span style="font-size: 0.75rem">We'll send a confirmation email to
                                            this address.</span>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                                      <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                                        <label for="phone"
                                          class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"><span>Phone</span></label>
                                        <div class="Stack-sc-hsu31d-0 kzckpq"></div>
                                      </div>
                                      <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                                        <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                                          <div class="style__InputWrapper-sc-1hz6a59-2 hYznmx">
                                            <input name="phone" type="tel" id="phone"
                                              class="style__InputElement-sc-1hz6a59-1 iDOcKT" style="
                                                border: none;
                                                background: transparent;
                                              " />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="Stack-sc-hsu31d-0 fmfdRL">
                                    <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                                      <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                                        <label for="nationalityCode"
                                          class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"><span>Nationality</span></label>
                                        <div class="Stack-sc-hsu31d-0 kzckpq"></div>
                                      </div>
                                      <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                                        <div class="style__InputContainer-sc-1hz6a59-0 gqVxEp">
                                          <div class="style__InputWrapper-sc-1hz6a59-2 hYznmx">
                                            <select id="country" id="phone"
                                              class="style__InputElement-sc-1hz6a59-1 iDOcKT" style="
                                                border: none;
                                                background: transparent;
                                              ">
                                              <option>select country</option>
                                              <option value="AF">
                                                Afghanistan
                                              </option>
                                              <option value="AX">
                                                Aland Islands
                                              </option>
                                              <option value="AL">
                                                Albania
                                              </option>
                                              <option value="DZ">
                                                Algeria
                                              </option>
                                              <option value="AS">
                                                American Samoa
                                              </option>
                                              <option value="AD">
                                                Andorra
                                              </option>
                                              <option value="AO">Angola</option>
                                              <option value="AI">
                                                Anguilla
                                              </option>
                                              <option value="AQ">
                                                Antarctica
                                              </option>
                                              <option value="AG">
                                                Antigua and Barbuda
                                              </option>
                                              <option value="AR">
                                                Argentina
                                              </option>
                                              <option value="AM">
                                                Armenia
                                              </option>
                                              <option value="AW">Aruba</option>
                                              <option value="AU">
                                                Australia
                                              </option>
                                              <option value="AT">
                                                Austria
                                              </option>
                                              <option value="AZ">
                                                Azerbaijan
                                              </option>
                                              <option value="BS">
                                                Bahamas
                                              </option>
                                              <option value="BH">
                                                Bahrain
                                              </option>
                                              <option value="BD">
                                                Bangladesh
                                              </option>
                                              <option value="BB">
                                                Barbados
                                              </option>
                                              <option value="BY">
                                                Belarus
                                              </option>
                                              <option value="BE">
                                                Belgium
                                              </option>
                                              <option value="BZ">Belize</option>
                                              <option value="BJ">Benin</option>
                                              <option value="BM">
                                                Bermuda
                                              </option>
                                              <option value="BT">Bhutan</option>
                                              <option value="BO">
                                                Bolivia
                                              </option>
                                              <option value="BQ">
                                                Bonaire, Sint Eustatius and Saba
                                              </option>
                                              <option value="BA">
                                                Bosnia and Herzegovina
                                              </option>
                                              <option value="BW">
                                                Botswana
                                              </option>
                                              <option value="BV">
                                                Bouvet Island
                                              </option>
                                              <option value="BR">Brazil</option>
                                              <option value="IO">
                                                British Indian Ocean Territory
                                              </option>
                                              <option value="BN">
                                                Brunei Darussalam
                                              </option>
                                              <option value="BG">
                                                Bulgaria
                                              </option>
                                              <option value="BF">
                                                Burkina Faso
                                              </option>
                                              <option value="BI">
                                                Burundi
                                              </option>
                                              <option value="KH">
                                                Cambodia
                                              </option>
                                              <option value="CM">
                                                Cameroon
                                              </option>
                                              <option value="CA">Canada</option>
                                              <option value="CV">
                                                Cape Verde
                                              </option>
                                              <option value="KY">
                                                Cayman Islands
                                              </option>
                                              <option value="CF">
                                                Central African Republic
                                              </option>
                                              <option value="TD">Chad</option>
                                              <option value="CL">Chile</option>
                                              <option value="CN">China</option>
                                              <option value="CX">
                                                Christmas Island
                                              </option>
                                              <option value="CC">
                                                Cocos (Keeling) Islands
                                              </option>
                                              <option value="CO">
                                                Colombia
                                              </option>
                                              <option value="KM">
                                                Comoros
                                              </option>
                                              <option value="CG">Congo</option>
                                              <option value="CD">
                                                Congo, Democratic Republic of
                                                the Congo
                                              </option>
                                              <option value="CK">
                                                Cook Islands
                                              </option>
                                              <option value="CR">
                                                Costa Rica
                                              </option>
                                              <option value="CI">
                                                Cote D'Ivoire
                                              </option>
                                              <option value="HR">
                                                Croatia
                                              </option>
                                              <option value="CU">Cuba</option>
                                              <option value="CW">
                                                Curacao
                                              </option>
                                              <option value="CY">Cyprus</option>
                                              <option value="CZ">
                                                Czech Republic
                                              </option>
                                              <option value="DK">
                                                Denmark
                                              </option>
                                              <option value="DJ">
                                                Djibouti
                                              </option>
                                              <option value="DM">
                                                Dominica
                                              </option>
                                              <option value="DO">
                                                Dominican Republic
                                              </option>
                                              <option value="EC">
                                                Ecuador
                                              </option>
                                              <option value="EG">Egypt</option>
                                              <option value="SV">
                                                El Salvador
                                              </option>
                                              <option value="GQ">
                                                Equatorial Guinea
                                              </option>
                                              <option value="ER">
                                                Eritrea
                                              </option>
                                              <option value="EE">
                                                Estonia
                                              </option>
                                              <option value="ET">
                                                Ethiopia
                                              </option>
                                              <option value="FK">
                                                Falkland Islands (Malvinas)
                                              </option>
                                              <option value="FO">
                                                Faroe Islands
                                              </option>
                                              <option value="FJ">Fiji</option>
                                              <option value="FI">
                                                Finland
                                              </option>
                                              <option value="FR">France</option>
                                              <option value="GF">
                                                French Guiana
                                              </option>
                                              <option value="PF">
                                                French Polynesia
                                              </option>
                                              <option value="TF">
                                                French Southern Territories
                                              </option>
                                              <option value="GA">Gabon</option>
                                              <option value="GM">Gambia</option>
                                              <option value="GE">
                                                Georgia
                                              </option>
                                              <option value="DE">
                                                Germany
                                              </option>
                                              <option value="GH">Ghana</option>
                                              <option value="GI">
                                                Gibraltar
                                              </option>
                                              <option value="GR">Greece</option>
                                              <option value="GL">
                                                Greenland
                                              </option>
                                              <option value="GD">
                                                Grenada
                                              </option>
                                              <option value="GP">
                                                Guadeloupe
                                              </option>
                                              <option value="GU">Guam</option>
                                              <option value="GT">
                                                Guatemala
                                              </option>
                                              <option value="GG">
                                                Guernsey
                                              </option>
                                              <option value="GN">Guinea</option>
                                              <option value="GW">
                                                Guinea-Bissau
                                              </option>
                                              <option value="GY">Guyana</option>
                                              <option value="HT">Haiti</option>
                                              <option value="HM">
                                                Heard Island and Mcdonald
                                                Islands
                                              </option>
                                              <option value="VA">
                                                Holy See (Vatican City State)
                                              </option>
                                              <option value="HN">
                                                Honduras
                                              </option>
                                              <option value="HK">
                                                Hong Kong
                                              </option>
                                              <option value="HU">
                                                Hungary
                                              </option>
                                              <option value="IS">
                                                Iceland
                                              </option>
                                              <option value="IN">India</option>
                                              <option value="ID">
                                                Indonesia
                                              </option>
                                              <option value="IR">
                                                Iran, Islamic Republic of
                                              </option>
                                              <option value="IQ">Iraq</option>
                                              <option value="IE">
                                                Ireland
                                              </option>
                                              <option value="IM">
                                                Isle of Man
                                              </option>
                                              <option value="IL">Israel</option>
                                              <option value="IT">Italy</option>
                                              <option value="JM">
                                                Jamaica
                                              </option>
                                              <option value="JP">Japan</option>
                                              <option value="JE">Jersey</option>
                                              <option value="JO">Jordan</option>
                                              <option value="KZ">
                                                Kazakhstan
                                              </option>
                                              <option value="KE">Kenya</option>
                                              <option value="KI">
                                                Kiribati
                                              </option>
                                              <option value="KP">
                                                Korea, Democratic People's
                                                Republic of
                                              </option>
                                              <option value="KR">
                                                Korea, Republic of
                                              </option>
                                              <option value="XK">Kosovo</option>
                                              <option value="KW">Kuwait</option>
                                              <option value="KG">
                                                Kyrgyzstan
                                              </option>
                                              <option value="LA">
                                                Lao People's Democratic Republic
                                              </option>
                                              <option value="LV">Latvia</option>
                                              <option value="LB">
                                                Lebanon
                                              </option>
                                              <option value="LS">
                                                Lesotho
                                              </option>
                                              <option value="LR">
                                                Liberia
                                              </option>
                                              <option value="LY">
                                                Libyan Arab Jamahiriya
                                              </option>
                                              <option value="LI">
                                                Liechtenstein
                                              </option>
                                              <option value="LT">
                                                Lithuania
                                              </option>
                                              <option value="LU">
                                                Luxembourg
                                              </option>
                                              <option value="MO">Macao</option>
                                              <option value="MK">
                                                Macedonia, the Former Yugoslav
                                                Republic of
                                              </option>
                                              <option value="MG">
                                                Madagascar
                                              </option>
                                              <option value="MW">Malawi</option>
                                              <option value="MY">
                                                Malaysia
                                              </option>
                                              <option value="MV">
                                                Maldives
                                              </option>
                                              <option value="ML">Mali</option>
                                              <option value="MT">Malta</option>
                                              <option value="MH">
                                                Marshall Islands
                                              </option>
                                              <option value="MQ">
                                                Martinique
                                              </option>
                                              <option value="MR">
                                                Mauritania
                                              </option>
                                              <option value="MU">
                                                Mauritius
                                              </option>
                                              <option value="YT">
                                                Mayotte
                                              </option>
                                              <option value="MX">Mexico</option>
                                              <option value="FM">
                                                Micronesia, Federated States of
                                              </option>
                                              <option value="MD">
                                                Moldova, Republic of
                                              </option>
                                              <option value="MC">Monaco</option>
                                              <option value="MN">
                                                Mongolia
                                              </option>
                                              <option value="ME">
                                                Montenegro
                                              </option>
                                              <option value="MS">
                                                Montserrat
                                              </option>
                                              <option value="MA">
                                                Morocco
                                              </option>
                                              <option value="MZ">
                                                Mozambique
                                              </option>
                                              <option value="MM">
                                                Myanmar
                                              </option>
                                              <option value="NA">
                                                Namibia
                                              </option>
                                              <option value="NR">Nauru</option>
                                              <option value="NP">Nepal</option>
                                              <option value="NL">
                                                Netherlands
                                              </option>
                                              <option value="AN">
                                                Netherlands Antilles
                                              </option>
                                              <option value="NC">
                                                New Caledonia
                                              </option>
                                              <option value="NZ">
                                                New Zealand
                                              </option>
                                              <option value="NI">
                                                Nicaragua
                                              </option>
                                              <option value="NE">Niger</option>
                                              <option value="NG">
                                                Nigeria
                                              </option>
                                              <option value="NU">Niue</option>
                                              <option value="NF">
                                                Norfolk Island
                                              </option>
                                              <option value="MP">
                                                Northern Mariana Islands
                                              </option>
                                              <option value="NO">Norway</option>
                                              <option value="OM">Oman</option>
                                              <option value="PK">
                                                Pakistan
                                              </option>
                                              <option value="PW">Palau</option>
                                              <option value="PS">
                                                Palestinian Territory, Occupied
                                              </option>
                                              <option value="PA">Panama</option>
                                              <option value="PG">
                                                Papua New Guinea
                                              </option>
                                              <option value="PY">
                                                Paraguay
                                              </option>
                                              <option value="PE">Peru</option>
                                              <option value="PH">
                                                Philippines
                                              </option>
                                              <option value="PN">
                                                Pitcairn
                                              </option>
                                              <option value="PL">Poland</option>
                                              <option value="PT">
                                                Portugal
                                              </option>
                                              <option value="PR">
                                                Puerto Rico
                                              </option>
                                              <option value="QA">Qatar</option>
                                              <option value="RE">
                                                Reunion
                                              </option>
                                              <option value="RO">
                                                Romania
                                              </option>
                                              <option value="RU">
                                                Russian Federation
                                              </option>
                                              <option value="RW">Rwanda</option>
                                              <option value="BL">
                                                Saint Barthelemy
                                              </option>
                                              <option value="SH">
                                                Saint Helena
                                              </option>
                                              <option value="KN">
                                                Saint Kitts and Nevis
                                              </option>
                                              <option value="LC">
                                                Saint Lucia
                                              </option>
                                              <option value="MF">
                                                Saint Martin
                                              </option>
                                              <option value="PM">
                                                Saint Pierre and Miquelon
                                              </option>
                                              <option value="VC">
                                                Saint Vincent and the Grenadines
                                              </option>
                                              <option value="WS">Samoa</option>
                                              <option value="SM">
                                                San Marino
                                              </option>
                                              <option value="ST">
                                                Sao Tome and Principe
                                              </option>
                                              <option value="SA">
                                                Saudi Arabia
                                              </option>
                                              <option value="SN">
                                                Senegal
                                              </option>
                                              <option value="RS">Serbia</option>
                                              <option value="CS">
                                                Serbia and Montenegro
                                              </option>
                                              <option value="SC">
                                                Seychelles
                                              </option>
                                              <option value="SL">
                                                Sierra Leone
                                              </option>
                                              <option value="SG">
                                                Singapore
                                              </option>
                                              <option value="SX">
                                                Sint Maarten
                                              </option>
                                              <option value="SK">
                                                Slovakia
                                              </option>
                                              <option value="SI">
                                                Slovenia
                                              </option>
                                              <option value="SB">
                                                Solomon Islands
                                              </option>
                                              <option value="SO">
                                                Somalia
                                              </option>
                                              <option value="ZA">
                                                South Africa
                                              </option>
                                              <option value="GS">
                                                South Georgia and the South
                                                Sandwich Islands
                                              </option>
                                              <option value="SS">
                                                South Sudan
                                              </option>
                                              <option value="ES">Spain</option>
                                              <option value="LK">
                                                Sri Lanka
                                              </option>
                                              <option value="SD">Sudan</option>
                                              <option value="SR">
                                                Suriname
                                              </option>
                                              <option value="SJ">
                                                Svalbard and Jan Mayen
                                              </option>
                                              <option value="SZ">
                                                Swaziland
                                              </option>
                                              <option value="SE">Sweden</option>
                                              <option value="CH">
                                                Switzerland
                                              </option>
                                              <option value="SY">
                                                Syrian Arab Republic
                                              </option>
                                              <option value="TW">
                                                Taiwan, Province of China
                                              </option>
                                              <option value="TJ">
                                                Tajikistan
                                              </option>
                                              <option value="TZ">
                                                Tanzania, United Republic of
                                              </option>
                                              <option value="TH">
                                                Thailand
                                              </option>
                                              <option value="TL">
                                                Timor-Leste
                                              </option>
                                              <option value="TG">Togo</option>
                                              <option value="TK">
                                                Tokelau
                                              </option>
                                              <option value="TO">Tonga</option>
                                              <option value="TT">
                                                Trinidad and Tobago
                                              </option>
                                              <option value="TN">
                                                Tunisia
                                              </option>
                                              <option value="TR">Turkey</option>
                                              <option value="TM">
                                                Turkmenistan
                                              </option>
                                              <option value="TC">
                                                Turks and Caicos Islands
                                              </option>
                                              <option value="TV">Tuvalu</option>
                                              <option value="UG">Uganda</option>
                                              <option value="UA">
                                                Ukraine
                                              </option>
                                              <option value="AE">
                                                United Arab Emirates
                                              </option>
                                              <option value="GB">
                                                United Kingdom
                                              </option>
                                              <option value="US">
                                                United States
                                              </option>
                                              <option value="UM">
                                                United States Minor Outlying
                                                Islands
                                              </option>
                                              <option value="UY">
                                                Uruguay
                                              </option>
                                              <option value="UZ">
                                                Uzbekistan
                                              </option>
                                              <option value="VU">
                                                Vanuatu
                                              </option>
                                              <option value="VE">
                                                Venezuela
                                              </option>
                                              <option value="VN">
                                                Viet Nam
                                              </option>
                                              <option value="VG">
                                                Virgin Islands, British
                                              </option>
                                              <option value="VI">
                                                Virgin Islands, U.s.
                                              </option>
                                              <option value="WF">
                                                Wallis and Futuna
                                              </option>
                                              <option value="EH">
                                                Western Sahara
                                              </option>
                                              <option value="YE">Yemen</option>
                                              <option value="ZM">Zambia</option>
                                              <option value="ZW">
                                                Zimbabwe
                                              </option>
                                            </select>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div></div>
                                  </div>
                                  <div class="style__FieldContainer-sc-1nv04ex-0 ckIMMf">
                                    <div class="style__FieldHeader-sc-1nv04ex-1 dCdeYC">
                                      <label for="notes"
                                        class="FormLabel__FormLabelElement-sc-z93c1x-0 dgoBoQ"><span>Special
                                          requests</span></label>
                                      <div class="Stack-sc-hsu31d-0 kzckpq"></div>
                                    </div>
                                    <div class="style__InputWrapper-sc-1nv04ex-3 eaagmD">
                                      <div class="style__TextareaContainerElement-sc-1pufucl-0 hSGtQB"
                                        style="width: 100%">
                                        <div class="style__ValueContainerElement-sc-1pufucl-3 fihCyX">
                                          <div
                                            class="style__PlaceholderElement-sc-1hz6a59-3 style__TextAreaPlaceholder-sc-1pufucl-2 wsMko ioTUrl">
                                          </div>
                                          <textarea rows="3" id="notes" name="notes"
                                            class="style__TextareaElement-sc-1pufucl-1 izuusJ" style="
                                              border: none;
                                              background: transparent;
                                            "></textarea>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </form>
                            </div>
                          </div>

                          <div class="Card-sc-j4gsfs-0 ftIjDA">
                            <div class="View__TransitionViewWrapper-sc-pmvrpt-1 fRFRZt">
                              <div class="Transition__TransitionElement-sc-lqfmkq-0 GKVsD">
                                <div aria-live="assertive"></div>
                                <div aria-busy="false">
                                  <div class="Stack-sc-hsu31d-0 hggCUz">
                                    <div id="btnp" style="
                                        display: none;
                                        justify-content: center;
                                        padding: 16px 0px;
                                      "></div>
                                    <div class="Stack-sc-hsu31d-0 hggCUz" style="margin-bottom: 8px">
                                      <div class="Stack-sc-hsu31d-0 btuPPv">
                                        <div class="Stack__StackItem-sc-hsu31d-1 ffuMMd">
                                          <div class="Stack-sc-hsu31d-0 iqtvBB" style="
                                              display: flex;
                                              justify-content: space-between;
                                            ">
                                            <strong class="MinorHighlight-sc-1kyyewz-0 bCIxvA"><span
                                                id="cat-title"></span> +
                                              <span>Standard Rate</span></strong><strong
                                              class="MinorHighlight-sc-1kyyewz-0 bCIxvA"><span
                                                id="cat-price"></span></strong>
                                          </div>
                                          <div data-test-id="additional-info" class="SmallLabel-sc-1xd6i7w-0 cNnLGm">
                                            1 ×
                                            <span id="cat-price2"></span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div class="style__DividerContainer-sc-qud6in-0 ftPbiR">
                                      <div class="style__DividerElement-sc-qud6in-1 krAfZo"></div>
                                      <div class="style__DividerElement-sc-qud6in-1 krAfZo"></div>
                                    </div>
                                    <div class="Stack-sc-hsu31d-0 iEVyvT" style="margin: 8px 0">
                                      <div class="Stack-sc-hsu31d-0 hggCUz">
                                        <div class="Stack-sc-hsu31d-0 iqtvBB" style="
                                            display: flex;
                                            justify-content: space-between;
                                          ">
                                          <div data-test-id="tax-rate" class="Label-sc-ruhf0l-0 iaRBLf">
                                            ALV <span>10%</span>
                                          </div>
                                          <div class="Label-sc-ruhf0l-0 iaRBLf" id="tax-price"></div>
                                        </div>
                                      </div>
                                      <div class="SmallLabel-sc-1xd6i7w-0 jxPsaa" style="
                                          display: flex;
                                          justify-content: flex-end;
                                        ">
                                        <span>Taxes included in price</span>
                                      </div>
                                    </div>
                                    <div class="style__DividerContainer-sc-qud6in-0 ftPbiR">
                                      <div class="style__DividerElement-sc-qud6in-1 krAfZo"></div>
                                      <div class="style__DividerElement-sc-qud6in-1 krAfZo"></div>
                                    </div>
                                    <div class="Stack-sc-hsu31d-0 hggCUz">
                                      <div class="Stack-sc-hsu31d-0 iqtvBB" style="
                                          display: flex;
                                          justify-content: space-between;
                                          margin-top: 8px;
                                        ">
                                        <strong data-test-id="total-bar-total"
                                          class="ModerateHighlight-sc-1az58ah-0 gqykjU"><span>Total</span></strong><strong
                                          class="ModerateHighlight-sc-1az58ah-0 gqykjU"><span
                                            id="cat-price3"></span></strong>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="Stack-sc-hsu31d-0 iqBJJL" id="btnw" style="justify-content: end; display: flex">
                            <button id="btnv" class="style__ButtonElement-sc-jv24-0 iliXYr boutoncontinue">
                              <span>Continue</span>
                            </button>
                          </div>
                        </div>
                        <div
                          class="components__MarginBase-sc-1fprdjl-0 components__LargeMargin-sc-1fprdjl-4 eWHTIh kElSVh">
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
        <div></div>
        <div id="portal-container"></div>
      </div>
    </div>
  </div>
</div>