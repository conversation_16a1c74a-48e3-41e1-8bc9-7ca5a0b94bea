
[2022-08-26 05:14:12] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 05:14:48] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 05:48:26] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 05:49:03] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 05:49:32] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 05:55:35] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:03:42] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:06:59] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:08:28] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:11:49] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:38:28] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:38:39] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:38:41] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:38:57] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:41:02] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:42:32] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:42:54] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:44:20] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:46:20] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 06:46:30] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 15:52:54] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 15:59:40] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 15:59:52] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:06:36] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:06:49] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:11:25] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:13:15] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:22:48] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:26:19] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:26:28] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:28:13] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:28:16] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:28:36] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:29:12] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:29:23] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:29:25] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:53:05] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:54:40] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:55:07] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 16:57:06] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:00:04] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:00:16] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:00:44] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-08-27","date_departure":"2022-08-29","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 17:02:48] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-08-27","date_departure":"2022-08-29","promocodevalue":"undefined","product_id":"1","adult_count":"2","child_count":"2","first_name":"Victorien","last_name":"KONGNUY","email":"<EMAIL>","phone":"+237690210677","country":"CM","notes":"","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-08-26 17:02:48] INFO 
[2022-08-26 17:04:15] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"23J69586D3579313A","id":"20","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2022-08-26 17:04:44] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:05:55] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:08:12] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:18:11] INFO Creating Tables ... 
[2022-08-26 17:18:11] INFO Query {"IF":"Created table IF"} | CREATE TABLE IF NOT EXISTS wp_wrs_currencies (
				`id` INT(11) NOT NULL AUTO_INCREMENT,
				`name` VARCHAR(254) NOT NULL,
				`code` VARCHAR(254) NOT NULL,
				`date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_wp_wrs_currencies PRIMARY KEY(`id`)
			) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 
[2022-08-26 17:18:11] INFO Query {"IF":"Created table IF"} | CREATE TABLE IF NOT EXISTS wp_wrs_languages (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `name` VARCHAR(254) NOT NULL,
        `code` VARCHAR(254) NOT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_wp_wrs_languages PRIMARY KEY(`id`)
      ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 
[2022-08-26 17:18:11] INFO Query {"IF":"Created table IF"} | CREATE TABLE IF NOT EXISTS wp_wrs_configurations (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `name` VARCHAR(254) NOT NULL,
        `value` LONGTEXT NOT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_wp_wrs_configurations PRIMARY KEY(`id`)
      ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 
[2022-08-26 17:18:11] INFO Query {"IF":"Created table IF"} | CREATE TABLE IF NOT EXISTS wp_wrs_promotional_codes (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `code` VARCHAR(254) NOT NULL,
        `value` DECIMAL(10,2) NOT NULL,
        `description` TEXT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_wp_wrs_promotional_codes PRIMARY KEY(`id`)
      ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 
[2022-08-26 17:18:11] INFO Query {"IF":"Created table IF"} | CREATE TABLE IF NOT EXISTS wp_wrs_locations (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `title` VARCHAR(254) NOT NULL,
        `picture` VARCHAR(254) NOT NULL,
        `btn_text` VARCHAR(254) NOT NULL,
        `link` VARCHAR(254) NULL,
        `page` VARCHAR(254) NULL,
        `logo` VARCHAR(254) NULL,
        `position` INT NOT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_wp_wrs_locations PRIMARY KEY(`id`)
      ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 
[2022-08-26 17:18:11] INFO Query {"IF":"Created table IF"} | CREATE TABLE IF NOT EXISTS wp_wrs_products (
				`id` INT(11) NOT NULL AUTO_INCREMENT,
				`title` VARCHAR(254) NOT NULL,
				`picture` VARCHAR(254) NOT NULL,
				`capacity` INT NOT NULL,
				`description` TEXT NOT NULL,
        `price_per_night` DECIMAL(10,2) NOT NULL,
        `rate_text` LONGTEXT NOT NULL,
				`date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `location_id` INT NOT NULL,
        CONSTRAINT pk_wp_wrs_products PRIMARY KEY(`id`),
        CONSTRAINT fk_wp_wrs_products_location_id FOREIGN KEY(`location_id`) REFERENCES wp_wrs_locations(`id`) ON DELETE CASCADE ON UPDATE CASCADE
			) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 
[2022-08-26 17:18:11] INFO Query {"IF":"Created table IF"} | CREATE TABLE IF NOT EXISTS wp_wrs_persons (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `first_name` VARCHAR(254) NULL,
        `last_name` VARCHAR(254) NULL,
        `email` VARCHAR(254) NULL,
        `phone` VARCHAR(254) NULL,
        `country` VARCHAR(254) NULL,
        `comment` VARCHAR(254) NULL,
        `is_guess` BOOLEAN DEFAULT 1,
        `buyer_first_name` VARCHAR(254) NULL,
        `buyer_last_name` VARCHAR(254) NULL,
        `buyer_email` VARCHAR(254) NULL,
        `buyer_phone` VARCHAR(254) NULL,
        `agree_policy` BOOLEAN DEFAULT 0,
        `receive_marketing_update` BOOLEAN DEFAULT 0,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_wp_wrs_persons PRIMARY KEY(`id`)
      ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 
[2022-08-26 17:18:11] INFO Query {"IF":"Created table IF"} | CREATE TABLE IF NOT EXISTS wp_wrs_reservation_payments (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `transaction_id` VARCHAR(254) NULL,
        `status` VARCHAR(254) NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_wp_wrs_reservation_payments PRIMARY KEY(`id`)
      ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 
[2022-08-26 17:18:11] INFO Query {"IF":"Created table IF"} | CREATE TABLE IF NOT EXISTS wp_wrs_reservations (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `date_arrive` VARCHAR(254) NULL,
        `date_departure` VARCHAR(254) NULL,
        `uuid` VARCHAR(254) NOT NULL,
        `adult_count` INT NULL,
        `child_count` INT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `person_id` INT NULL,
        `language_id` INT NULL,
        `currency_id` INT NULL,
        `payment_id` INT NULL,
        CONSTRAINT pk_wp_wrs_reservations PRIMARY KEY(`id`),
        CONSTRAINT fk_wp_wrs_reservations_person_id FOREIGN KEY(`person_id`) REFERENCES wp_wrs_persons(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_wp_wrs_reservations_language_id FOREIGN KEY(`language_id`) REFERENCES wp_wrs_languages(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_wp_wrs_reservations_currency_id FOREIGN KEY(`currency_id`) REFERENCES wp_wrs_currencies(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_wp_wrs_reservations_payment_id FOREIGN KEY(`payment_id`) REFERENCES wp_wrs_reservation_payments(`id`) ON DELETE SET NULL ON UPDATE CASCADE
      ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 
[2022-08-26 17:18:11] INFO Query {"IF":"Created table IF"} | CREATE TABLE IF NOT EXISTS wp_wrs_reservation_items (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `quantity` INT NOT NULL,
        `product_id` INT NULL,
        `reservation_id` INT NOT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_wp_wrs_reservation_items PRIMARY KEY(`id`),
        CONSTRAINT fk_wp_wrs_reservation_items_product_id FOREIGN KEY(`product_id`) REFERENCES wp_wrs_products(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_wp_wrs_reservation_items_reservation_id FOREIGN KEY(`reservation_id`) REFERENCES wp_wrs_reservations(`id`) ON DELETE CASCADE ON UPDATE CASCADE
      ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 
[2022-08-26 17:18:11] INFO Tables creation completed !
[2022-08-26 17:18:13] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:18:18] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:18:36] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:18:43] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:18:49] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:18:54] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:19:23] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:21:08] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:21:15] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:22:41] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2024-01-01","date_departure":"2024-01-03","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 17:23:03] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:23:39] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2024-01-01","date_departure":"2024-01-04","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 17:24:45] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2024-01-01","date_departure":"2024-01-04","promocodevalue":"undefined","product_id":"1","adult_count":"3","child_count":"2","first_name":"Victorien","last_name":"KONGNUY","email":"<EMAIL>","phone":"+237690210677","country":"CM","notes":"","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-08-26 17:25:24] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"98405616WK897950M","id":"25","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2022-08-26 17:27:00] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:27:07] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:27:29] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:27:41] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:28:04] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:31:00] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:32:08] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"98405616WK897950M","id":"25","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2022-08-26 17:32:55] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"98405616WK897950M","id":"25","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2022-08-26 17:33:42] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:37:21] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"98405616WK897950M","id":"25","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2022-08-26 17:38:03] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"98405616WK897950M","id":"25","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2022-08-26 17:41:56] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"98405616WK897950M","id":"25","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2022-08-26 17:44:55] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"98405616WK897950M","id":"25","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2022-08-26 17:47:06] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"98405616WK897950M","id":"25","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2022-08-26 17:48:09] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:50:52] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:51:25] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-05-01","date_departure":"2023-05-04","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 17:52:21] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2023-05-01","date_departure":"2023-05-04","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"Victorien","last_name":"KONGNUY","email":"<EMAIL>","phone":"673029034","country":"CM","notes":"","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-08-26 17:53:04] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:53:27] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 17:54:05] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-010-03","date_departure":"2022-010-06","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 17:54:52] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-010-03","date_departure":"2022-010-06","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"Vic","last_name":"KONG","email":"<EMAIL>","phone":"690210677","country":"CM","notes":"","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-08-26 18:02:27] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:02:45] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:02:52] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:03:10] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-08-30","date_departure":"2022-09-13","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 18:03:22] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-08-30","date_departure":"2022-09-13","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 18:05:44] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:05:49] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:05:55] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:06:17] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:06:28] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:06:39] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:06:46] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:07:15] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:07:23] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:11:42] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:12:28] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:14:17] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:17:15] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:17:23] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:17:26] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:17:34] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:17:38] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:17:55] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:18:07] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:18:10] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:28:10] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:40:38] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:40:46] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:40:53] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:44:19] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:52:42] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 18:53:07] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-08-30","date_departure":"2022-09-07","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 18:53:58] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-08-30","date_departure":"2022-09-07","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"Leo","last_name":"Ghemtio","email":"<EMAIL>","phone":"0766109339","country":"FR","notes":"","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-08-26 18:54:57] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-08-30","date_departure":"2022-09-07","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"Leo","last_name":"Ghemtio","email":"<EMAIL>","phone":"0766109339","country":"FR","notes":"","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-08-26 19:15:56] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-08-30","date_departure":"2022-09-07","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"Leo","last_name":"Ghemtio","email":"<EMAIL>","phone":"0766109339","country":"FR","notes":"","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-08-26 19:19:24] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-08-30","date_departure":"2022-09-07","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"Leo","last_name":"Ghemtio","email":"<EMAIL>","phone":"0766109339","country":"FR","notes":"","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-08-26 19:19:57] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:17:44] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:18:10] INFO Processing new AJAX Request width payload {"location_id":"2","date_arrive":"2022-08-30","date_departure":"2022-09-07","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 20:18:14] INFO Processing new AJAX Request width payload {"location_id":"2","date_arrive":"2022-08-30","date_departure":"2022-09-07","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 20:18:23] INFO Processing new AJAX Request width payload {"location_id":"2","date_arrive":"2022-09-01","date_departure":"2022-09-07","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 20:18:37] INFO Processing new AJAX Request width payload {"location_id":"2","date_arrive":"2022-10-13","date_departure":"2022-10-27","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 20:18:56] INFO Processing new AJAX Request width payload {"location_id":"2","date_arrive":"2022-11-23","date_departure":"2022-11-29","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 20:19:14] INFO Processing new AJAX Request width payload {"location_id":"2","date_arrive":"2022-10-05","date_departure":"2022-11-29","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 20:21:54] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:22:00] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:22:00] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:22:01] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:22:19] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:22:23] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:22:30] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:22:45] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:22:51] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:23:20] INFO Processing new AJAX Request width payload {"location_id":"2","date_arrive":"2023-10-05","date_departure":"2023-10-29","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 20:23:51] INFO Processing new AJAX Request width payload {"location_id":"2","date_arrive":"2025-06-05","date_departure":"2022-08-26","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-08-26 20:24:04] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:24:20] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:31:26] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:31:42] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:31:47] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:31:50] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-08-26 20:32:46] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}