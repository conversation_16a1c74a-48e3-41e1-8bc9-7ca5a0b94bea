(function (e, w) {
  'use strict';
  var $ = e,
    wrs = w.wrs || {},
    admLocation,
    admDateArrival,
    admDateDeparture,
    admProduct,
    editProductId,
    state = { ...(wrs.state || {}) };

  wrs.state = function () {
    return state;
  };

  wrs.isBookNow = function () {
    return w.location.pathname.indexOf('book-now') !== -1;
  };

  wrs.isAdminReservationNew = function () {
    return (
      w.location.href.indexOf('wodaabe-reservations-reservations') !== -1 &&
      (w.location.href.indexOf('new') !== -1 ||
        w.location.href.indexOf('edit') !== -1)
    );
  };

  wrs.initFields = function () {
    admLocation = $('#adm-location');
    admDateArrival = $('#adm-date_arrive');
    admDateDeparture = $('#adm-date_departure');
    admProduct = $('#adm-product');
    editProductId = $('#edit_product_id');
    wrs.registerListener();
    wrs.onLocation();
  };

  wrs.onLocation = function () {
    const loc = admLocation.val();
    const dt1 = admDateArrival.val();
    const dt2 = admDateDeparture.val();
    admProduct.empty();
    admProduct.val('');

    if (loc && dt1 && dt2) {
      wrs
        .getProducts({
          location_id: loc,
          date_arrive: dt1,
          date_departure: dt2,
        })
        .then(function (res) {
          console.log(res);
          if (Array.isArray(res) && res.length > 0) {
            $('.location').show();
            admProduct.append(
              res
                .sort(function (a, b) {
                  return ('' + a.attr).localeCompare(b.attr);
                })
                .map(function (a) {
                  if (editProductId) {
                    if (String(editProductId.val()) === String(a.id)) {
                      return `<option value="${a.id}" selected="truue">${a.title}</option>`;
                    }
                  }
                  return `<option value="${a.id}">${a.title}</option>`;
                })
                .join('')
            );
          } else {
            $('.location').hide();
          }
        });
    } else {
      $('.location').hide();
    }
  };
  wrs.registerListener = function () {
    if (admLocation && admDateArrival && admDateDeparture && admProduct) {
      admLocation.on('change', function (e) {
        wrs.onLocation();
      });
      admDateArrival.on('change', function (e) {
        wrs.onLocation();
      });
      admDateDeparture.on('change', function (e) {
        wrs.onLocation();
      });
    }
  };
  $(w.document).ready(function () {
    if (wrs.isBookNow()) wrs.getLocations();
    if (wrs.isAdminReservationNew()) wrs.initFields();
  });

  w.wrs = wrs;
})(jQuery, window);
