<?php

/**
 * WodaabeReservations_Table class will create the page to load the table
 */
class WodaabeReservations_Table {

  /**
   * Display the list table page
   *
   * @return Void
   */
  public function list_table_page() {
    if (isset($_GET["action"])) {
      $action = $_GET["action"];
      wrs_log("Action: " . $action);
      if ("show" == $action) {
        wrs_log("Showing details page...");
        return $this->show_details_page();
      } else if ("new" == $action) {
        return $this->show_create_page();
      } else if ("delete" == $action) {
        return $this->do_delete();
      } else if ("edit" == $action) {
        return $this->show_edit_page();
      } else if ("send" == $action) {
        return $this->send_email();
      }
    }
    wrs_log("list_table_page");
    $listTable = new WodaabeReservations_List_Table();
    $listTable->prepare_items();
    $new_link = $_SERVER['REQUEST_URI'] . "&action=new";
    ?>
      <div class="wrap">
          <h2 style="display: inline-block;">Reservations</h2>
          <a style="display: inline-block;" href="<?php echo $new_link ?>" class="page-title-action">Add New</a>
          <p>Find below the available reservations that has been done by your customers.</p>
          <?php $listTable->display();?>
      </div>
    <?php
}

  public function send_email() {
    global $wpdb, $wrsModule;
    $id = $_GET["id"];
    $wrsModule->send_mails($wrsModule->get_reservation($id));
    wp_redirect("/wp-admin/admin.php?page=wodaabe-reservations-reservations");
  }

  public function do_delete() {
    global $wpdb, $wrsModule;
    $id = $_GET["id"];

    $reservation = $wrsModule->get_reservation($id);
    wrs_log("Deleting reservation: " . print_r($reservation, true));

    // Get the reservation items to find the product
    $reservation_items = $wrsModule->get_reservation_items($id);
    if (!empty($reservation_items)) {
        $product_id = $reservation_items[0]['product_id'];

        // Check if the product has a listingMapId
        $product = $wrsModule->get_by_id(WODAABE_RESERVATIONS_PRODUCT_TABLE, $product_id);

        if (!empty($product['listingMapId'])) {
            wrs_log("Product has Hostaway listing ID: " . $product['listingMapId']);

            // First try to find the reservation in the configuration table
            $config_key = 'hostaway_reservation_' . $id;
            $hostaway_id = $wrsModule->get_configuration($config_key);

            if (empty($hostaway_id)) {
                // If not found in configuration, find the Hostaway reservation ID using the improved function
                // with the listingMapId parameter for more accurate matching
                $hostaway_id = $wrsModule->get_hostaway_reservation_id(
                    $reservation->date_arrive,
                    $reservation->date_departure,
                    $product['listingMapId']
                );
            }

            wrs_log("Hostaway ID: " . ($hostaway_id ? $hostaway_id : "Not found"));

            if ($hostaway_id) {
                // Cancel the Hostaway reservation instead of deleting it
                $result = $wrsModule->cancel_hostaway_reservation($hostaway_id);
                wrs_log("Hostaway cancellation result: " . print_r($result, true));
            }
        }
    }

    // Update the reservation status to 'Canceled' before deleting
    $reservation_table_name = $wpdb->prefix . 'wrs_reservations';
    $wpdb->update(
      $reservation_table_name,
      ['status' => 'Canceled'],
      ['id' => $id]
    );

    // Send cancellation notification email if needed
    try {
        $wrsModule->sendMail(
            'Réservation annulée',
            'reservation_cancelled',
            $id
        );
        wrs_log("Cancellation notification email sent for reservation #" . $id);
    } catch (\Throwable $e) {
        wrs_log("Failed to send cancellation notification: " . $e->getMessage());
        // Continue with deletion even if email fails
    }

    // Then delete it from the database
    $reservation_table_name = $wpdb->prefix . 'wrs_reservations';
    $wpdb->delete(
      $reservation_table_name,
      ['id' => $id],
      ['%d']
    );

    // Update calendars after deletion
    wrs_log("Updating product reservation calendars after deleting reservation " . $id);
    if (method_exists($wrsModule, 'update_product_reservation_calendars')) {
        $wrsModule->update_product_reservation_calendars();
    } else {
        $calendar_table = new WodaabeReservationsCalendar_Table();
        $calendar_table->update_reservation_calendars();
    }

    // Redirect back to the reservations list
    header("Location: /wp-admin/admin.php?page=wodaabe-reservations-reservations");
    exit;
  }

  public function show_create_page() {
    global $wpdb, $wrsModule;

    if ("POST" == $_SERVER['REQUEST_METHOD']) {
      try {
        $wpdb->show_errors();
        $wpdb->query('START TRANSACTION');

        $person_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE;
        $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
        $reservation_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
        $reservation_item_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;

        $wpdb->insert($person_table_name,
          array(
            'last_name' => $_POST['last_name'],
            'email' => $_POST['email'],
            'country' => 1,
            'comment' => "",
            'is_guess' => 0,
            'buyer_last_name' => $_POST['guest_last_name'],
            'buyer_email' => $_POST['guest_email'],
            'agree_policy' => 0,
            'receive_marketing_update' => 0,
          )
        );
        $personId = $wpdb->insert_id;

        $wpdb->insert($payment_table_name,
          array(
            'transaction_id' => $_POST['transaction_id'],
            'status' => empty($_POST['transaction_id']) ? 'CREATED' : "COMPLETED",
          )
        );
        $payId = $wpdb->insert_id;

        $reservationUuid = wrs_uuid();
        $wpdb->insert($reservation_table_name,
          array(
            'date_arrive' => $_POST['date_arrive'],
            'date_departure' => $_POST['date_departure'],
            'uuid' => $reservationUuid,
            'adult_count' => $_POST['adult_count'],
            'child_count' => $_POST['child_count'],
            'person_id' => $personId,
            'language_id' => 1,
            'currency_id' => 1,
            'payment_id' => $payId,
            'status' => 'Active',
          )
        );
        $reservationId = $wpdb->insert_id;

        $wpdb->insert($reservation_item_table_name,
          array(
            'quantity' => 1,
            'product_id' => intval($_POST['product_id']),
            'reservation_id' => intval($reservationId),
          )
        );

        wrs_log($wpdb->last_error, 'INFO');
        $wpdb->query('COMMIT');

        wrs_log("Updating product reservation calendars after deleting reservation " . $id);
    	$calendar_table = new WodaabeReservationsCalendar_Table();
        $calendar_table->update_reservation_calendars();
      } catch (\Throwable $th) {
        wrs_log($th->__toString(), 'ERROR');
        $wpdb->query('ROLLBACK');
      }
      wp_redirect("/wp-admin/admin.php?page=wodaabe-reservations-reservations");
    }
    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE;

    $locations = $wpdb->get_results($sql, 'ARRAY_A');

    ?>
    <div class="wrap">
<form method="POST">
  <h2>Reservations</h2>
  <p>Here you can create a reservation </p>
  <table class="wp-list-table widefat fixed striped table-view-excerpt reservations_page_wodaabe-reservations-reservations">


<tbody id="the-list">
<tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Location
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <select id="adm-location" name="location" style="width: 80%">
      <option value=""> Selectionner ... </option>
      <?php foreach ($locations as $value) {?>
      <option value="<?php echo $value["id"] ?>"> #<?php echo $value["id"] ?> <?php echo $value["title"] ?></option>
      <?php }?>
    </select>
  </td>
</tr>
<tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Date arrival
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input id="adm-date_arrive" value="" type="date" style="width: 50%" name="date_arrive" />
  </td>
</tr>
<tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Date departure
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input id="adm-date_departure" value="" type="date" style="width: 50%" name="date_departure" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Product
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <select id="adm-product" name="product_id" style="width: 80%"></select>
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Adult count
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="" type="number" style="width: 50%" name="adult_count" min="0" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Child count
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="" type="number" style="width: 50%" name="child_count" min="0" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Reférence paiement
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="" type="text" style="width: 50%" name="transaction_id" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Guest reservation?
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <select name="product" style="width: 80%">
      <option value="1"> No, I'm booking for myself</option>
      <option value="2"> Yes, I'm booking for someone else</option>
    </select>
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Client name
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="" type="text" style="width: 50%" name="last_name" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Client email
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="" type="email" style="width: 50%" name="email" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Guest name
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="" type="text" style="width: 50%" name="guest_last_name" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Guest email
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="" type="email" style="width: 50%" name="guest_email" />
  </td>
</tr >
  </tboby>
  </table>
  <div  class="location" style="margin-top:1rem; display: none">
    <input type="submit" name="publish" id="publish" class="button button-primary button-large" value="Publish">
  </div>
  </form>
    </div>
 <?php
}
  public function show_edit_page() {
    global $wpdb, $wrsModule;
    $id = $_GET["id"];

    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_TABLE . " WHERE id = " . $id;

    $item = $wpdb->get_row($sql, 'ARRAY_A');

    $sql2 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE . " WHERE id = " . $item["payment_id"];

    $item2 = $wpdb->get_row($sql2, 'ARRAY_A');

    $sql3 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE . " WHERE id = " . $item["person_id"];

    $item3 = $wpdb->get_row($sql3, 'ARRAY_A');

    $sql4 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE . " WHERE reservation_id = " . $item["id"];

    $item4 = $wpdb->get_row($sql4, 'ARRAY_A');

    $sql5 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE . " WHERE id = " . $item4["product_id"];

    $item5 = $wpdb->get_row($sql5, 'ARRAY_A');

    if ("POST" == $_SERVER['REQUEST_METHOD']) {
      try {
        $wpdb->show_errors();
        $wpdb->query('START TRANSACTION');

        $person_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE;
        $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
        $reservation_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
        $reservation_item_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;

        $wpdb->update($person_table_name,
          array(
            'last_name' => $_POST['last_name'],
            'email' => $_POST['email'],
            'is_guess' => $_POST['is_guess'],
            'buyer_last_name' => $_POST['guest_last_name'],
            'buyer_email' => $_POST['guest_email'],
          ),
          array('id' => $item3["id"])
        );
        $personId = $wpdb->insert_id;

        $wpdb->update($payment_table_name,
          array(
            'transaction_id' => $_POST['transaction_id'],
            'status' => empty($_POST['transaction_id']) ? 'CREATED' : "COMPLETED",
          ),
          array('id' => $item2["id"])
        );
        $payId = $wpdb->insert_id;

        $reservationUuid = wrs_uuid();
        $wpdb->update($reservation_table_name,
          array(
            'date_arrive' => $_POST['date_arrive'],
            'date_departure' => $_POST['date_departure'],
            'adult_count' => $_POST['adult_count'],
            'child_count' => $_POST['child_count'],
            'status' => $_POST['status'],
          ),
          array('id' => $item["id"])
        );
        $reservationId = $wpdb->insert_id;

        $wpdb->update($reservation_item_table_name,
          array(
            'product_id' => intval($_POST['product_id']),
          ),
          array('id' => $item4["id"])
        );

        wrs_log($wpdb->last_error, 'INFO');
        $wpdb->query('COMMIT');

      } catch (\Throwable $th) {
        wrs_log($th->__toString(), 'ERROR');
        $wpdb->query('ROLLBACK');
      }
      wp_redirect("/wp-admin/admin.php?page=wodaabe-reservations-reservations");
    }

    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE;

    $locations = $wpdb->get_results($sql, 'ARRAY_A');

    ?>
    <div class="wrap">
<form method="POST">
  <h2>Reservations</h2>
  <p>Here you can edit a reservation </p>
  <table class="wp-list-table widefat fixed striped table-view-excerpt reservations_page_wodaabe-reservations-reservations">


<tbody id="the-list">
<tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Location
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <select id="adm-location" name="location" style="width: 80%">
    <option value=""> Selectionner ... </option>
    <?php foreach ($locations as $value) {?>
      <option value="<?php echo $value["id"] ?>" <?php if ($value["id"] == $item5["location_id"]) {?> selected="true" <?php }?>> #<?php echo $value["id"] ?> <?php echo $value["title"] ?></option>
      <?php }?>

    </select>
  </td>
</tr>
<tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Date arrival
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input id="adm-date_arrive" value="<?php echo $item["date_arrive"] ?>" type="date" style="width: 50%" name="date_arrive" />
  </td>
</tr>
<tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Date departure
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input id="adm-date_departure" value="<?php echo $item["date_departure"] ?>" type="date" style="width: 50%" name="date_departure" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Product
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input type="hidden" id="edit_product_id" value="<?php echo $item4["product_id"] ?>" />
    <select id="adm-product" name="product_id" style="width: 80%">
    <?php foreach ($products as $value) {?>
     <option value="<?php echo $value["id"] ?>" <?php if ($item4["product_id"] == $value["id"]) {?> selected="true" <?php }?>> #<?php echo $value["id"] ?> <?php echo $value["title"] ?></option>
     <?php }?>
    </select>
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Adult count
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="<?php echo $item["adult_count"] ?>" type="number" style="width: 50%" name="adult_count" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Child count
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="<?php echo $item["child_count"] ?>" type="number" style="width: 50%" name="child_count" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Reférence paiement
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="<?php echo $item2["transaction_id"] ?>" type="text" style="width: 50%" name="transaction_id" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Guest reservation?
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <select name="is_guess" style="width: 80%">
      <option value="0" <?php if ($item3["is_guess"] == 0) {?> selected="true" <?php }?>> No, I'm booking for myself</option>
      <option value="1" <?php if ($item3["is_guess"] == 1) {?> selected="true" <?php }?>> Yes, I'm booking for someone else</option>
    </select>
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Client name
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="<?php echo $item3["last_name"] ?>" type="text" style="width: 50%" name="last_name" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Client email
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="<?php echo $item3["email"] ?>" type="email" style="width: 50%" name="email" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Guest name
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="<?php echo $item3["buyer_last_name"] ?>" type="text" style="width: 50%" name="guest_last_name" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Guest email
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <input value="<?php echo $item3["buyer_email"] ?>" type="email" style="width: 50%" name="guest_email" />
  </td>
</tr>
<tr class="location" style=" display: none">
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      Booking State
    </strong>
  </td>
  <td class="date_arrive column-date_arrive">
    <select name="status" style="width: 80%">
      <option value="Incoming" <?php if ($item["status"] == "Incoming") {?> selected="true" <?php }?>>Incoming</option>
      <option value="Active" <?php if ($item["status"] == "Active") {?> selected="true" <?php }?>>Active</option>
      <option value="Completed" <?php if ($item["status"] == "Completed") {?> selected="true" <?php }?>>Completed</option>
    </select>
  </td>
</tr>
  </tboby>
  </table>
  <div class="location" style="margin-top:1rem; display: none">
    <input type="submit" name="publish" id="publish" class="button button-primary button-large" value="Publish">
  </div>
  </form>
    </div>
 <?php
}

  public function show_details_page() {
    wrs_log("Showing details page...");
    global $wpdb, $wrsModule;

    //$wrs = new Wodaabe_Reservations();
    //$wrs->get_hostaway_token();
    //$wrs->delete_hostaway_reservation();

    $id = $_GET["id"];

    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_TABLE . " WHERE id = " . $id;

    $item = $wpdb->get_row($sql, 'ARRAY_A');
    wrs_log("Item: ");
    wrs_log(print_r($item, true));
    $sql2 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE . " WHERE id = " . $item["payment_id"];

    $item2 = $wpdb->get_row($sql2, 'ARRAY_A');

    $sql3 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE . " WHERE id = " . $item["person_id"];

    $item3 = $wpdb->get_row($sql3, 'ARRAY_A');

    $sql4 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE . " WHERE id = " . $item["product_id"];

    $product = $wrsModule->get_reservation_items($id);

    $items = array(
      '#' => $item["id"],
      'UUID' => $item["uuid"],
      'Date arrival' => $item["date_arrive"],
      'Date departure' => $item["date_departure"],
      'Adult count' => $item["adult_count"],
      'Child count' => $item["child_count"],
      'Status' => $item["status"],
      'Product' => $product[0]["product"]["title"],
      'Language' => "English",
      'Currency' => "EURO",
      'Payment Status' => $item2["status"],
      'Payment ID' => !empty($item2["transaction_id"]) ? $item2["transaction_id"] : "-",
      'Client Name' => $item3["first_name"] . ' ' . $item3["last_name"],
      'Client Email' => $item3["email"],
      'Client Phone' => $item3["phone"],
    );

    ?>
    <div class="wrap">
        <h2>Reservations</h2>
        <p>Find below the details about the reservations #<?php echo $id ?> </p>
        <table class="wp-list-table widefat fixed striped table-view-excerpt reservations_page_wodaabe-reservations-reservations">


	<tbody id="the-list">
    <?php
foreach ($items as $key => $value) {?>
      	<tr>
        <td class=" has-row-actions column-primary" data-colname="ID">
          <strong>
            <?php echo $key ?>
          </strong>
        </td>
        <td class="date_arrive column-date_arrive" data-colname="Arrival date">
          <?php echo $value ?>
        </td>
        <?php }?>
      </table>
    </div>
  <?php
}

}

// WP_List_Table is not loaded automatically so we need to load it in our application
if (!class_exists('WP_List_Table')) {
  require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';
}

/**
 * Create a new table class that will extend the WP_List_Table
 */
class WodaabeReservations_List_Table extends WP_List_Table {

  /**
   * Returns the count of records in the database.
   *
   * @return null|string
   */
  public static function record_count() {
    global $wpdb;

    $sql = "SELECT COUNT(*) FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
    return $wpdb->get_var($sql);
  }

  /**
   * Retrieve customer’s data from the database
   *
   *
   * @return mixed
   */
  public function get_items($orderby = "p.id", $order = "desc") {
    global $wpdb, $wrsModule;

    $columns = array('id', 'titre', 'picture', 'btn_text', 'link', 'date_add', 'date_upd');
    if (!in_array($orderby, $columns)) {
      $orderby = "p.id";
    } else {
      $orderby = "p." . $orderby;
    }
    $orders = array('asc', 'desc');
    if (!in_array($order, $orders)) {
      $order = "desc";
    }

    $sql = "SELECT p.*, c.last_name, c.email FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_TABLE . " p, " . $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE . " c GROUP BY p.id  ORDER BY " . $orderby . " " . $order;

    $result = $wpdb->get_results($sql, 'ARRAY_A');

    return $result;
  }

  /**
   * Prepare the items for the table to process
   *
   * @return Void
   */
  public function prepare_items() {

    $columns = $this->get_columns();
    $hidden = $this->get_hidden_columns();
    $sortable = $this->get_sortable_columns();

    $perPage = 10;
    $currentPage = $this->get_pagenum();

    // Set defaults
    $orderby = 'id';
    $order = 'desc';

    // If orderby is set, use this as the sort column
    if (!empty($_GET['orderby'])) {
      $orderby = $_GET['orderby'];
    }

    // If order is set use this as the order
    if (!empty($_GET['order'])) {
      $order = $_GET['order'];
    }

    $data = $this->get_items($orderby, $order);

    $data = is_array($data) ? $data : array();
    $totalItems = $this->record_count();

    $this->set_pagination_args(array(
      'total_items' => $totalItems,
      'per_page' => $perPage,
    ));

    $data = array_slice($data, (($currentPage - 1) * $perPage), $perPage);

    $this->_column_headers = array($columns, $hidden, $sortable);
    $this->items = $data;
  }

  /**
   * Override the parent columns method. Defines the columns to use in your listing table
   *
   * @return Array
   */
  public function get_columns() {
    $columns = array(
      'id' => 'ID',
      'date_arrive' => 'Arrival date',
      'date_departure' => 'Departure date',
      'title' => 'Location',
      'status' => 'Booking State',
      'payment_status' => 'Payment Status',
      'adult_count' => 'Adults',
      'child_count' => 'Children',
      'email' => 'Email',
      'nom' => 'Last name',
      'prenom' => 'First name',
      'date_add' => 'Reservation date',
    );
    return $columns;
  }

  /**
   * Define which columns are hidden
   *
   * @return Array
   */
  public function get_hidden_columns() {
    return array();
  }

  /**
   * Define the sortable columns
   *
   * @return Array
   */
  public function get_sortable_columns() {
    return array(
      'id' => array('id', false),
      'date_add' => array('date_add', false),
      'date_arrive' => array('titre', false),
      'title' => array('title', false),
      'nom' => array('nom', false),
      'prenom' => array('prenom', false),
    );
  }

  /**
   * Define what data to show on each column of the table
   *
   * @param  Array $item        Data
   * @param  String $column_name - Current column name
   *
   * @return Mixed
   */
  public function column_default($item, $column_name) {
    global $wpdb;

    switch ($column_name) {
    case 'id':
      return "<strong>#{$item[$column_name]}</strong>";
    case 'status':
      if ("Active" == $item[$column_name]) {
        return "<strong style='color: green;'>{$item[$column_name]}</strong>";
      }
      if ("Completed" == $item[$column_name]) {
        return "<strong style='color: red;'>{$item[$column_name]}</strong>";
      }
      return "<strong>{$item[$column_name]}</strong>";
    case 'payment_status':
      $sql4 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE . " WHERE id = " . $item["payment_id"];
      $column_name = 'status';
      $item4 = $wpdb->get_row($sql4, 'ARRAY_A');
      if ("PENDING" == $item4[$column_name]) {
        return "<strong style='color: orange;'>{$item4[$column_name]}</strong>";
      }
      if ("COMPLETED" == $item4[$column_name]) {
        return "<strong style='color: green;'>{$item4[$column_name]}</strong>";
      }
      return "<strong>{$item4[$column_name]}</strong>";
    case 'nom':
      $sql3 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE . " WHERE id = " . $item["person_id"];

      $item3 = $wpdb->get_row($sql3, 'ARRAY_A');
      $item3["last_name"] = !empty($item3["last_name"]) ? $item3["last_name"] : "-";

      return "<strong>{$item3["last_name"]}</strong>";
    case 'prenom':
      $sql3 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE . " WHERE id = " . $item["person_id"];

      $item3 = $wpdb->get_row($sql3, 'ARRAY_A');

      $item3["first_name"] = !empty($item3["first_name"]) ? $item3["first_name"] : "-";

      return "<strong>{$item3["first_name"]}</strong>";
    case 'email':
      $sql3 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE . " WHERE id = " . $item["person_id"];

      $item3 = $wpdb->get_row($sql3, 'ARRAY_A');
      $item3["email"] = !empty($item3["email"]) ? $item3["email"] : "-";

      return "<strong>{$item3["email"]}</strong>";

    case 'date_add':
      return $item[$column_name] ? \date("d/m/Y H:i:s", \strtotime($item[$column_name])) : "<b>-</b>";

    case 'date_departure':
      return $item[$column_name] ? \date("d/m/Y", \strtotime($item[$column_name])) : "<b>-</b>";

    default:
      return $item[$column_name];
    }
  }

  public function column_title($item) {
    global $wpdb;
    // Retrieve the product Id from the database for the current item
    $sqlItem = "SELECT product_id FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE . " WHERE reservation_id = " . $item["id"];
    wrs_log("sqlItem: " . $sqlItem);
    $prodId = $wpdb->get_row($sqlItem, 'ARRAY_A');
    wrs_log("prodId:");
    wrs_log(print_r($prodId, true));

    $sqlLocation = "SELECT location_id FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE . " WHERE id = " . $prodId['product_id'];
    wrs_log("sqlLocation: " . $sqlLocation);
    $locationId = $wpdb->get_row($sqlLocation, 'ARRAY_A');
    wrs_log("locationId:");
    wrs_log(print_r($locationId, true));

    $sqlLocationName = "SELECT title FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE . " WHERE id = " . $locationId['location_id'];
    wrs_log("sqlLocationName: " . $sqlLocationName);
    $locationName = $wpdb->get_row($sqlLocationName, 'ARRAY_A');
    wrs_log("locationName:");
    wrs_log(print_r($locationName, true));


    // Display the value in the table
    return $locationName['title'];
  }

  public function column_date_arrive($item) {
    wrs_log("Item:");
    wrs_log(print_r($item, true));
    $path = $_SERVER['REQUEST_URI'];
    $edit_link = $path . "&action=edit&id=" . $item["id"];
    $view_link = $path . "&action=show&id=" . $item["id"];
    $delete_link = $path . "&action=delete&id=" . $item["id"];
    $send_link = $path . "&action=send&id=" . $item["id"];
    $output = '';
    $dt = $item["date_arrive"] ? \date("d/m/Y", \strtotime($item["date_arrive"])) : "<b>-</b>";

    // Title.
    $output .= '<strong><a href="' . esc_url($edit_link) . '" class="row-title">' . $dt . '</a></strong>';

    // Get actions.
    $actions = array(
      'view' => '<a href="' . esc_url($view_link) . '">' . esc_html__('View', 'my_plugin') . '</a>',
      'edit' => '<a href="' . esc_url($edit_link) . '">' . esc_html__('Edit', 'my_plugin') . '</a>',
      'send' => '<a href="' . esc_url($send_link) . '">' . esc_html__('Send email', 'my_plugin') . '</a>',
      'delete' => '<a href="' . esc_url($delete_link) . '">' . esc_html__('Delete', 'my_plugin') . '</a>',
    );

    $row_actions = array();

    foreach ($actions as $action => $link) {
      $row_actions[] = '<span class="' . esc_attr($action) . '">' . $link . '</span>';
    }

    $output .= '<div class="row-actions visible">' . implode(' | ', $row_actions) . '</div>';

    return $output;
  }

  /**
   * Allows you to sort the data by the variables set in the $_GET
   *
   * @return Mixed
   */
  private function sort_data($a, $b) {
    // Set defaults
    $orderby = 'id';
    $order = 'desc';

    // If orderby is set, use this as the sort column
    if (!empty($_GET['orderby'])) {
      $orderby = $_GET['orderby'];
    }

    // If order is set use this as the order
    if (!empty($_GET['order'])) {
      $order = $_GET['order'];
    }

    $result = strcmp($a[$orderby], $b[$orderby]);

    if ($order === 'asc') {
      return $result;
    }

    return -$result;
  }
}
?>