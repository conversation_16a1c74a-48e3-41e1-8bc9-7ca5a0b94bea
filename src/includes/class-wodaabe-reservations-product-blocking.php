<?php

class WodaabeReservationsProductBlocking extends WP_List_Table {
    private $table_name;

    public function __construct() {
        global $wpdb;
        parent::__construct();
        $this->table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCTS_BLOCK_TABLE;
    }
    
    public function prepare_items() {
        $columns = $this->get_columns();
        $hidden = $this->get_hidden_columns();
        $sortable = $this->get_sortable_columns();
    
        $this->_column_headers = array($columns, $hidden, $sortable);
    
        $per_page = 10;
        $current_page = $this->get_pagenum();
        $total_items = $this->record_count();
    
        $this->set_pagination_args(array(
            'total_items' => $total_items,
            'per_page' => $per_page,
        ));
    
        $data = $this->get_items($current_page, $per_page);
        $this->items = $data;
    }
    
    public function get_columns() {
        $columns = array(
            'cb' => '<input type="checkbox" />',
            'id' => 'ID',
            'product_id' => 'Product',
            'comment' => 'Justification',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'date_add' => 'Date Added',
            'date_upd' => 'Last Updated',
            'actions' => 'Actions'
        );
        return $columns;
    }
    
    public function get_hidden_columns() {
        return array();
    }
    
    public function get_sortable_columns() {
        return array(
            'id' => array('id', false),
            'product_id' => array('product_id', false),
            'start_date' => array('start_date', false),
            'end_date' => array('end_date', false),
            'date_add' => array('date_add', false),
            'date_upd' => array('date_upd', false),
        );
    }
    
    public function get_items($current_page = 1, $per_page = 10) {
        global $wpdb;
    
        $sql = "SELECT * FROM {$this->table_name}";
        $total_items = $this->record_count();
    
        $sql .= $wpdb->prepare(" LIMIT %d, %d", ($current_page - 1) * $per_page, $per_page);
        $data = $wpdb->get_results($sql, ARRAY_A);
    
        return $data;
    }
    
    public function record_count() {
        global $wpdb;
        $sql = "SELECT COUNT(*) FROM {$this->table_name}";
        return $wpdb->get_var($sql);
    }
    
    public function column_default($item, $column_name) {
        switch ($column_name) {
            case 'id':
            case 'product_id':
            case 'start_date':
            case 'end_date':
            case 'date_add':
            case 'date_upd':
            case 'comment':
                return $item[$column_name];
            case 'actions':
                return $this->column_actions($item);
            default:
                return print_r($item, true);
        }
    }
    
    public function column_cb($item) {
        return sprintf(
            '<input type="checkbox" name="id[]" value="%s" />',
            $item['id']
        );
    }
    
    public function column_product_id($item) {
        global $wpdb;
        $product = $wpdb->get_row($wpdb->prepare("SELECT title FROM {$wpdb->prefix}wrs_products WHERE id = %d", $item['product_id']), ARRAY_A);
        if ($product) {
            return $product['title'];
        } else {
            return '-';
        }
    }
    
    public function column_actions($item) {
        $edit_url = admin_url('admin.php?page=wodaabe-reservations-product-blocking&action=edit&id=' . $item['id']);
        $delete_url = wp_nonce_url(admin_url('admin.php?page=wodaabe-reservations-product-blocking&action=delete&id=' . $item['id']), 'delete_product_block_' . $item['id']);
        
        $edit_button = '<a href="' . esc_url($edit_url) . '" class="button button-secondary">Edit</a>';
        $delete_button = '<a href="' . esc_url($delete_url) . '" class="button button-secondary" onclick="return confirm(\'Are you sure you want to delete this item?\')">Delete</a>';
        
        return $edit_button . ' ' . $delete_button;
    }
    
    public function get_bulk_actions() {
        $actions = array(
            'delete' => 'Delete',
        );
        return $actions;
    }
    
    public function process_bulk_action() {
        global $wpdb;
    
        // Bulk delete action
        if ('delete' === $this->current_action() && isset($_REQUEST['id'])) {
            $ids = array_map('intval', (array) $_REQUEST['id']);
            $wpdb->query("DELETE FROM {$this->table_name} WHERE id IN (" . implode(',', $ids) . ")");
        }
    
        // Single delete action
        if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
            $id = intval($_GET['id']);
            
            // Verify nonce before deleting
            if (check_admin_referer('delete_product_block_' . $id)) {
                $wpdb->delete($this->table_name, array('id' => $id));
                
                // Redirect to prevent accidental re-submission
                wp_redirect(admin_url('admin.php?page=wodaabe-reservations-product-blocking'));
                exit;
            }
        }
    }

    public function check_blocking_availability($product_id, $start_date, $end_date) {
        global $wpdb;
        
        // Validate input dates
        $start_date = date('Y-m-d', strtotime($start_date));
        $end_date = date('Y-m-d', strtotime($end_date));
        
        // Check for overlapping reservations
        $reservation_sql = $wpdb->prepare(
            "SELECT p.id, p.date_arrive, p.date_departure, p.status, l.product_id
            FROM {$wpdb->prefix}" . WODAABE_RESERVATIONS_TABLE . " p
            JOIN {$wpdb->prefix}" . WODAABE_RESERVATIONS_ITEM_TABLE . " l ON p.id = l.reservation_id
            WHERE p.status = 'Active'
            AND l.product_id = %d
            AND (
                (p.date_arrive >= %s AND p.date_arrive <= %s)
                OR (p.date_departure >= %s AND p.date_departure <= %s)
                OR (p.date_arrive < %s AND p.date_departure > %s)
            )",
            $product_id, $start_date, $end_date, $start_date, $end_date, $start_date, $end_date
        );
        $overlapping_reservations = $wpdb->get_results($reservation_sql, ARRAY_A);
        
        // Check for existing blocked periods
        $blocking_sql = $wpdb->prepare(
            "SELECT id, start_date, end_date, comment 
            FROM {$wpdb->prefix}" . WODAABE_RESERVATIONS_PRODUCTS_BLOCK_TABLE . "
            WHERE product_id = %d
            AND (
                (start_date >= %s AND start_date <= %s)
                OR (end_date >= %s AND end_date <= %s)
                OR (start_date < %s AND end_date > %s)
            )",
            $product_id, $start_date, $end_date, $start_date, $end_date, $start_date, $end_date
        );
        $blocked_periods = $wpdb->get_results($blocking_sql, ARRAY_A);
        
        // Prepare detailed error messages
        $errors = [];
        
        if (!empty($overlapping_reservations)) {
            $reservation_details = [];
            foreach ($overlapping_reservations as $reservation) {
                $reservation_details[] = sprintf(
                    "Reservation from %s to %s",
                    $reservation['date_arrive'], 
                    $reservation['date_departure']
                );
            }
            $errors[] = "Cannot block period. Overlapping reservations exist: " . 
                        implode(', ', $reservation_details);
        }
        
        if (!empty($blocked_periods)) {
            $block_details = [];
            foreach ($blocked_periods as $period) {
                $block_details[] = sprintf(
                    "Blocked period from %s to %s (Reason: %s)", 
                    $period['start_date'], 
                    $period['end_date'], 
                    $period['comment']
                );
            }
            $errors[] = "Cannot block period. Overlapping blocked periods exist: " . 
                        implode(', ', $block_details);
        }
        
        // Return false if there are any overlaps, otherwise return true
        return [
            'available' => empty($errors),
            'errors' => $errors
        ];
    }
    
    public function add_page() {
        wrs_log("_SERVER['REQUEST_METHOD'] : ");
        wrs_log($_SERVER['REQUEST_METHOD']);
        wrs_log(isset($_POST['save']));
        
        if ('POST' === $_SERVER['REQUEST_METHOD'] && isset($_POST['save'])) {
            global $wpdb;
            $product_id = intval($_POST['product_id']);
            $start_date = sanitize_text_field($_POST['start_date']);
            $end_date = sanitize_text_field($_POST['end_date']);
            $comment = sanitize_textarea_field($_POST['comment']);
            
             // Check availability of the blocking period
            $availability_check = $this->check_blocking_availability($product_id, $start_date, $end_date);
            
            if ($availability_check['available']) {
                // Insert the blocking period
                $insert_result = $wpdb->insert($this->table_name, array(
                    'product_id' => $product_id,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'comment' => $comment,
                ));
                
                if ($insert_result) {
                    // Successful insertion
                    wp_redirect(admin_url('admin.php?page=wodaabe-reservations-product-blocking&message=success'));
                    exit;
                } else {
                    // Database insertion failed
                    wp_redirect(admin_url('admin.php?page=wodaabe-reservations-product-blocking&message=db_error'));
                    exit;
                }
            } else {
                // Availability check failed
                // Store errors in a session or pass as a URL parameter
                wrs_log($availability_check['errors']);
                set_transient('wodaabe_error_message', implode(' | ', $availability_check['errors']), 60); // Expires in 60 seconds

                //$error_message = urlencode(implode(' ', $availability_check['errors']));
                wp_redirect(admin_url('admin.php?page=wodaabe-reservations-product-blocking&action=add'));
                exit;
            }
        }
        
        // Render the add form
        $this->render_form();
    }
    
    public function edit_page() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save'])) {
            global $wpdb;
            $id = intval($_POST['id']);
            $product_id = intval($_POST['product_id']);
            $start_date = sanitize_text_field($_POST['start_date']);
            $end_date = sanitize_text_field($_POST['end_date']);
            $comment = sanitize_textarea_field($_POST['comment']);
    
            $wpdb->update($this->table_name, array(
                'product_id' => $product_id,
                'start_date' => $start_date,
                'end_date' => $end_date,
                'comment' => $comment,
            ), array('id' => $id));
    
            wp_redirect(admin_url('admin.php?page=wodaabe-reservations-product-blocking'));
            exit;
        }
    
        // Retrieve existing block data and render the edit form
        global $wpdb;
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        $block = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->table_name} WHERE id = %d", $id), ARRAY_A);
    
        $this->render_form($block);
    }
    
    public function render_form($block = null) {
        global $wpdb;
        // Check for error messages
        $error_message = get_transient('wodaabe_error_message');

        

        $products = $wpdb->get_results("SELECT id, title FROM {$wpdb->prefix}wrs_products", ARRAY_A);
    
        $is_edit = $block !== null;
        $action_url = admin_url('admin.php?page=wodaabe-reservations-product-blocking');
        $action_url .= $is_edit ? '&action=edit&id=' . esc_attr($block['id']) : '&action=add';
        ?>
        
        <div class="wrap">
            <h1><?php echo $is_edit ? 'Edit Product Block' : 'Add New Product Block'; ?></h1>
    
            <?php 
                // Display error messages if any
                if ($error_message) {
                    wrs_log($error_message);
                    echo '<div class="notice notice-error" style="display: block!important;"><p>' . esc_html($error_message) . '</p></div>';
                    delete_transient('wodaabe_error_message'); // Clear it after use
                }
            ?>
    
            <form method="post" action="<?php echo esc_url($action_url); ?>">
                <?php
                $nonce_action = $is_edit ? 'edit-product-block' : 'add-product-block';
                wp_nonce_field($nonce_action, 'product-block-nonce');
                ?>
    
                <?php if ($is_edit): ?>
                    <input type="hidden" name="id" value="<?php echo esc_attr($block['id']); ?>">
                <?php endif; ?>
    
                <table class="form-table">
                    <tbody>
                        <tr>
                            <th scope="row"><label for="product_id">Product</label></th>
                            <td>
                                <select id="product_id" name="product_id" required>
                                    <option value="">Select a product</option>
                                    <?php foreach ($products as $product): ?>
                                        <option value="<?php echo esc_attr($product['id']); ?>"
                                            <?php selected($is_edit ? $product['id'] : '', $block['product_id'] ?? ''); ?>>
                                            <?php echo esc_html($product['title']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="start_date">Start Date</label></th>
                            <td><input type="date" id="start_date" name="start_date" value="<?php echo esc_attr($block['start_date'] ?? ''); ?>" required></td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="end_date">End Date</label></th>
                            <td><input type="date" id="end_date" name="end_date" value="<?php echo esc_attr($block['end_date'] ?? ''); ?>" required></td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="comment">Comment</label></th>
                            <td><textarea id="comment" name="comment" rows="3"><?php echo esc_textarea($block['comment'] ?? ''); ?></textarea></td>
                        </tr>
                    </tbody>
                </table>
    
                <p class="submit">
                    <input type="submit" name="save" id="save" class="button button-primary" value="<?php echo $is_edit ? 'Update' : 'Save'; ?>">
                </p>
            </form>
        </div>
        <?php
    } // End of render_form method
}

