<?php

/**
 * Writes in the log files
 */
function wrs_log($data, $level = "INFO", $file = "log") {
  if (empty($data)) {
    return;
  }
  $day = gmdate("Y-m-d");
  $logfile = WODAABE_RESERVATIONS_PLUGIN_PATH . 'logs/' . $file . '_' . $day . '.log';
   if (is_array($data)) {
      error_log("\r\n" . '[' . gmdate("Y-m-d H:i:s") . '] ' . $level . ' ' . print_r($data, true), 3, $logfile);
  } else {
      error_log("\r\n" . '[' . gmdate("Y-m-d H:i:s") . '] ' . $level . ' ' . $data, 3, $logfile);
  }
}

function wrs_dd($data) {
  echo "<pre>";
  if (is_scalar($data)) {
    echo $data;
  } else {
    print_r($data);
  }
  echo "</pre>";
}

function is_wrs_admin_page() {
  if (isset($_GET['page']) && 'medcamer-payments' === $_GET['page']) {
    return true;
  }
  return false;
}

function wrs_inject_before_notices() {
  if (!is_wrs_admin_page()) {
    return;
  }
  echo '<div class="wrs-d-none">';
}
add_action('admin_notices', 'wrs_inject_before_notices', -9999);

function wrs_inject_after_notices() {
  if (!is_wrs_admin_page()) {
    return;
  }
  echo '</div>';
}
add_action('admin_notices', 'wrs_inject_after_notices', PHP_INT_MAX);

function wrs_custom_admin_css() {
  if (is_wrs_admin_page()) {
    echo "
      <style type='text/css' id='wrs-admin-css'>
      " . file_get_contents(WODAABE_RESERVATIONS_ADMIN_CSS_PATH) . "
      </style>
    ";
  }
}
add_action('admin_head', 'wrs_custom_admin_css');
function wrs_add_data_attribute($tag, $handle) {
  if ('wrs_paypal_js' !== $handle) {
    return $tag;
  }

  return str_replace(' src', ' data-sdk-integration-source="button-factory" src', $tag);
}
add_filter('script_loader_tag', 'wrs_add_data_attribute', 10, 2);

function wrs_enqueue_scripts($hook) {
  wp_enqueue_script('wrs_paypal_js', 'https://www.paypal.com/sdk/js?client-id=AXvO93T7s9DAr9UQLjey3yO2OTBwzWy6A7zjHwNIjMFBgnWAJuAVxCRYRrPO738WQmAenhgU9rXxcs2i&enable-funding=venmo&currency=EUR', false, false);
  //wp_enqueue_script('wrs_paypal_js', 'https://www.paypal.com/sdk/js?client-id=ASp5EmltjRDAuKZdEHEtQ_Vdaos7STK5PT8DgaT3ws7RxloCSVO_Q3KhWC6MS52gP873Ofn6KO2PW4Fx&components=buttons,hosted-fields&locale=en_US&currency=EUR', false, false);
  //wp_enqueue_script('wrs_paypal_js', 'https://www.paypal.com/sdk/js?client-id=AXvO93T7s9DAr9UQLjey3yO2OTBwzWy6A7zjHwNIjMFBgnWAJuAVxCRYRrPO738WQmAenhgU9rXxcs2i&components=buttons&locale=en_US&currency=EUR', false, false);
  wp_enqueue_script('wrs_stripe_js', 'https://js.stripe.com/v3/', false, false);
  wp_enqueue_script('wrs_all_js', WODAABE_RESERVATIONS_JS_URL, array('jquery', 'wrs_paypal_js'), WODAABE_RESERVATIONS_ASSETS_VERSION);
  wp_enqueue_script('wrs_front_js', WODAABE_RESERVATIONS_FRONT_JS_URL, array('jquery', 'wrs_all_js'), WODAABE_RESERVATIONS_ASSETS_VERSION);
  wp_add_inline_script('wrs_all_js', 'const wrsJsData = ' . json_encode(array(
    'ajaxUrl' => admin_url('admin-ajax.php'),
  )), 'before');
}
add_action('wp_enqueue_scripts', 'wrs_enqueue_scripts');

function wrs_enqueue_admin_scripts($hook) {
  wp_enqueue_style('wrs_admin_css', WODAABE_RESERVATIONS_ADMIN_CSS_URL, false, WODAABE_RESERVATIONS_ASSETS_VERSION);
  wp_enqueue_script('wrs_all_js', WODAABE_RESERVATIONS_JS_URL, array('jquery'), WODAABE_RESERVATIONS_ASSETS_VERSION);
  wp_enqueue_script('wrs_admin_script', WODAABE_RESERVATIONS_ADMIN_JS_URL, 'wrs_all_js', WODAABE_RESERVATIONS_ASSETS_VERSION);
  wp_add_inline_script('wrs_all_js', 'const wrsJsData = ' . json_encode(array(
    'ajaxUrl' => admin_url('admin-ajax.php'),
  )), 'before');
  wp_enqueue_script(
    'fullcalendar',
    'https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js',
    array('jquery'),
    '6.1.10',
    true
  );
  // Enqueue iCal.js script
    wp_enqueue_script(
        'ical-js',
        'https://cdn.jsdelivr.net/npm/ical.js@1.5.0/build/ical.min.js',
        array(), // Dependencies
        '1.5.0',  // Version
        true     // Enqueue in the footer
    );

    // Enqueue FullCalendar iCalendar plugin
    wp_enqueue_script(
        'fullcalendar-icalendar',
        'https://cdn.jsdelivr.net/npm/@fullcalendar/icalendar@6.1.10/index.global.min.js',
        array('jquery', 'fullcalendar'), // Dependencies including 'fullcalendar' script
        '6.1.10',
        true
    );
    
    // Enqueue simple-notify CSS
    wp_enqueue_style('simple-notify-css', 'https://cdn.jsdelivr.net/npm/simple-notify@1.0.4/dist/simple-notify.css');

    // Enqueue simple-notify JS
    wp_enqueue_script('simple-notify-js', 'https://cdn.jsdelivr.net/npm/simple-notify@1.0.4/dist/simple-notify.min.js');
}

add_action('admin_enqueue_scripts', 'wrs_enqueue_admin_scripts');

function wrs_remove_file_version($src) {
  if (strpos($src, "paypal")) {
    return remove_query_arg('ver', $src);
  }
  return $src;
}
add_filter('script_loader_src', 'wrs_remove_file_version', 9999);

/**
 * Returns a UUID4 string.
 *
 * @return string uuid4
 */
function wrs_uuid() {
  return wp_generate_uuid4();
}

function onMailError($wp_error) {
  echo "<pre>";
  print_r($wp_error);
  echo "</pre>";
}