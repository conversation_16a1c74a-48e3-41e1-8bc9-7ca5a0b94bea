
[2023-09-18 12:21:11] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 12:22:07] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 13:13:32] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 13:30:11] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 13:30:29] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 13:31:57] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-19","date_departure":"2023-09-23","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-18 13:32:08] INFO Processing new AJAX Request width payload {"id":"null","wrs_action":"delete_reservation","action":"wrs_process_ajax"}
[2023-09-18 13:40:42] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 13:41:03] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 13:43:31] INFO Processing new AJAX Request width payload {"id":"null","wrs_action":"delete_reservation","action":"wrs_process_ajax"}
[2023-09-18 13:45:33] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 14:01:48] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 14:02:22] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-19","date_departure":"2023-09-30","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-18 14:02:35] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-19","date_departure":"2023-09-20","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-18 14:02:44] INFO Processing new AJAX Request width payload {"id":"null","wrs_action":"delete_reservation","action":"wrs_process_ajax"}
[2023-09-18 14:02:50] INFO Processing new AJAX Request width payload {"location_id":"7","date_arrive":"2023-09-19","date_departure":"2023-09-22","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-18 14:03:08] INFO Processing new AJAX Request width payload {"id":"null","locationId":"7","currency_id":"1","language_id":"1","date_arrive":"2023-09-19","date_departure":"2023-09-22","promocodevalue":"undefined","product_id":"15","adult_count":"1","child_count":"0","first_name":"HERVE BORIS","last_name":"MBOU LONTSI","email":"<EMAIL>","phone":"56388003","country":"TN","notes":"","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"2","wrs_action":"save_order","action":"wrs_process_ajax"}
[2023-09-18 14:03:14] INFO Processing new AJAX Request width payload {"id":"109","wrs_action":"delete_reservation","action":"wrs_process_ajax"}
[2023-09-18 14:03:15] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 17:07:04] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-09-18 17:07:13] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-19","date_departure":"2023-09-21","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-18 17:10:14] INFO Processing new AJAX Request width payload {"location_id":"6","date_arrive":"2023-09-18","date_departure":"2023-09-20","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-09-18 19:17:02] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}