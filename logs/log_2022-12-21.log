
[2022-12-21 06:24:22] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 06:24:45] INFO Processing new AJAX Request width payload {"location_id":"4","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 06:25:19] INFO Processing new AJAX Request width payload {"location_id":"4","date_arrive":"2023-03-09","date_departure":"2023-09-13","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 09:58:33] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 09:58:47] INFO Processing new AJAX Request width payload {"location_id":"4","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:01:10] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-09-10","date_departure":"2022-09-12","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:02:07] INFO Processing new AJAX Request width payload {"location_id":"4","date_arrive":"2022-12-30","date_departure":"2023-01-03","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:02:54] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 10:03:05] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:04:00] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 10:04:05] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:05:26] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2022-12-21 10:05:48] INFO Processing new AJAX Request width payload {"code":"9569","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2022-12-21 10:08:09] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 10:08:16] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:09:13] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2022-12-21 10:09:35] INFO Processing new AJAX Request width payload {"code":"7383","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2022-12-21 10:09:38] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","promocodevalue":"undefined","product_id":"6","adult_count":"1","child_count":"0","first_name":"Leo","last_name":"Ghemtio","email":"<EMAIL>","phone":"0766109339","country":"FR","notes":"None","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-12-21 10:13:22] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","promocodevalue":"undefined","product_id":"6","adult_count":"1","child_count":"0","first_name":"Leo","last_name":"Ghemtio","email":"<EMAIL>","phone":"0766109339","country":"FR","notes":"None","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-12-21 10:15:36] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 10:15:46] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 10:15:52] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:16:43] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2022-12-21 10:17:13] INFO Processing new AJAX Request width payload {"code":"4666","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2022-12-21 10:17:18] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"leo","last_name":"ghemtio","email":"<EMAIL>","phone":"","country":"FR","notes":"None","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-12-21 10:22:06] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 10:22:11] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:22:20] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:22:34] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-24","date_departure":"2022-11-25","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:22:51] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-30","date_departure":"2022-12-01","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:23:34] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2022-12-21 10:24:33] INFO Processing new AJAX Request width payload {"code":"9203","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2022-12-21 10:24:39] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-11-30","date_departure":"2022-12-01","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"Leo","last_name":"Ghemtio","email":"<EMAIL>","phone":"0766109339","country":"select country","notes":"None","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-12-21 10:53:39] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 10:55:14] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-05","date_departure":"2023-01-04","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:57:09] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2022-12-21 10:57:28] INFO Processing new AJAX Request width payload {"code":"2664","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2022-12-21 10:57:45] INFO Processing new AJAX Request width payload {"code":"2464","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2022-12-21 10:58:25] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 10:59:03] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 10:59:40] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2022-12-21 10:59:59] INFO Processing new AJAX Request width payload {"code":"5546","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2022-12-21 11:00:12] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"Leo","last_name":"Ghemtio","email":"<EMAIL>","phone":"0766109339","country":"FR","notes":"None","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-12-21 11:00:32] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 11:01:50] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:03:39] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:03:58] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 12:09:13] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2022-12-21 12:09:52] INFO Processing new AJAX Request width payload {"code":"9583","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2022-12-21 12:14:53] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:18:30] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 12:22:59] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:23:04] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 12:25:04] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:25:55] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:26:38] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 12:27:00] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2022-12-21 12:27:30] INFO Processing new AJAX Request width payload {"code":"8443","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2022-12-21 12:42:15] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:43:39] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:43:46] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:44:01] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:48:28] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 12:48:45] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 13:52:14] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 13:53:21] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-12-29","date_departure":"2022-12-31","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 13:55:31] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 13:55:37] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 13:56:04] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 13:56:27] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 13:58:28] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 13:59:03] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 13:59:39] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 13:59:44] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 14:00:57] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2022-12-21 14:01:03] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2022-11-22","date_departure":"2022-11-24","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 14:02:08] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2022-12-21 14:04:20] INFO Processing new AJAX Request width payload {"code":"5104","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2022-12-21 14:05:24] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-04","date_departure":"2023-01-06","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 14:06:55] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-04","date_departure":"2023-01-10","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 14:08:17] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-04","date_departure":"2023-01-10","wrs_action":"get_products","action":"wrs_process_ajax"}
[2022-12-21 14:09:55] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2023-01-04","date_departure":"2023-01-10","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"Leo","last_name":"G","email":"<EMAIL>","phone":"0766109339","country":"FR","notes":"None","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-12-21 14:12:38] INFO Processing new AJAX Request width payload {"locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2023-01-04","date_departure":"2023-01-10","promocodevalue":"undefined","product_id":"1","adult_count":"1","child_count":"0","first_name":"Leo","last_name":"G","email":"<EMAIL>","phone":"0766109339","country":"FR","notes":"None","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2022-12-21 14:17:14] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"5NB768137R077871W","id":"52","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2022-12-21 14:58:37] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}