<?php

/**
 * WodaabeReservationsLocations_Table class will create the page to load the table
 */
class WodaabeReservationsLocations_Table {

  /**
   * Display the list table page
   *
   * @return Void
   */
  public function list_table_page() {
    if (isset($_GET["action"])) {
      $action = $_GET["action"];
      if ("show" == $action) {
        return $this->show_details_page();
      } else if ("new" == $action) {
        return $this->show_create_page();
      } else if ("delete" == $action) {
        return $this->do_delete();
      } else if ("edit" == $action) {
        return $this->show_edit_page();
      }
    }
    $listTable = new WodaabeReservationsLocations_List_Table();
    $listTable->prepare_items();
    $new_link = $_SERVER['REQUEST_URI'] . "&action=new";
    ?>
      <div class="wrap">
          <h2 style="display: inline-block;">Locations</h2>
          <a style="display: inline-block;" href="<?php echo $new_link ?>" class="page-title-action">Add New</a>
          <p>Locations represents the differents places where your homes and appartements can be found.</p>
          <?php $listTable->display();?>
      </div>
    <?php
}

  public function do_delete() {
    global $wpdb, $wrsModule;
    $id = $_GET["id"];

    $wpdb->delete(
      'wp_wrs_locations',
      ['id' => $id],
      ['%d'],
    );
    wp_redirect("/wp-admin/admin.php?page=wodaabe-reservations");
  }

  public function show_details_page() {
    global $wpdb, $wrsModule;
    $id = $_GET["id"];

    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE . " WHERE id = " . $id;

    $item = $wpdb->get_row($sql, 'ARRAY_A');

    $items = array(
      '#' => $item["id"],
      'Title' => $item["title"],
      'Picture' => "<a href=" . $item["picture"] . " target='_blank'>View</a>",
      'Button text' => $item["btn_text"],
      'Link' => $item["link"],
      //'Page' => $item["page"],
      //'Logo' => "<a href=" . $item["logo"] . " target='_blank'>View</a>",
      'Position' => $item["position"],
      'Date created' => $item["date_add"] ? \date("d/m/Y H:i:s", \strtotime($item["date_add"])) : "<b>-</b>",
      'Last update' => $item["date_add"] ? \date("d/m/Y H:i:s", \strtotime($item["date_add"])) : "<b>-</b>",
    );

    ?>
  <div class="wrap">
      <h2>Locations</h2>
      <p>Find below the details about the location #<?php echo $id ?> </p>
      <table class="wp-list-table widefat  striped table-view-excerpt reservations_page_wodaabe-reservations-reservations">


<tbody id="the-list">
  <?php
foreach ($items as $key => $value) {?>
      <tr>
      <td class=" has-row-actions column-primary">
        <strong>
          <?php echo $key ?>
        </strong>
      </td>
      <td class="column-date_arrive" >
        <?php echo $value ?>
      </td>
      <?php }?>
    </table>
  </div>
<?php
}

  public function show_create_page() {
    global $wpdb, $wrsModule;

    $pages = get_pages();
    $itemspages = array();
    foreach ($pages as $page) {
      $itemspages[] = array(
        'id' => get_page_link($page->ID),
        'title' => $page->post_title,
      );
    }

    if ("POST" == $_SERVER['REQUEST_METHOD']) {
      $wpdb->insert('wp_wrs_locations',
        array(
          'title' => $_POST['Title'],
          'picture' => $_POST['Picture'],
          'page' => $_POST['Page'],
          'btn_text' => $_POST['Btn_text'],
          'link' => $_POST['Link'],
          'position' => intval($_POST['Position']),
        )
      );
      wp_redirect("/wp-admin/admin.php?page=wodaabe-reservations");
    }

    $items = array(
      'Title' => "",
      'Picture' => "",
      'Btn text' => "",
      'Position' => "",
    );
    $items2 = array();
    $item2 = array(
      array(
        "id" => "inline",
        "title" => "Open reservation modal",
      ),
      array(
        "id" => "contact",
        "title" => "Open contact page",
      ),
    );
    $items3 = array(
      'Link' => "",
    );
    $items4 = array(
      'Page' => "",
    );

    ?>
<div class="wrap">
<form method="POST">
  <h2>Locations</h2>
  <p>Here you can create a new location </p>
  <table class="wp-list-table widefat fixed striped table-view-excerpt reservations_page_wodaabe-reservations-reservations">


<tbody id="the-list">
<?php
foreach ($items as $key => $value) {?>
  <tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      <?php echo $key ?>
    </strong>
  </td>

</tr>
<tr>
  <td class="date_arrive column-date_arrive">
    <input style="width: 50%" name="<?php echo $key ?>" type="text" />
  </td>
</tr>
  <?php }?>
  <?php
foreach ($items3 as $key => $value) {?>
  <tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      <?php echo $key ?>
    </strong>
  </td>

</tr>
<tr>

  <td class="date_arrive column-date_arrive">
    <select style="width: 50%" name="<?php echo $key ?>" >
<?php
foreach ($item2 as $val) {?>
 <option value="<?php echo $val["id"] ?>"> <?php echo $val["title"] ?> </option>
  <?php }?>

</select>
  </td>
</tr>
  <?php }?>
  <?php
foreach ($items4 as $key => $value) {?>
  <tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      <?php echo $key ?>
    </strong>
  </td>

</tr>
<tr>

  <td class="date_arrive column-date_arrive">
    <select style="width: 50%" name="<?php echo $key ?>" >
<?php
foreach ($itemspages as $val) {?>
 <option value="<?php echo $val["id"] ?>"> <?php echo $val["title"] ?> </option>
  <?php }?>

</select>
  </td>
</tr>
  <?php }?>
  <?php
foreach ($items2 as $key => $value) {?>
  <tr>
  <td class=" has-row-actions column-primary" style="width: 20%">
    <strong>
      <?php echo $key ?>
    </strong>
  </td>

</tr>
<tr>

  <td class="date_arrive column-date_arrive">
    <textarea rows="5" style="width: 100%" name="<?php echo $key ?>"></textarea>
  </td>
</tr>
  <?php }?>
</table>
<div style="margin-top:1rem">
        <input type="submit" name="publish" id="publish" class="button button-primary button-large" value="Publish">						</div>

</form>
      </div>
<?php
}

  public function show_edit_page() {
    global $wpdb, $wrsModule;
    $id = $_GET["id"];
    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE . " WHERE id = " . $id;

    $item = $wpdb->get_row($sql, 'ARRAY_A');

    $pages = get_pages();
    $itemspages = array();
    foreach ($pages as $page) {
      $itemspages[] = array(
        'id' => get_page_link($page->ID),
        'title' => $page->post_title,
      );
    }

    if ("POST" == $_SERVER['REQUEST_METHOD']) {
      $wpdb->update('wp_wrs_locations',
        array(
          'title' => $_POST['Title'],
          'picture' => $_POST['Picture'],
          'page' => $_POST['Page'],
          'btn_text' => $_POST['Btn_text'],
          'link' => $_POST['Link'],
          'position' => intval($_POST['Position']),
        ),
        array('id' => $id)
      );
      wp_redirect("/wp-admin/admin.php?page=wodaabe-reservations");
    }
    $item2 = array(
      array(
        "id" => "inline",
        "title" => "Open reservation modal",
      ),
      array(
        "id" => "contact",
        "title" => "Open contact page",
      ),
    );
    $items = array(
      'Title' => $item["title"],
      'Picture' => $item["picture"],
      'Btn text' => $item["btn_text"],
      'Position' => $item["position"],
    );
    $items2 = array();
    $items3 = array(
      'Link' => "",
    );
    $items4 = array(
      'Page' => "",
    );

    ?>
<div class="wrap">
<form method="POST">
<h2>Locations</h2>
<p>Here you can edit a  location </p>
<table class="wp-list-table widefat fixed striped table-view-excerpt reservations_page_wodaabe-reservations-reservations">


<tbody id="the-list">
<?php
foreach ($items as $key => $value) {?>
<tr>
<td class=" has-row-actions column-primary" style="width: 20%">
  <strong>
    <?php echo $key ?>
  </strong>
</td>

</tr>
<tr>
<td class="date_arrive column-date_arrive">
  <input value="<?php echo $value ?>" style="width: 50%" name="<?php echo $key ?>" type="text" />
</td>
</tr>
<?php }?>
<?php
foreach ($items3 as $key => $value) {?>
<tr>
<td class=" has-row-actions column-primary" style="width: 20%">
  <strong>
    <?php echo $key ?>
  </strong>
</td>

</tr>
<tr>

<td class="date_arrive column-date_arrive">
  <select style="width: 50%" name="<?php echo $key ?>" >
<?php
foreach ($item2 as $val) {?>
<option value="<?php echo $val["id"] ?>" <?php if ($val["id"] == $item["link"]) {?> selected="true" <?php }?>> <?php echo $val["title"] ?> </option>
<?php }?>

</select>
</td>
</tr>
<?php }?>
<?php
foreach ($items4 as $key => $value) {?>
<tr>
<td class=" has-row-actions column-primary" style="width: 20%">
  <strong>
    <?php echo $key ?>
  </strong>
</td>

</tr>
<tr>

<td class="date_arrive column-date_arrive">
  <select style="width: 50%" name="<?php echo $key ?>" >
<?php
foreach ($itemspages as $val) {?>
<option value="<?php echo $val["id"] ?>" <?php if ($val["id"] == $item["page"]) {?> selected="true" <?php }?>> <?php echo $val["title"] ?> </option>
<?php }?>

</select>
</td>
</tr>
<?php }?>
<?php
foreach ($items2 as $key => $value) {?>
<tr>
<td class=" has-row-actions column-primary" style="width: 20%">
  <strong>
    <?php echo $key ?>
  </strong>
</td>

</tr>
<tr>

<td class="date_arrive column-date_arrive">
  <textarea rows="5" style="width: 100%" name="<?php echo $key ?>"><?php echo $value ?></textarea>
</td>
</tr>
<?php }?>
</table>
<div style="margin-top:1rem">
      <input type="submit" name="publish" id="publish" class="button button-primary button-large" value="Publish">						</div>

</form>
    </div>
<?php
}

}

// WP_List_Table is not loaded automatically so we need to load it in our application
if (!class_exists('WP_List_Table')) {
  require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';
}

/**
 * Create a new table class that will extend the WP_List_Table
 */
class WodaabeReservationsLocations_List_Table extends WP_List_Table {

  /**
   * Returns the count of records in the database.
   *
   * @return null|string
   */
  public static function record_count() {
    global $wpdb;

    $sql = "SELECT COUNT(*) FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE;
    return $wpdb->get_var($sql);
  }

  /**
   * Retrieve customer’s data from the database
   *
   *
   * @return mixed
   */
  public function get_items($orderby = "id", $order = "desc") {
    global $wpdb, $wrsModule;

    $columns = array('id', 'titre', 'picture', 'btn_text', 'link', 'date_add', 'date_upd');
    if (!in_array($orderby, $columns)) {
      $orderby = "id";
    }
    $orders = array('asc', 'desc');
    if (!in_array($order, $orders)) {
      $order = "desc";
    }

    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE . " ORDER BY " . $orderby . " " . $order;

    $result = $wpdb->get_results($sql, 'ARRAY_A');

    return $result;
  }

  /**
   * Prepare the items for the table to process
   *
   * @return Void
   */
  public function prepare_items() {

    $columns = $this->get_columns();
    $hidden = $this->get_hidden_columns();
    $sortable = $this->get_sortable_columns();

    $perPage = 10;
    $currentPage = $this->get_pagenum();

    // Set defaults
    $orderby = 'id';
    $order = 'desc';

    // If orderby is set, use this as the sort column
    if (!empty($_GET['orderby'])) {
      $orderby = $_GET['orderby'];
    }

    // If order is set use this as the order
    if (!empty($_GET['order'])) {
      $order = $_GET['order'];
    }

    $data = $this->get_items($orderby, $order);

    $data = is_array($data) ? $data : array();
    $totalItems = $this->record_count();

    $this->set_pagination_args(array(
      'total_items' => $totalItems,
      'per_page' => $perPage,
    ));

    $data = array_slice($data, (($currentPage - 1) * $perPage), $perPage);

    $this->_column_headers = array($columns, $hidden, $sortable);
    $this->items = $data;
  }

  /**
   * Override the parent columns method. Defines the columns to use in your listing table
   *
   * @return Array
   */
  public function get_columns() {
    $columns = array(
      'id' => 'ID',
      'title' => 'Titre',
      'picture' => 'Cover picture',
      'btn_text' => 'Action texte',
      'link' => 'Link action',
      'date_add' => 'Date de création',
      'date_upd' => 'Dernière mise à jour',
    );
    return $columns;
  }

  /**
   * Define which columns are hidden
   *
   * @return Array
   */
  public function get_hidden_columns() {
    return array();
  }

  /**
   * Define the sortable columns
   *
   * @return Array
   */
  public function get_sortable_columns() {
    return array(
      'id' => array('id', false),
      'date_add' => array('date_add', false),
      'titre' => array('titre', false),
    );
  }

  /**
   * Define what data to show on each column of the table
   *
   * @param  Array $item        Data
   * @param  String $column_name - Current column name
   *
   * @return Mixed
   */
  public function column_default($item, $column_name) {
    switch ($column_name) {
    case 'id':
      return "<strong>#{$item[$column_name]}</strong>";

    case 'picture':
      return "<a href='{$item[$column_name]}' target='_blank'>Show Image</a>";
    case 'link':
      return $item[$column_name] == "inline" ? "Start reservation process" : "Send to contact page";

    case 'date_add':
      return $item[$column_name] ? \date("d/m/Y H:i:s", \strtotime($item[$column_name])) : "<b>-</b>";
    case 'date_upd':
      return $item[$column_name] ? \date("d/m/Y H:i:s", \strtotime($item[$column_name])) : "<b>-</b>";

    default:
      return $item[$column_name];
    }
  }

  public function column_title($item) {
    $path = $_SERVER['REQUEST_URI'];
    $edit_link = $path . "&action=edit&id=" . $item["id"];
    $view_link = $path . "&action=show&id=" . $item["id"];
    $delete_link = $path . "&action=delete&id=" . $item["id"];
    $output = '';

    // Title.
    $output .= '<strong><a href="' . esc_url($edit_link) . '" class="row-title">' . esc_html($item["title"]) . '</a></strong>';

    // Get actions.
    $actions = array(
      'view' => '<a href="' . esc_url($view_link) . '">' . esc_html__('View', 'my_plugin') . '</a>',
      'edit' => '<a href="' . esc_url($edit_link) . '">' . esc_html__('Edit', 'my_plugin') . '</a>',
      'delete' => '<a href="' . esc_url($delete_link) . '">' . esc_html__('Delete', 'my_plugin') . '</a>',
    );

    $row_actions = array();

    foreach ($actions as $action => $link) {
      $row_actions[] = '<span class="' . esc_attr($action) . '">' . $link . '</span>';
    }

    $output .= '<div class="row-actions visible">' . implode(' | ', $row_actions) . '</div>';

    return $output;
  }

  /**
   * Allows you to sort the data by the variables set in the $_GET
   *
   * @return Mixed
   */
  private function sort_data($a, $b) {
    // Set defaults
    $orderby = 'id';
    $order = 'desc';

    // If orderby is set, use this as the sort column
    if (!empty($_GET['orderby'])) {
      $orderby = $_GET['orderby'];
    }

    // If order is set use this as the order
    if (!empty($_GET['order'])) {
      $order = $_GET['order'];
    }

    $result = strcmp($a[$orderby], $b[$orderby]);

    if ($order === 'asc') {
      return $result;
    }

    return -$result;
  }
}
?>