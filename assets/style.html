<style>
  @font-face {
    font-family: ClassicoURW-Reg;
    src: url(/wp-content/reservations/font.woff2) format("woff2"),
      url(/wp-content/reservations/font.woff) format("woff");
    font-display: swap;
  }

  @font-face {
    font-family: ClassicoURW-Med;
    src: url(/wp-content/reservations/font.woff2) format("woff2"),
      url(/wp-content/reservations/font.woff) format("woff");
    font-display: swap;
  }

  @font-face {
    font-family: ClassicoURW-RegIta;
    src: url(/wp-content/reservations/font.woff2) format("woff2"),
      url(/wp-content/reservations/font.woff) format("woff");
    font-display: swap;
  }

  @font-face {
    font-family: ClassicoURW-MedIta;
    src: url(../../fonts/classicourw-medlta/font.woff2) format("woff2"),
      url(../../fonts/classicourw-medita/font.woff) format("woff");
    font-display: swap;
  }

  @font-face {
    font-family: MessinaSansWeb;
    src: url(/wp-content/reservations/MessinaSansWeb-Regular.eot);
    src: url(/wp-content/reservations/MessinaSansWeb-Regular.eot?#iefix) format("embedded-opentype"),
      url(/wp-content/reservations/MessinaSansWeb-Regular.woff2) format("woff2"),
      url(/wp-content/reservations/MessinaSansWeb-Regular.woff) format("woff");
    font-weight: 400;
    font-style: normal;
    font-stretch: normal;
    unicode-range: U+000D-FB04;
    font-display: swap;
  }

  @font-face {
    font-family: MessinaSansWeb;
    src: url(/wp-content/reservations/MessinaSansWeb-Bold.eot);
    src: url(/wp-content/reservations/MessinaSansWeb-Bold.eot?#iefix) format("embedded-opentype"),
      url(/wp-content/reservations/MessinaSansWeb-Bold.woff2) format("woff2"),
      url(/wp-content/reservations/MessinaSansWeb-Bold.woff) format("woff");
    font-weight: 700;
    font-style: normal;
    font-stretch: normal;
    unicode-range: U+000D-FB04;
    font-display: swap;
  }

  @font-face {
    font-family: MessinaSansWeb;
    src: url(/wp-content/reservations/MessinaSansWeb-Black.eot);
    src: url(/wp-content/reservations/MessinaSansWeb-Black.eot?#iefix) format("embedded-opentype"),
      url(/wp-content/reservations/MessinaSansWeb-Black.woff2) format("woff2"),
      url(/wp-content/reservations/MessinaSansWeb-Black.woff) format("woff");
    font-weight: 900;
    font-style: normal;
    font-stretch: normal;
    unicode-range: U+000D-FB04;
    font-display: swap;
  }
</style>
<style>
  #reservation-wrapper {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;
    width: 100%;
    background: white;
    z-index: 999;
    display: none;
  }

  .ktFePO {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: rgb(243, 243, 244);
    font-family: "Open Sans", sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    color: rgb(0, 0, 0);
  }

  .ggXAzT {
    position: absolute;
    width: 100%;
    z-index: 99;
  }

  .cmeXPA {
    padding: 0px 24px;
    height: 72px;
    background: rgb(255, 255, 255);
    border-bottom: 1px solid rgb(191, 191, 192);
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    box-sizing: border-box;
    flex-shrink: 0;
  }

  .hzznkK {
    display: flex;
    flex-wrap: nowrap;
    margin: 0px;
    padding: 0px;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
  }

  .cmeXPA>* {
    width: 100%;
  }

  .jIiwPp {
    display: flex;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    justify-content: flex-start;
    margin: 0px;
    padding: 0px;
    -webkit-box-align: center;
    align-items: center;
  }

  .gykQbo {
    display: flex;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    justify-content: flex-start;
    margin: 0px;
    padding: 0px;
    -webkit-box-align: center;
    align-items: center;
  }

  .dafbhF {
    background: transparent;
    opacity: 1;
    transition: opacity 350ms ease-out 0s;
    max-width: 100%;
    border-radius: 4px;
  }

  .dAijgu img {
    object-fit: cover;
    width: 40px;
    height: 40px;
    border-radius: 4px;
  }

  .BCYKq {
    font-family: "Open Sans", sans-serif;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    color: rgb(0, 0, 0);
    display: none;
  }

  @media screen and (min-width: 600px) {
    .BCYKq {
      display: block;
    }
  }

  .hzznkK> :not(:first-child) {
    margin-left: 0px;
  }

  .jIiwPp {
    display: flex;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    justify-content: flex-start;
    margin: 0px;
    padding: 0px;
    -webkit-box-align: center;
    align-items: center;
  }

  /*.epeqRw {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-image: url(https://cdn.mews.com/media/image/6b908d0a-64ea-4431-89f3-b18c202f179f?quality=85&width=1920&height=1080);
    opacity: 1;
    transition: opacity 350ms ease-out 0s;
    height: 100%;
}*/
  .jIiwPp {
    display: flex;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    justify-content: flex-start;
    margin: 0px;
    padding: 0px;
    -webkit-box-align: center;
    align-items: center;
  }

  .cYuerP {
    -webkit-box-align: center;
    align-items: center;
    align-self: stretch;
    border-left: 1px solid rgb(191, 191, 192);
    display: flex;
    padding: 16px 0px 16px 16px;
  }

  .jIiwPp> :not(:first-child) {
    margin-left: 16px;
  }

  .fUxyGn {
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    padding: 0px 16px;
    border: 0px;
    border-radius: 4px;
    height: 40px;
    font-family: "Open Sans", sans-serif;
    font-size: 1rem;
    font-weight: 700;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    background-color: transparent;
    color: rgb(43, 43, 46);
    transition-property: background-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    white-space: nowrap;
    cursor: pointer;
  }

  .kUkETl {
    padding: 0px;
    width: 40px;
  }

  .fcNaly {
    font-family: "Mews Icons";
    font-style: normal;
    font-weight: 400;
    display: inline-block;
    line-height: 1;
    text-transform: none;
    letter-spacing: normal;
    overflow-wrap: normal;
    white-space: nowrap;
    direction: ltr;
    color: inherit;
    font-size: 24px;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizelegibility;
    font-feature-settings: "liga";
  }

  .eHzrow {
    display: flex;
    pointer-events: none;
  }

  .bFHuOj {
    background-color: rgb(255, 255, 255);
    border-bottom: 1px solid rgb(191, 191, 192);
    display: flex;
    -webkit-box-pack: center;
    justify-content: center;
    flex-direction: column;
    box-sizing: border-box;
    padding: 8px 0px;
    min-height: 60px;
  }

  @media screen and (min-width: 600px) {
    .bFHuOj {
      padding: 8px;
    }
  }

  .hnbCHS {
    width: 100%;
    margin: 0px;
    padding: 0px;
    list-style: none;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    justify-content: space-between;
  }

  .kigjlB {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    box-sizing: border-box;
    background: transparent;
    border: 0px;
    text-align: left;
    min-width: 112px;
    padding: 8px 16px;
    max-width: 320px;
  }

  .fcNaly {
    font-family: "Mews Icons";
    font-style: normal;
    font-weight: 400;
    display: inline-block;
    line-height: 1;
    text-transform: none;
    letter-spacing: normal;
    overflow-wrap: normal;
    white-space: nowrap;
    direction: ltr;
    color: inherit;
    font-size: 24px;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizelegibility;
    font-feature-settings: "liga";
  }

  .cUHlJV {
    color: rgb(70, 103, 195);
    border-radius: 50%;
    background: rgba(70, 103, 195, 0.08);
    box-shadow: rgb(70 103 195 / 8%) 0px 0px 0px 8px;
  }

  .eHzrow {
    display: flex;
    pointer-events: none;
  }

  .eQcwDJ {
    overflow: auto;
    margin-left: 0px;
  }

  @media screen and (min-width: 600px) {
    .eQcwDJ {
      display: block;
    }
  }

  .fbvjyv {
    font-family: "Open Sans", sans-serif;
    font-size: 0.875rem;
    font-weight: 700;
    line-height: 1.5;
    letter-spacing: 0.0025em;
    -webkit-font-smoothing: antialiased;
    color: rgb(0, 0, 0);
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .kVIJdD {
    font-family: "Open Sans", sans-serif;
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.5;
    letter-spacing: 0.0025em;
    -webkit-font-smoothing: antialiased;
    color: rgba(0, 0, 0, 0.64);
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .hcbNEz {
    width: 1px;
    background: rgba(0, 0, 0, 0.32);
    min-width: 16px;
    height: 1px;
    -webkit-box-flex: 1;
    flex-grow: 1;
    margin-left: 0px;
  }

  .style__IconWrapper-sc-147rnkr-0 {
    display: none;
  }

  .entry-content ol li {
    padding-left: 0rem;
  }

  .fsMpal {
    position: absolute;
    inset: 0px;
    width: 100%;
    min-height: 100%;
  }

  .XqjEM {
    opacity: 1;
    transform: translate3d(0px, 0px, 0px);
    transition: transform 350ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
      opacity 350ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  }

  .ebjVgV {
    height: 100%;
    overflow: auto;
    position: relative;
  }

  .cELOjf {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    height: 100%;
  }

  .fRFRZt {
    position: relative;
    height: 100%;
  }

  .GKVsD {
    opacity: 1;
    transition: opacity 350ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  }

  .eOhtQL {
    width: 100%;
    position: absolute;
    inset: 0px;
  }

  .fURGZJ {
    height: 100%;
  }

  .fFoQxz {
    position: absolute;
    inset: 0px;
  }

  .kmjYkF {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    /*background-image: url(https://cdn.mews.com/media/image/6b908d0a-64ea-4431-89f3-b18c202f179f?quality=85&width=1920&height=1080);*/
    opacity: 1;
    transition: opacity 350ms ease-out 0s;
    background-color: blue;
  }

  .bWdVGU {
    margin: 0px auto;
    padding: 24px 16px;
    max-width: 960px;
    position: relative;
    height: calc(100% - 48px);
  }

  @media screen and (min-width: 1024px) {
    .bWdVGU {
      padding: 32px 24px;
    }

    .bWdVGU {
      height: calc(100% - 64px);
    }
  }

  .hnVevJ {
    display: flex;
    flex-wrap: nowrap;
    margin: 0px;
    padding: 0px;
    width: 100%;
    height: 100%;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
  }

  .kknGbm {
    width: 100%;
    max-width: 440px;
  }

  .ftIjDA {
    box-sizing: border-box;
    background: rgb(255, 255, 255);
    padding: 16px;
    box-shadow: rgb(43 43 46 / 8%) 0px -1px 4px, rgb(43 43 46 / 8%) 0px 4px 8px;
    border-radius: 8px;
  }

  .jStchL {
    display: flex;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    justify-content: flex-start;
    margin: 0px;
    padding: 0px;
  }

  @media screen and (max-width: 2147483646px) {
    .jStchL {
      flex-direction: column;
    }

    .jStchL.jStchL>* {
      margin-left: 0px;
    }
  }

  .eyUgGM {
    display: flex;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    justify-content: flex-start;
    margin: 0px;
    padding: 0px;
  }

  .jZsWXm {
    position: relative;
  }

  .eyUgGM>* {
    -webkit-box-flex: 1;
    flex-grow: 1;
    width: 100%;
  }

  .dLFyGP {
    width: 100%;
    height: 21px;
    box-sizing: border-box;
    display: flex;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    margin-bottom: 8px;
    position: absolute;
    top: 0px;
    left: 50%;
    transform: translateX(-50%);
  }

  .iQzZyX {
    width: 38px;
    height: 11px;
  }

  .bmLGcy {
    fill: rgb(70, 103, 195);
  }

  .eyUgGM>* {
    -webkit-box-flex: 1;
    flex-grow: 1;
    width: 100%;
  }

  .eyUgGM> :not(:first-child) {
    margin-left: 0px;
  }

  .jvnuuH {
    color: rgba(0, 0, 0, 0.64);
    font-size: 14px;
    line-height: 21px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .gkAxwe {
    background: transparent;
    border: 1px solid rgb(191, 191, 192);
    cursor: pointer;
    padding: 16px;
    font-family: inherit;
    position: relative;
    width: 100%;
    border-radius: 4px 0px 0px 4px;
  }

  .kRwtGQ {
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    width: 100%;
    padding: 0px 16px;
    border: 0px;
    border-radius: 4px;
    height: 40px;
    font-family: "Open Sans", sans-serif;
    font-size: 1rem;
    font-weight: 700;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    background-color: #3D6E84;
    color: rgb(255, 255, 255);
    transition-property: background-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    white-space: nowrap;
    cursor: pointer;
  }

  @media screen and (max-width: 2147483646px) {
    .jStchL.jStchL>* {
      margin-left: 0px;
    }

    .jStchL.jStchL> :not(:first-child) {
      margin-top: 16px;
    }
  }

  .eyUgGM>* {
    -webkit-box-flex: 1;
    flex-grow: 1;
    width: 100%;
  }

  .eyUgGM> :not(:first-child) {
    margin-left: 0px;
  }

  .gnWpjP {
    color: rgba(0, 0, 0, 0.64);
    font-size: 14px;
    line-height: 21px;
    font-weight: 600;
    margin-bottom: 8px;
    text-align: right;
  }

  .hlGmvd {
    background: transparent;
    border-width: 1px 1px 1px 0px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-top-color: rgb(191, 191, 192);
    border-right-color: rgb(191, 191, 192);
    border-bottom-color: rgb(191, 191, 192);
    border-image: initial;
    cursor: pointer;
    padding: 16px;
    font-family: inherit;
    position: relative;
    width: 100%;
    border-radius: 0px 4px 4px 0px;
    border-left-style: initial;
    border-left-color: initial;
  }

  .juDRzz {
    text-align: center;
  }

  .ebrBxM {
    color: rgb(0, 0, 0);
    font-size: 36px;
    line-height: 40px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .dBNnar {
    color: rgb(0, 0, 0);
    font-size: 16px;
    line-height: 24px;
    font-weight: 600;
  }

  .jvSZiY {
    color: rgba(0, 0, 0, 0.64);
    font-size: 16px;
    line-height: 24px;
  }

  .cizVkM {
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    width: 100%;
    padding: 0px 16px;
    border: 0px;
    border-radius: 4px;
    height: 40px;
    font-family: "Open Sans", sans-serif;
    font-size: 1rem;
    font-weight: 700;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    background-color: transparent;
    color: rgb(43, 43, 46);
    transition-property: background-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    white-space: nowrap;
    cursor: pointer;
  }

  .fcNaly {
    font-family: "Mews Icons";
    font-style: normal;
    font-weight: 400;
    display: inline-block;
    line-height: 1;
    text-transform: none;
    letter-spacing: normal;
    overflow-wrap: normal;
    white-space: nowrap;
    direction: ltr;
    color: inherit;
    font-size: 24px;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizelegibility;
    font-feature-settings: "liga";
  }

  .style__IconWrapper-sc-147rnkr-0 {
    display: none;
  }

  .jxFcPm {
    margin: 0px auto;
    padding: 24px 16px;
    max-width: 960px;
    position: relative;
    overflow: hidden;
    background-color: red;
  }

  @media screen and (min-width: 1024px) {
    .jxFcPm {
      padding: 32px 24px;
    }
  }

  .hggCUz {
    display: flex;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    justify-content: flex-start;
    margin: 0px;
    padding: 0px;
  }

  @media screen and (max-width: 2147483646px) {
    .hggCUz {
      flex-direction: column;
    }
  }

  .kElSVh {
    height: 24px;
  }

  .eWHTIh {
    width: 100%;
  }

  .kjGUHG {
    display: flex;
    flex-wrap: wrap;
    margin: 0px;
    padding: 0px;
  }

  @media screen and (min-width: 1024px) {
    .lgmnzy .style__Column-sc-17dphdl-0 {
      width: calc(50% - 12px);
    }
  }

  .jsIfmS {
    list-style: none;
  }

  .ftIjDA {
    box-sizing: border-box;
    background: rgb(255, 255, 255);
    padding: 16px;
    box-shadow: rgb(43 43 46 / 8%) 0px -1px 4px, rgb(43 43 46 / 8%) 0px 4px 8px;
    border-radius: 8px;
  }

  .hggCUz {
    display: flex;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    justify-content: flex-start;
    margin: 0px;
    padding: 0px;
  }

  @media screen and (max-width: 2147483646px) {
    .hggCUz {
      flex-direction: column;
    }
  }

  .geZMSr {
    width: 100%;
    height: 0px;
    padding-bottom: 56.25%;
    position: relative;
  }

  @media screen and (max-width: 2147483646px) {
    .hggCUz.hggCUz>* {
      margin-left: 0px;
    }
  }

  .hCJQrM {
    position: absolute;
    inset: 0px;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
  }

  .dGYbUL {
    width: 100%;
    height: 100%;
  }

  .fOBuvM {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 4px;
  }

  .dUDoAH {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    transition: transform 350ms cubic-bezier(0.4, 0, 0.2, 1) 0s;
    transform: translate3d(0px, 0px, 0px);
  }

  .jqNDgS {
    cursor: pointer;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .jrVkPR {
    background: transparent;
    opacity: 1;
    transition: opacity 350ms ease-out 0s;
    max-width: 100%;
  }

  .epeqRw {
    position: relative;
    overflow: hidden;
    height: calc(100% - 132px);
    top: 132px;
  }

  .bdmeit {
    margin: 0px;
    color: rgb(0, 0, 0);
    font-family: "Open Sans", sans-serif;
    font-size: 1.625rem;
    font-weight: 700;
    line-height: 1.3;
    letter-spacing: -0.0075em;
    -webkit-font-smoothing: antialiased;
    text-align: center;
  }

  .irmQpm {
    display: flex;
    flex-wrap: nowrap;
    margin: 0px;
    padding: 0px;
    -webkit-box-pack: center;
    justify-content: center;
  }

  .eHUYcd {
    margin: 0px;
    color: rgb(0, 0, 0);
    font-family: "Open Sans", sans-serif;
    font-size: 1.4375rem;
    font-weight: 700;
    line-height: 1.3;
    letter-spacing: -0.005em;
    -webkit-font-smoothing: antialiased;
    text-align: center;
  }

  .style__ImageAmountWrapperElement-sc-bgcs2a-0 {
    display: none;
  }

  .ikqrFJ {
    color: rgb(0, 0, 0);
    font-family: "Open Sans", sans-serif;
    font-size: 1.4375rem;
    font-weight: 700;
    line-height: 1.3;
    letter-spacing: -0.005em;
    -webkit-font-smoothing: antialiased;
  }

  .czSrlE> :not(:first-child) {
    margin-left: 0px;
  }

  .fuOsjJ {
    color: rgb(0, 0, 0);
    font-family: "Open Sans", sans-serif;
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.5;
    letter-spacing: 0.0025em;
    -webkit-font-smoothing: antialiased;
    white-space: nowrap;
  }

  .Label-sc-ruhf0l-0 {
    display: flex;
    align-items: end;
  }

  .ftIjDA {
    box-sizing: border-box;
    background: rgb(255, 255, 255);
    padding: 16px;
    box-shadow: rgb(43 43 46 / 8%) 0px -1px 4px, rgb(43 43 46 / 8%) 0px 4px 8px;
    border-radius: 8px;
  }

  @media screen and (max-width: 2147483646px) {
    .fJdyDT.fJdyDT>* {
      margin-left: 0px;
    }

    .fJdyDT.fJdyDT> :not(:first-child) {
      margin-top: 24px;
    }
  }

  .eaagmD {
    border: 1px solid rgb(191, 191, 192);
    background: rgb(255, 255, 255);
    display: flex;
    -webkit-box-pack: center;
    justify-content: center;
    border-radius: 4px;
    box-sizing: border-box;
    transition-duration: 350ms;
    transition-property: border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dHomJu {
    width: 100%;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    padding: 0px 16px;
  }

  .YVhge {
    padding: 0px;
    background: transparent;
    border: 0px;
    cursor: pointer;
    color: rgb(43, 43, 46);
    transition-property: color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    height: 24px;
    width: 24px;
  }

  .YVhge:disabled {
    cursor: not-allowed;
    opacity: 0.32;
  }

  .fcNaly {
    font-family: "Mews Icons";
    font-style: normal;
    font-weight: 400;
    display: inline-block;
    line-height: 1;
    text-transform: none;
    letter-spacing: normal;
    overflow-wrap: normal;
    white-space: nowrap;
    direction: ltr;
    color: inherit;
    font-size: 24px;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizelegibility;
    font-feature-settings: "liga";
  }

  .eHzrow {
    display: flex;
    pointer-events: none;
  }

  .jyRZtS {
    flex: 1 1 0%;
  }

  .gqVxEp {
    height: 100%;
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
    box-sizing: border-box;
  }

  .kYwDEo {
    width: 100%;
    position: relative;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    display: flex;
  }

  .iDOcKT {
    font-family: "Open Sans", sans-serif;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    color: rgb(0, 0, 0);
    width: 100%;
    box-sizing: border-box;
    padding: 7px 16px;
    border: 0px;
    background: transparent;
    outline: none;
    display: block;
    margin: 0px;
    appearance: none;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    text-align: left;
  }

  .dHomJu input {
    text-align: center;
    box-sizing: border-box;
  }

  .YVhge {
    padding: 0px;
    background: transparent;
    border: 0px;
    cursor: pointer;
    color: rgb(43, 43, 46);
    transition-property: color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    height: 24px;
    width: 24px;
  }

  .YVhge:disabled {
    cursor: not-allowed;
    opacity: 0.32;
  }

  .fcNaly {
    font-family: "Mews Icons";
    font-style: normal;
    font-weight: 400;
    display: inline-block;
    line-height: 1;
    text-transform: none;
    letter-spacing: normal;
    overflow-wrap: normal;
    white-space: nowrap;
    direction: ltr;
    color: inherit;
    font-size: 24px;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizelegibility;
    font-feature-settings: "liga";
  }

  .eHzrow {
    display: flex;
    pointer-events: none;
  }

  .style__StepElement-sc-1sy1og7-5 {
    display: flex;
    align-items: center;
  }

  .gBGbt> :not(:first-child) {
    margin-left: 16px;
  }

  @media screen and (max-width: 2147483646px) {
    .jStchL {
      flex-direction: column;
    }
  }

  .jStchL {
    display: flex;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    justify-content: flex-start;
    margin: 0px;
    padding: 0px;
  }

  .cFmKju {
    display: flex;
    flex-wrap: nowrap;
    margin: 0px;
    padding: 0px;
    -webkit-box-pack: justify;
    justify-content: space-between;
    align-items: flex-end;
  }

  @media screen and (min-width: 600px) {
    .cFmKju {
      flex-direction: column;
    }
  }

  @media screen and (max-width: 2147483646px) {
    .jStchL.jStchL>* {
      margin-left: 0px;
    }
  }

  .kXgQpS {
    color: rgb(0, 0, 0);
    font-family: "Open Sans", sans-serif;
    font-size: 1.3125rem;
    font-weight: 700;
    line-height: 1.3;
    letter-spacing: -0.0025em;
    -webkit-font-smoothing: antialiased;
    white-space: nowrap;
  }

  @media screen and (min-width: 600px) {
    .cFmKju.cFmKju>* {
      margin-left: 0px;
    }
  }

  .krAfZo {
    flex: 1 1 0%;
    border-top: 1px solid rgb(230, 230, 230);
    border-right-color: rgb(230, 230, 230);
    border-bottom-color: rgb(230, 230, 230);
    border-left-color: rgb(230, 230, 230);
  }

  .fmfdRL>* {
    -webkit-box-flex: 1;
    flex-grow: 1;
    width: 100%;
  }

  .ckIMMf {
    text-align: left;
  }

  .dCdeYC {
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    margin-bottom: 2px;
  }

  .eaagmD {
    border: 1px solid rgb(191, 191, 192);
    background: rgb(255, 255, 255);
    display: flex;
    -webkit-box-pack: center;
    justify-content: center;
    border-radius: 4px;
    box-sizing: border-box;
    transition-duration: 350ms;
    transition-property: border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dgoBoQ {
    font-family: "Open Sans", sans-serif;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1.5;
    letter-spacing: 0.005em;
    -webkit-font-smoothing: antialiased;
    color: rgba(0, 0, 0, 0.64);
    padding: 0px;
  }

  .kzckpq {
    display: flex;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    justify-content: flex-start;
    margin: 0px;
    padding: 0px;
  }

  .gqVxEp {
    height: 100%;
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
    box-sizing: border-box;
  }

  .hYznmx {
    width: 100%;
    position: relative;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    display: flex;
  }

  .iDOcKT {
    font-family: "Open Sans", sans-serif;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    color: rgb(0, 0, 0);
    width: 100%;
    box-sizing: border-box;
    padding: 7px 16px;
    border: 0px;
    background: transparent;
    outline: none;
    display: block;
    margin: 0px;
    appearance: none;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    text-align: left;
  }

  .fmfdRL> :not(:first-child) {
    margin-left: 16px;
  }

  .kVMTZe {
    padding-left: 4px;
    color: rgba(0, 0, 0, 0.64);
  }

  .active-btn {
    border: solid 2px #444444;
    color: #fff;
    background-color: #444444;
  }

  .pointer {
    cursor: pointer !important;
  }
</style>