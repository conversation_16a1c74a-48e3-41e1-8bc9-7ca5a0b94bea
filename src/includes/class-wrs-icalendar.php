<?php
// substr($product_calendar_url, 0, 45)
class WodaabeReservationsCalendar_Table {

  public function list_table_page() {
      // Fetch and display products from your database
                        $products = $this->get_products();
        ?>
            <div class="wrap">
                <h1>Reservation Calendar</h1>
                <p>Export and view reservations in iCalendar format.</p>
                <div id="products_calendars">
                    <?php
                        $products_Table = new WodaabeReservationsProducts_Table();
                        $price_listings = $products_Table->get_pricelabs_listings();
                        wrs_log($price_listings);

                        // Create an associative array for quick lookup
                        $price_listings_lookup = array();
                        foreach ($price_listings as $listing) {
                            $price_listings_lookup[$listing['id']] = $listing;
                        }

                        $colorCombinations = array(
                            array("textColor" => "navy", "backgroundColor" => "lightgray"),
                            array("textColor" => "darkorange", "backgroundColor" => "lightcyan"),
                            array("textColor" => "darkslateblue", "backgroundColor" => "lightcoral"),
                            array("textColor" => "darkgreen", "backgroundColor" => "lightgoldenrodyellow"),
                            array("textColor" => "darkmagenta", "backgroundColor" => "lightseagreen"),
                            array("textColor" => "darkolivegreen", "backgroundColor" => "lightsalmon"),
                            array("textColor" => "darkslategray", "backgroundColor" => "lightpink"),
                            array("textColor" => "darkturquoise", "backgroundColor" => "lightsteelblue"),
                            array("textColor" => "darkviolet", "backgroundColor" => "lightyellow"),
                            array("textColor" => "firebrick", "backgroundColor" => "lightblue"),
                            array("textColor" => "forestgreen", "backgroundColor" => "lightyellow"),
                            array("textColor" => "indigo", "backgroundColor" => "lightcoral"),
                            array("textColor" => "maroon", "backgroundColor" => "lightgoldenrodyellow"),
                            array("textColor" => "mediumblue", "backgroundColor" => "lightgray"),
                            array("textColor" => "mediumseagreen", "backgroundColor" => "lightcyan"),
                            array("textColor" => "mediumslateblue", "backgroundColor" => "lightcoral"),
                            array("textColor" => "orangered", "backgroundColor" => "lightsteelblue"),
                            array("textColor" => "purple", "backgroundColor" => "lightpink"),
                            array("textColor" => "royalblue", "backgroundColor" => "lightyellow"),
                            array("textColor" => "saddlebrown", "backgroundColor" => "lightsalmon"),
                            array("textColor" => "sienna", "backgroundColor" => "lightcoral"),
                            array("textColor" => "teal", "backgroundColor" => "lightblue"),
                            array("textColor" => "darkslategray", "backgroundColor" => "lightgreen"),
                            array("textColor" => "midnightblue", "backgroundColor" => "lightgray")
                        );

                        $index = 10;
                        foreach ($products as $product) {
                            $product_calendar_url = esc_url(plugin_dir_url(__FILE__) . 'product_calendars/wodaabe_rental_' . $product['id'] . '.ics');
                            $price_listing = isset($price_listings_lookup[$product['price_listing_id']]) ? $price_listings_lookup[$product['price_listing_id']] : null;

                            $price_checkbox = $price_listing ?
                                "<div style='display: flex; place-items: center;'>
                                    <input type=\"checkbox\" id=\"prices" . $index . "\" name=\"prices\" />
                                    <label for=\"prices" . $index . "\">Prices</label>
                                </div>" : "";

                            $price_listing_data = $price_listing ?
                                "data-price-listing-id='" . esc_attr($price_listing['id']) . "' " .
                                "data-price-listing-pms='" . esc_attr($price_listing['pms']) . "' " : "";

                            echo "<div class=\"card\">
                                    <img class=\"card-img\" src=\"" . esc_html($product['picture']) . "\" alt=\"" . esc_html($product['title']) . "\" />
                                    <div class='wrapper'>
                                        <div class='space'>
                                            <span class='long'>" . esc_html($product['title']) . "</span>
                                            <span>
                                                <button class='my-button view-calendar' data-id='" . $product['id'] . "' " .
                                                "data-tc='" . $colorCombinations[$index]['textColor'] . "' " .
                                                "data-bg='" . $colorCombinations[$index]['backgroundColor'] . "' " .
                                                $price_listing_data .
                                                "style='background-color:" . $colorCombinations[$index]['textColor'] . ";color:" . $colorCombinations[$index]['backgroundColor'] . ";'>View</button>
                                                <a class='my-button' href=\"" . $product_calendar_url . "\" download='wodaabe_rental_" . $product['id'] . ".ics'>Download</a>
                                            </span>
                                        </div>
                                        <div class='space'>
                                            <span class='long link' data-full-url=\"" . esc_attr($product_calendar_url) . "\">" .
                                                '...' . esc_html(substr($product_calendar_url, -40))  .
                                            "</span>
                                            <span class='buttons'> " . $price_checkbox .
                                                "<div style='display: flex; place-items: center;'>
                                                    <input type=\"checkbox\" id=\"bookings" . $index . "\" name=\"bookings\" checked />
                                                    <label for=\"bookings" . $index . "\">Bookings</label>
                                                </div>
                                                <button class='my-button copy-link'>Copy link</button>
                                            </span>
                                        </div>
                                        <div class='space'>
                                            <input type=\"text\" placeholder=\"External calendar link\"  class='long' value='" . $product['sync_url'] . "' />
                                            <span class='buttons'>
                                                " . (!empty($product['sync_url']) ? "<button class='my-button unsync' data-id='" . $product['id'] . "'>Unsync</button>" : "") .
                                                "<button class='my-button sync' data-id='" . $product['id'] . "'>Synchronize</button>
                                            </span>
                                        </div>
                                        <div class='space api-sync-section'>
                                            <div class='long'>
                                                <strong>Hostaway API Integration:</strong> " .
                                                (empty($product['listingMapId']) ?
                                                    "<span class='api-status inactive'>Not configured</span>" :
                                                    "<span class='api-status active'>Active (ID: " . $product['listingMapId'] . ")</span>") . "
                                            </div>
                                            <div class='buttons'>
                                                " . (!empty($product['listingMapId']) ? "<button class='my-button api-unsync' data-id='" . $product['id'] . "'>Disable API</button>" : "") .
                                                "<button class='my-button api-config' data-id='" . $product['id'] . "'>Configure API</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>";
                            $index++;
                        }
                    ?>
                </div>
                <div id="calendar"></div>
                <style>
                    #listing_sync {
                        border-radius: 20px;
                        width: 100%;
                    }
                    .my-button {
                        display: inline-block;
                        padding: 8px 15px;
                        color: white;
                        text-align: center;
                        text-decoration: none;
                        font-size: 16px;
                        margin: 4px 2px;
                        cursor: pointer;
                        border-radius: 18px;
                        border-width: 0px;
                        background-color: #007bff;
                    }
                    .my-button:hover {
                        background-color: #556677;
                        color: #e8f0fe;
                    }
                    .card {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: flex-start;
                        border-radius: 15px;
                        padding: 15px;
                        width: 100%;
                        max-width: 1000px;
                        margin-bottom: 20px;
                        background-color: #f5f5f5;
                        box-shadow: 0px 2px 5px rgba(0,0,0,0.15);
                    }
                    .card-img {
                        height: 140px;
                        border-radius: 5px;
                        align-self: flex-start;
                    }
                    .card span {
                        font-family: 'Open Sans', sans-serif;
                        font-size: 18px;
                        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
                    }
                    .wrapper {
                        flex-grow: 4;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        margin-left: 15px;
                        min-height: 140px;
                        height: auto;
                    }
                    .buttons {
                        width: 40%;
                        display: flex;
                        justify-content: flex-end;
                        gap: 5px;
                    }
                    .space {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    .long {
                        width: 60%;
                        border-radius: 20px !important;
                        height: fit-content;
                        /* Add these properties for better text handling */
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                        hyphens: auto;
                    }

                    .space {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 10px;
                        width: 100%;
                    }


                    button.view-calendar:hover {
                        background-color: #556677 !important;
                        color: #e8f0fe !important;
                    }
                    .fc-day {
                        position: relative;
                    }
                    .day-indication {
                        position: absolute;
                        bottom: 10px;
                        left: 10px;
                        font-size: 30px;
                        font-weight: 300;
                        color: green;
                    }

                    .api-status {
                        display: inline-block;
                        padding: 3px 8px;
                        border-radius: 10px;
                        font-size: 14px;
                        margin-left: 10px;
                    }

                    .api-status.active {
                        background-color: #d4edda;
                        color: #155724;
                        border: 1px solid #c3e6cb;
                    }

                    .api-status.inactive {
                        background-color: #f8d7da;
                        color: #721c24;
                        border: 1px solid #f5c6cb;
                    }

                    .api-sync-section {
                        border-top: 1px solid #e0e0e0;
                        padding-top: 10px;
                        width: 100%;
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-between;
                        align-items: center;
                    }

                    .api-sync-section .long {
                        width: 60%;
                        display: flex;
                        align-items: center;
                    }

                    .api-sync-section .buttons {
                        width: 40%;
                        display: flex;
                        justify-content: flex-end;
                        gap: 5px;
                    }

                    /* Mobile Responsive Styles */
                    @media screen and (max-width: 768px) {
                        .card {
                            flex-direction: column;
                            align-items: flex-start;
                            padding: 10px;
                        }
                        .card-img {
                            width: 100%;
                            height: auto;
                            margin-bottom: 10px;
                        }
                        .wrapper {
                            width: 100%;
                            margin-left: 0;
                            height: auto;
                        }
                        .buttons {
                            width: 100%;
                            justify-content: flex-start;
                            margin-top: 10px;
                        }
                        .space, .api-sync-section {
                            flex-direction: column;
                            align-items: flex-start;
                            width: 100%;
                        }
                        .long, .api-sync-section .long {
                            width: 100%;
                            margin-bottom: 10px;
                            /* Ensure text doesn't overflow on mobile */
                            max-width: 100%;
                            box-sizing: border-box;
                            padding: 5px;  /* Add some padding for better readability */
                        }
                        .api-sync-section .buttons {
                            width: 100%;
                            justify-content: flex-start;
                            flex-wrap: wrap;
                        }

                        .api-sync-section .my-button {
                            margin-top: 5px;
                        }
                        .my-button {
                            width: 100%;
                            margin: 5px 0;
                        }
                        .card span {
                            font-size: 16px;
                        }
                        .day-indication {
                            font-size: 14px;
                            bottom: 5px;
                            left: 5px;
                        }
                    }
                </style>
                <script>
                    jQuery(document).ready(function($) {

                        <?php
                            // Initialize an empty JavaScript array
                            $jsArray = "var products = [];\n";

                            // Iterate through the PHP array and append URLs to the JS array
                            foreach ($products as $product) {
                                $product_calendar_url = esc_url(plugin_dir_url(__FILE__) . 'product_calendars/wodaabe_rental_' . $product['id'] . '.ics');
                                $url = json_encode($product_calendar_url, JSON_UNESCAPED_SLASHES); // Properly encode URL for JS
                                $jsArray .= "products.push({id:" . $product['id'] . ", url:" . $url . "});\n";
                            }

                            // Output the JavaScript code
                            echo "$jsArray\n";
                        ?>


                        var prices_id = null;
                        var prices_pms = null;

                        var calendarEl = document.getElementById('calendar');
                        var blockingsDisplayed = false;
                        var calendar = new FullCalendar.Calendar(calendarEl, {
                            initialView: 'dayGridMonth',
                            datesSet: function(info) {
                                  console.log(info);
                                  displayPricesWithAttributes(prices_id, prices_pms);
                                },
                             customButtons: {
                                updateCal: {
                                  text: '🔄 Update Calendars',
                                  click: function() {
                                    // Trigger an Update of the reservations calendars
                                    wrs.getCalendarEvents();

                                    let events = calendar.getEventSources();
                                    console.log("Events: ", events);

                                    calendar.refetchEvents();

                                    new Notify ({
                                        status: 'success',
                                        title: 'iCal files up to date!',
                                        text: 'All iCal files have been updated.',
                                        effect: 'slide',
                                        speed: 1000,
                                        customClass: '',
                                        customIcon: '',
                                        showIcon: true,
                                        showCloseButton: true,
                                        autoclose: true,
                                        autotimeout: 2500,
                                        notificationsGap: null,
                                        notificationsPadding: null,
                                        type: 'outline',
                                        position: 'y-center left',
                                        customWrapper: '',
                                    });
                                  }
                                },
                                clearCal: {
                                    text: 'Clear Calendar',
                                    click: function() {
                                        let events = calendar.getEventSources();
                                        //console.log("Events: ", events);

                                        events.forEach((event) => {
                                            event.remove();
                                        });

                                        $('.fc-day').find('.day-indication').remove();

                                        prices_id = null;
                                        prices_pms = null;

                                        new Notify ({
                                            status: 'info',
                                            title: 'Calendar reset',
                                            text: 'The calendar has been emptied.',
                                            effect: 'slide',
                                            speed: 1000,
                                            customClass: '',
                                            customIcon: '',
                                            showIcon: true,
                                            showCloseButton: true,
                                            autoclose: true,
                                            autotimeout: 1500,
                                            notificationsGap: null,
                                            notificationsPadding: null,
                                            type: 'outline',
                                            position: 'y-center left',
                                            customWrapper: '',
                                        });
                                    }
                                },
                                viewAllCals: {
                                    text: '📅 View All Calendars',
                                    click: function() {
                                        let events = calendar.getEventSources();
                                        //console.log("Events: ", events);

                                        events.forEach((event) => {
                                            event.remove();
                                        });

                                        products.forEach(function(product) {
                                            let button = $(".my-button[data-id='" + product.id + "']");
                                            // Retrieve the data-bg and data-tc attributes
                                            let textColor = button.data('bg');
                                            let backgroundColor = button.data('tc');

                                            calendar.addEventSource({
                                                url: product.url,
                                                format: 'ics',
                                                id: product.id,
                                                cache: false,
                                                color: backgroundColor,
                                                textColor: textColor
                                            });
                                        });

                                        calendar.refetchEvents();

                                        new Notify ({
                                            status: 'success',
                                            title: 'Calendar up to date!',
                                            text: 'All calendars displayed.',
                                            effect: 'slide',
                                            speed: 1000,
                                            customClass: '',
                                            customIcon: '',
                                            showIcon: true,
                                            showCloseButton: true,
                                            autoclose: true,
                                            autotimeout: 2500,
                                            notificationsGap: null,
                                            notificationsPadding: null,
                                            type: 'outline',
                                            position: 'y-center left',
                                            customWrapper: '',
                                        });
                                    }
                                },
                                displayBlockings: {
                                    text: '📅 Display Blockings',
                                    click: function() {
                                        wrs.getBlockedPeriods().then(
                                            (data) => {
                                                console.log('Blockings : ', data);

                                                // Group events by product_id
                                                const eventsByProduct = data.reduce((acc, event) => {
                                                    if (!acc[event.product_id]) {
                                                        acc[event.product_id] = [];
                                                    }
                                                    acc[event.product_id].push(event);
                                                    return acc;
                                                }, {});

                                                // Add each product's events as a separate event source
                                                Object.entries(eventsByProduct).forEach(([productId, events]) => {
                                                    const button = document.querySelector(`button.view-calendar[data-id="${productId}"]`);
                                                    if (button) {
                                                        const textColor = button.getAttribute('data-tc');
                                                        const backgroundColor = button.getAttribute('data-bg');

                                                        const transformedEvents = events.map(event => ({
                                                            title: event.comment,
                                                            start: event.start_date,
                                                            end: event.end_date,
                                                            extendedProps: {
                                                                product_id: event.product_id
                                                            }
                                                        }));

                                                        /*
                                                        if (blockingsDisplayed) {
                                                            let events = calendar.getEventSources();
                                                            //console.log("Events: ", events);

                                                            events.forEach((event) => {
                                                                event.remove();
                                                            });
                                                        }
                                                        */

                                                        // Remove existing event source for this product if it exists
                                                        const existingSource = calendar.getEventSourceById(`product-${productId}`);
                                                        if (existingSource) {
                                                            existingSource.remove();
                                                        }

                                                        calendar.addEventSource({
                                                            id: `product-${productId}`,
                                                            events: transformedEvents,
                                                            color: textColor,          // Using the same text color as background
                                                            textColor: backgroundColor // Using the same background color as text
                                                        });

                                                        blockingsDisplayed = true;
                                                    }
                                                });

                                                new Notify ({
                                                    status: 'success',
                                                    title: 'Blockings',
                                                    text: 'All blockings displayed.',
                                                    effect: 'slide',
                                                    speed: 1000,
                                                    customClass: '',
                                                    customIcon: '',
                                                    showIcon: true,
                                                    showCloseButton: true,
                                                    autoclose: true,
                                                    autotimeout: 2500,
                                                    notificationsGap: null,
                                                    notificationsPadding: null,
                                                    type: 'outline',
                                                    position: 'y-center left',
                                                    customWrapper: '',
                                                });
                                            }
                                        )
                                    }
                                }
                              },
                              headerToolbar: {
                                left: 'updateCal clearCal viewAllCals displayBlockings',
                                center: 'title',
                                right: 'prev,next today'
                              },
                            events: {
                                url: "",
                                format: 'ics',
                                lazyFetching: false,
                                cache: false
                            },
                            displayEventTime: false
                        });

                        calendar.render();

                        // Event listener for when the price listing selection changes
                        //$('#listing_sync').on('change', displayPrices);

                        // Event handler for "Prices" checkbox
                        $('input[name="prices"]').change(function() {
                            if (!$(this).is(':checked')) {
                                var priceListingId = $(this).closest('.card').find('.view-calendar').data('price-listing-id');
                                if (prices_id === priceListingId) {
                                    prices_id = null;
                                    prices_pms = null;
                                    $('.fc-day').find('.day-indication').remove();
                                }
                            }
                        });

                        // Event handler for "Copy link" button
                        $('.my-button.copy-link').click(function() {
                            // Get the full URL from the data attribute instead of the displayed text
                            var linkText = $(this).closest('.card').find('.link').data('full-url');

                            // Create a temporary input element
                            var tempInput = $('<input>');
                            $('body').append(tempInput);

                            // Set the value of the input to the full URL
                            tempInput.val(linkText);

                            // Select the input text
                            tempInput[0].select();

                            try {
                                // Copy the selected text to the clipboard
                                document.execCommand('copy');

                                // Show success notification
                                new Notify ({
                                    status: 'success',
                                    title: 'Link copied successfully!',
                                    text: 'The link is in your clipboard.',
                                    effect: 'slide',
                                    speed: 1000,
                                    customClass: '',
                                    customIcon: '',
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 2500,
                                    notificationsGap: null,
                                    notificationsPadding: null,
                                    type: 'outline',
                                    position: 'y-center left',
                                    customWrapper: '',
                                });
                            } catch (err) {
                                // Show error notification if copying fails
                                new Notify ({
                                    status: 'error',
                                    title: 'Copy failed',
                                    text: 'Could not copy link to clipboard.',
                                    effect: 'slide',
                                    speed: 1000,
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 2500,
                                    type: 'outline',
                                    position: 'y-center left',
                                });
                                console.error('Failed to copy URL:', err);
                            } finally {
                                // Remove the temporary input element
                                tempInput.remove();
                            }
                        });

                        // Event handler for "View" button
                        $('.my-button.view-calendar').click(function() {
                            // Retrieve the information from the data attribute
                            var id = $(this).data('id');
                            var textColor = $(this).data('tc');
                            var backgroundColor = $(this).data('bg');
                            console.log("Text Color: " + textColor, "Background Color: " + backgroundColor);
                            // Get the product calendar URL associated with this card
                            var productCalendarURL = $(this).closest('.card').find('a.my-button').attr('href');
                            console.log("Updating FullCalendar to display " + productCalendarURL);

                            var priceListingId = $(this).data('price-listing-id');
                            var priceListingPms = $(this).data('price-listing-pms');

                            prices_id = priceListingId;
                            prices_pms = priceListingPms;

                            let events = calendar.getEventSources();
                            //console.log("Events: ", events);

                            events.forEach((event) => {
                                event.remove();
                            });

                            var bookingsCheckbox = $(this).closest('.card').find('input[name="bookings"]');

                            if (bookingsCheckbox.is(':checked')) {
                                calendar.addEventSource({
                                    url: productCalendarURL,
                                    format: 'ics',
                                    id: id,
                                    cache: false,
                                    color: textColor,
                                    textColor: backgroundColor,
                                });

                                calendar.refetchEvents();
                            }


                            // Check if the product has a price_listing_id and the prices checkbox is checked
                            var priceCheckbox = $(this).closest('.card').find('input[name="prices"]');
                            if (priceCheckbox.length && priceCheckbox.is(':checked') && priceListingId && priceListingPms) {
                                displayPricesWithAttributes(priceListingId, priceListingPms);
                            } else {
                                // Clear any existing price displays
                                $('.fc-day').find('.day-indication').remove();
                            }
                        });

                        function displayBlockings() {

                        }

                        // Modify the displayPrices function to take priceListingId and priceListingPms as parameters
                        async function displayPricesWithAttributes(priceListingId, priceListingPms) {

                            // Return immediately if id or pms is empty
                            if (!priceListingId || !priceListingPms) {
                                console.log("ID or PMS is empty. Exiting function.");
                                /*
                                new Notify ({
                                    status: 'error',
                                    title: 'Error fetching price data',
                                    text: 'ID or PMS not found.',
                                    effect: 'slide',
                                    speed: 3000,
                                    customClass: '',
                                    customIcon: '',
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 3500,
                                    notificationsGap: null,
                                    notificationsPadding: null,
                                    type: 'outline',
                                    position: 'y-center left',
                                    customWrapper: '',
                                });
                                */
                                return;
                            }

                            let earliestDate, latestDate;
                            $('.fc-day').each(function() {
                                const date = $(this).attr('data-date');
                                if (!earliestDate || date < earliestDate) {
                                    earliestDate = date;
                                }
                                if (!latestDate || date > latestDate) {
                                    latestDate = date;
                                }
                            });

                            new Notify ({
                                status: 'info',
                                title: 'Fetching prices',
                                text: 'From ' + earliestDate + ' to ' + latestDate,
                                effect: 'slide',
                                speed: 2000,
                                customClass: '',
                                customIcon: '',
                                showIcon: true,
                                showCloseButton: true,
                                autoclose: true,
                                autotimeout: 3500,
                                notificationsGap: null,
                                notificationsPadding: null,
                                type: 'outline',
                                position: 'y-center left',
                                customWrapper: '',
                            });

                            try {
                                const results = await wrs.ajax(
                                    wrs.payload({
                                        id: priceListingId,
                                        pms: priceListingPms,
                                        dateFrom: earliestDate,
                                        dateTo: latestDate,
                                        reason: true,
                                        wrs_action: 'fetchPriceLabsData',
                                    })
                                );

                                const resultObj = JSON.parse(results);
                                console.log(resultObj);

                                $('.fc-day').each(function() {
                                    const date = $(this).attr('data-date');
                                    const dayData = resultObj[0].data.find(obj => obj.date === date);
                                    const text = dayData ? dayData.price : '';
                                    $(this).find('.day-indication').remove();
                                    $(this).append(`<div class="day-indication">${text}</div>`);
                                });

                                new Notify ({
                                    status: 'success',
                                    title: 'Price fetching successful',
                                    text: 'Prices loaded successfully.',
                                    effect: 'slide',
                                    speed: 2000,
                                    customClass: '',
                                    customIcon: '',
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 3500,
                                    notificationsGap: null,
                                    notificationsPadding: null,
                                    type: 'outline',
                                    position: 'y-center left',
                                    customWrapper: '',
                                });
                            } catch (error) {
                                console.error("Error fetching price data:", error);
                                new Notify ({
                                    status: 'error',
                                    title: 'Error fetching price data',
                                    text: 'Error: ' + error,
                                    effect: 'slide',
                                    speed: 3000,
                                    customClass: '',
                                    customIcon: '',
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 3500,
                                    notificationsGap: null,
                                    notificationsPadding: null,
                                    type: 'outline',
                                    position: 'y-center left',
                                    customWrapper: '',
                                });
                            }
                        }

                        $('.my-button.sync').click(async function() {
                            // Retrieve the information from the data attribute
                            var id = $(this).data('id');
                            console.log("Id is " + id);

                            // Retrieve the value of the External calendar link input
                            var link = $(this).closest('.space').find('input').val();
                            console.log("Link is " + link);

                            const myNotify = new Notify ({
                                status: 'info',
                                title: 'Synchronization in progress',
                                text: 'Looking for new reservations',
                                effect: 'slide',
                                speed: 1000,
                                customClass: '',
                                customIcon: '',
                                showIcon: true,
                                showCloseButton: true,
                                autoclose: true,
                                autotimeout: 1500,
                                notificationsGap: null,
                                notificationsPadding: null,
                                type: 'outline',
                                position: 'y-center left',
                                customWrapper: '',
                            });

                            // Call the syncExternalReservations function with the product ID and External calendar link as parameters
                            const syncResult = await wrs.syncExternalReservations({id, link});
                            console.log("Sync result: ", syncResult);

                            if (!syncResult) {
                                new Notify ({
                                    status: 'error',
                                    title: 'Synchronisation failed',
                                    text: 'Please make sure the link is correct and the iCal format is respected.',
                                    effect: 'slide',
                                    speed: 3000,
                                    customClass: '',
                                    customIcon: '',
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 3500,
                                    notificationsGap: null,
                                    notificationsPadding: null,
                                    type: 'outline',
                                    position: 'y-center left',
                                    customWrapper: '',
                                });
                                return;
                            }

                            calendar.refetchEvents();

                            //myNotify.close();

                            new Notify ({
                                status: 'success',
                                title: 'Synchronization successful!',
                                text: 'All reservations are now up to date.',
                                effect: 'slide',
                                speed: 1000,
                                customClass: '',
                                customIcon: '',
                                showIcon: true,
                                showCloseButton: true,
                                autoclose: true,
                                autotimeout: 2500,
                                notificationsGap: null,
                                notificationsPadding: null,
                                type: 'outline',
                                position: 'y-center left',
                                customWrapper: '',
                            });
                        });

                        $('.my-button.unsync').click(async function() {
                            var id = $(this).data('id');
                            console.log("Id is " + id);

                            const unSyncResult = await wrs.unsyncExternalReservations(id);
                            console.log("Unsync result: ", unSyncResult);

                            // Empty the corresponding input field
                            $(this).closest('.space').find('input').val('');

                            new Notify ({
                                status: 'success',
                                title: 'Synchronization disabled for product #' + id,
                                text: 'All reservations are now up to date.',
                                effect: 'slide',
                                speed: 1000,
                                customClass: '',
                                customIcon: '',
                                showIcon: true,
                                showCloseButton: true,
                                autoclose: true,
                                autotimeout: 2500,
                                notificationsGap: null,
                                notificationsPadding: null,
                                type: 'outline',
                                position: 'y-center left',
                                customWrapper: '',
                            });
                        });

                        // Handle API configuration button click
                        $(document).on('click', '.my-button.api-config', function() {
                            var id = $(this).data('id');
                            console.log("Configuring API for product ID: " + id);

                            // Prompt user for Hostaway listing ID
                            var listingMapId = prompt("Enter the Hostaway listing ID for this product:", "");

                            // Validate input
                            if (listingMapId === null) {
                                // User cancelled
                                return;
                            }

                            // Validate that the input is a number
                            if (!$.isNumeric(listingMapId) || listingMapId.trim() === "") {
                                new Notify ({
                                    status: 'error',
                                    title: 'Invalid Hostaway listing ID',
                                    text: 'Please enter a valid numeric ID.',
                                    effect: 'slide',
                                    speed: 3000,
                                    customClass: '',
                                    customIcon: '',
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 3500,
                                    notificationsGap: null,
                                    notificationsPadding: null,
                                    type: 'outline',
                                    position: 'y-center left',
                                    customWrapper: '',
                                });
                                return;
                            }

                            // Save the Hostaway listing ID
                            saveHostawayListingId(id, listingMapId);
                        });

                        // Handle API unsync button click
                        $(document).on('click', '.my-button.api-unsync', function() {
                            var id = $(this).data('id');
                            console.log("Disabling API for product ID: " + id);

                            // Confirm with the user
                            if (confirm("Are you sure you want to disable API synchronization for this product?")) {
                                // Remove the Hostaway listing ID
                                saveHostawayListingId(id, null);
                            }
                        });

                        // Function to save Hostaway listing ID
                        async function saveHostawayListingId(productId, listingMapId) {
                            const myNotify = new Notify ({
                                status: 'info',
                                title: 'Saving configuration',
                                text: 'Updating Hostaway API configuration...',
                                effect: 'slide',
                                speed: 1000,
                                customClass: '',
                                customIcon: '',
                                showIcon: true,
                                showCloseButton: true,
                                autoclose: true,
                                autotimeout: 1500,
                                notificationsGap: null,
                                notificationsPadding: null,
                                type: 'outline',
                                position: 'y-center left',
                                customWrapper: '',
                            });

                            try {
                                // Call the API to save the Hostaway listing ID
                                const result = await wrs.ajax(
                                    wrs.payload({
                                        wrs_action: 'save_hostaway_listing_id',
                                        product_id: productId,
                                        listing_map_id: listingMapId
                                    })
                                );

                                console.log("Save result:", result);

                                if (result) {
                                    // Update the UI
                                    const apiStatusElement = $(".my-button.api-config[data-id='" + productId + "']")
                                        .closest('.space')
                                        .find('.api-status');

                                    const apiUnsyncButton = $(".my-button.api-config[data-id='" + productId + "']")
                                        .closest('.buttons')
                                        .find('.api-unsync');

                                    if (listingMapId) {
                                        // API enabled
                                        apiStatusElement
                                            .removeClass('inactive')
                                            .addClass('active')
                                            .html('Active (ID: ' + listingMapId + ')');

                                        // Add unsync button if it doesn't exist
                                        if (apiUnsyncButton.length === 0) {
                                            $(".my-button.api-config[data-id='" + productId + "']")
                                                .parent()
                                                .prepend("<button class='my-button api-unsync' data-id='" + productId + "'>Disable API</button>");
                                        }

                                        new Notify ({
                                            status: 'success',
                                            title: 'API configuration saved',
                                            text: 'Hostaway API synchronization is now active for this product.',
                                            effect: 'slide',
                                            speed: 1000,
                                            customClass: '',
                                            customIcon: '',
                                            showIcon: true,
                                            showCloseButton: true,
                                            autoclose: true,
                                            autotimeout: 2500,
                                            notificationsGap: null,
                                            notificationsPadding: null,
                                            type: 'outline',
                                            position: 'y-center left',
                                            customWrapper: '',
                                        });
                                    } else {
                                        // API disabled
                                        apiStatusElement
                                            .removeClass('active')
                                            .addClass('inactive')
                                            .html('Not configured');

                                        // Remove unsync button
                                        if (apiUnsyncButton.length > 0) {
                                            apiUnsyncButton.remove();
                                        }

                                        new Notify ({
                                            status: 'success',
                                            title: 'API synchronization disabled',
                                            text: 'Hostaway API synchronization has been disabled for this product.',
                                            effect: 'slide',
                                            speed: 1000,
                                            customClass: '',
                                            customIcon: '',
                                            showIcon: true,
                                            showCloseButton: true,
                                            autoclose: true,
                                            autotimeout: 2500,
                                            notificationsGap: null,
                                            notificationsPadding: null,
                                            type: 'outline',
                                            position: 'y-center left',
                                            customWrapper: '',
                                        });
                                    }
                                } else {
                                    throw new Error("Failed to save configuration");
                                }
                            } catch (error) {
                                console.error("Error saving Hostaway listing ID:", error);

                                new Notify ({
                                    status: 'error',
                                    title: 'Error saving configuration',
                                    text: 'An error occurred while saving the Hostaway API configuration.',
                                    effect: 'slide',
                                    speed: 3000,
                                    customClass: '',
                                    customIcon: '',
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 3500,
                                    notificationsGap: null,
                                    notificationsPadding: null,
                                    type: 'outline',
                                    position: 'y-center left',
                                    customWrapper: '',
                                });
                            }
                        }

                        async function displayPrices() {
                            // Get the selected price listing ID and PMS
                            const selectedListing = $('#listing_sync').val();
                            const selectedText = $('#listing_sync option:selected').text();
                            const [id, pms] = selectedListing.split('|'); // Assuming the value is stored as "id|pms"

                            // Return immediately if id or pms is empty
                            if (!id || !pms) {
                                console.log("ID or PMS is empty. Exiting function.");
                                new Notify ({
                                    status: 'error',
                                    title: 'Error fetching price data',
                                    text: 'ID or PMS not found.',
                                    effect: 'slide',
                                    speed: 3000,
                                    customClass: '',
                                    customIcon: '',
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 3500,
                                    notificationsGap: null,
                                    notificationsPadding: null,
                                    type: 'outline',
                                    position: 'y-center left',
                                    customWrapper: '',
                                });
                                return;
                            }

                            let earliestDate, latestDate;
                            $('.fc-day').each(function() {
                                const date = $(this).attr('data-date');
                                if (!earliestDate || date < earliestDate) {
                                    earliestDate = date;
                                }
                                if (!latestDate || date > latestDate) {
                                    latestDate = date;
                                }
                            });

                            //console.log("Earliest: ", earliestDate);
                            //console.log("Latest Date: ", latestDate);

                            new Notify ({
                                status: 'info',
                                title: 'Fetching prices from PriceLabs for ' + selectedText,
                                text: 'From ' + earliestDate + ' to ' + latestDate,
                                effect: 'slide',
                                speed: 2000,
                                customClass: '',
                                customIcon: '',
                                showIcon: true,
                                showCloseButton: true,
                                autoclose: true,
                                autotimeout: 3500,
                                notificationsGap: null,
                                notificationsPadding: null,
                                type: 'outline',
                                position: 'y-center left',
                                customWrapper: '',
                            });

                            try {
                                const results = await wrs.ajax(
                                    wrs.payload({
                                        id: id,
                                        pms: pms,
                                        dateFrom: earliestDate,
                                        dateTo: latestDate,
                                        reason: true,
                                        wrs_action: 'fetchPriceLabsData',
                                    })
                                );

                                const resultObj = JSON.parse(results);
                                console.log(resultObj);

                                $('.fc-day').each(function() {
                                    const date = $(this).attr('data-date');
                                    //console.log(date);
                                    const dayData = resultObj[0].data.find(obj => obj.date === date);
                                    //console.log("dayData: ", dayData);
                                    const text = dayData ? dayData.price : '';
                                    //console.log("Text: ", text);
                                    $(this).find('.day-indication').remove(); // Remove any existing .day-indication div
                                    $(this).append(`<div class="day-indication">${text}</div>`); // Append the newly created one
                                });

                                new Notify ({
                                    status: 'success',
                                    title: 'Price fetching successful',
                                    text: 'Prices for ' + selectedText + ' loaded successfully.',
                                    effect: 'slide',
                                    speed: 2000,
                                    customClass: '',
                                    customIcon: '',
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 3500,
                                    notificationsGap: null,
                                    notificationsPadding: null,
                                    type: 'outline',
                                    position: 'y-center left',
                                    customWrapper: '',
                                });
                            } catch (error) {
                                console.error("Error fetching price data:", error);
                                // Display an error message to the user
                                new Notify ({
                                    status: 'error',
                                    title: 'Error fetching price data',
                                    text: 'Error: ' + error,
                                    effect: 'slide',
                                    speed: 3000,
                                    customClass: '',
                                    customIcon: '',
                                    showIcon: true,
                                    showCloseButton: true,
                                    autoclose: true,
                                    autotimeout: 3500,
                                    notificationsGap: null,
                                    notificationsPadding: null,
                                    type: 'outline',
                                    position: 'y-center left',
                                    customWrapper: '',
                                });
                            }
                        }
                    });
                </script>
            </div>
        <?php
    }

    // Helper function to get reservations
    public function get_full_reservations($reservations) {
        //wrs_log("get_full_reservations");
        global $wrsModule;

        $fullReservations = [];
        foreach ($reservations as $reservation) {
            //wrs_log("reservation: " . print_r($reservation, true));
            $items = $wrsModule->get_reservation_items($reservation[0]["id"]);
            //wrs_log("Items: " . print_r($items, true));
    		$reservation["items"] = $items;
    		$person = $wrsModule->get_reservation_person($reservation[0]["person_id"]);
    		$reservation["person"] = $person;
            $location = $wrsModule->get_reservation_location($items[0]["product"]["location_id"]);
            $reservation["location"] = $location;
            //wrs_log("location: " . print_r($location, true));
            //wrs_log("reservation: " . print_r($reservation, true));
    		$fullReservations[] = $reservation;
    		//wrs_log(" Full reservation: " . print_r(end($fullReservations), true));
        }
        return $fullReservations;
    }

    public function get_reservations() {
        global $wpdb;

        $table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
        $sql = "SELECT * FROM " . $table_name . " WHERE status = 'Active'";

        $reservations = $wpdb->get_results($sql, ARRAY_A);
        return $reservations;
    }

    public function get_reservations_by_product($product_id) {
        //wrs_log("get_reservations_by_product for prod_id: " . $product_id);
        global $wpdb;
        $table_name = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;
        $sql = "SELECT reservation_id FROM " . $table_name . " WHERE product_id = " . $product_id . ";";
        $res = $wpdb->get_results($sql, ARRAY_A);

        $table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
        $reservations = [];

        foreach ($res as $product_res) {
            $sql = "SELECT * FROM " . $table_name . " WHERE id = " . $product_res['reservation_id'] . ";";
            $reservations[] = $wpdb->get_results($sql, ARRAY_A);
        }
        return $reservations;
    }

    // Helper function to get locations from the database
    public function get_locations() {
        global $wpdb;
        $locations = $wpdb->get_results("SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE, ARRAY_A);
        return $locations;
    }

    // Helper function to get products from the database
    public function get_products() {
        global $wpdb;
        $products = $wpdb->get_results("SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE, ARRAY_A);
        return $products;
    }

    public function filter_reservations($reservations, $start_date, $end_date) {

        $filtered_reservations = array_filter($reservations, function ($reservation) use ($start_date, $end_date) {
            // Implement your filtering conditions here
            // Return true for reservations that match the criteria, false otherwise
            return (
                ($start_date === null || strtotime($reservation['date_arrive']) >= strtotime($start_date)) &&
                ($end_date === null || strtotime($reservation['date_departure']) <= strtotime($end_date))
            );
        });

        return $filtered_reservations;
    }

    public function get_blocked_periods($product_id) {
        //wrs_log("Getting blocked periods for product # " . $product_id);
        global $wpdb;
        $table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCTS_BLOCK_TABLE;
        $sql = "SELECT * FROM " . $table_name . " WHERE product_id = %d";
        //wrs_log($wpdb->prepare($sql, $product_id));
        return $wpdb->get_results($wpdb->prepare($sql, $product_id), ARRAY_A);
    }

    public function generate_icalendar_content($reservations, $product_id = null) {
        $icalendar_content = "BEGIN:VCALENDAR\r\n";
        $icalendar_content .= "PRODID:Wodaabe Stays\r\n";
        $icalendar_content .= "VERSION:2.0\r\n";

        foreach ($reservations as $reservation) {
            //wrs_log("gic resa : " . print_r($reservation, true));
            $description = "Phone: " . $reservation['person']['phone'] . " Number of Guests: " . ($reservation[0]['adult_count'] + $reservation[0]['child_count']) . " Total price: " . ($reservation['items'][0]['quantity'] * $reservation['items'][0]['product']["price_per_night"]) . " Nights: " . $reservation['items'][0]['quantity'] . " Channel: wodaabe-stays.com";
            //wrs_log("Description: " . $description);
            // Add iCalendar entry for each reservation
            // Customize this part based on your reservation structure
            // Construct event properties
            $event = "BEGIN:VEVENT\r\n";
            $event .= "UID:" . $reservation[0]['uuid'] . "\r\n";
            $dtstamp = gmdate('Ymd\THis\Z', strtotime($reservation[0]['date_add']));
            $event .= "DTSTAMP:$dtstamp\r\n";
            $event .= "SUMMARY:" . $this->escape_text(print_r($reservation['person']['first_name'], true) . " " . print_r($reservation['person']['last_name'], true) . " on wodaabe-stays.com") . "\r\n";
            $event .= "DESCRIPTION:" . $this->escape_text($description) . "\r\n";
            $event .= "DTSTART;VALUE=DATE:" . $this->format_date($reservation[0]['date_arrive']) . "\r\n";
            $event .= "DTEND;VALUE=DATE:" . $this->format_date($reservation[0]['date_departure']) . "\r\n";
            $event .= "STATUS:CONFIRMED\r\n";
            $event .= "END:VEVENT\r\n";

            // Add the event to the calendar
            $icalendar_content .= $event;
        }

         // Add blocked periods if product_id is provided
        if ($product_id) {
            $blocked_periods = $this->get_blocked_periods($product_id);
            //wrs_log("Number of blocked periods for product # " . count($blocked_periods));
            //wrs_log("Blocked periods: ");
            //wrs_log(print_r($blocked_periods, true));
            foreach ($blocked_periods as $period) {
                //wrs_log(print_r($period, true));
                $event = "BEGIN:VEVENT\r\n";
                $event .= "UID:blocked-" . $period['id'] . "\r\n";
                $dtstamp = gmdate('Ymd\THis\Z', strtotime($period['date_add']));
                $event .= "DTSTAMP:$dtstamp\r\n";
                $event .= "SUMMARY:" . $this->escape_text("BLOCKED: " . $period['comment']) . "\r\n";
                $event .= "DESCRIPTION:" . $this->escape_text("Period blocked for : " . $period['comment']) . "\r\n";
                $event .= "DTSTART;VALUE=DATE:" . $this->format_date($period['start_date']) . "\r\n";
                $event .= "DTEND;VALUE=DATE:" . $this->format_date($period['end_date']) . "\r\n";
                $event .= "STATUS:CONFIRMED\r\n";
                $event .= "END:VEVENT\r\n";

                $icalendar_content .= $event;
            }
        }

        $icalendar_content .= "END:VCALENDAR";

        return $icalendar_content;
    }

    // Helper function to format datetime according to iCalendar specification
    private function format_date($datetime) {
        return date('Ymd', strtotime($datetime));
    }

    // Helper function to escape special characters in text fields
    private function escape_text($text) {
        // Replace semicolons and commas with their escaped versions
        $text = str_replace([';', ','], ['\;', '\,'], $text);
        return $text;
    }

    public function generate_icalendar_file($reservation) {
        wrs_log("Starting generate_icalendar_file");

        // Generate iCalendar content based on provided reservations
        $icalendar_content = $this->generate_icalendar_content($reservations);

        // Set the content type and headers for iCalendar file
        header('Content-Type: text/calendar; charset=utf-8');
        header('Content-Disposition: attachment; filename=calendar.ics');

        // Output the iCalendar content
        echo $icalendar_content;

        // Ensure no further output is sent
        exit;
    }

    public function generate_and_store_product_icalendar($product_id) {
        // Fetch reservations for the specified product
        $reservations = $this->get_full_reservations($this->get_reservations_by_product($product_id));
        //wrs_log("resa: " . print_r($reservations, true));
        //wrs_log("Number of reservations: " . count($reservations));
        // Generate iCalendar content based on reservations
        $icalendar_content = $this->generate_icalendar_content($reservations, $product_id);

        // Set the directory path
        $directory_path = plugin_dir_path(__FILE__) . 'product_calendars/';

        // Ensure the directory exists or create it
        wp_mkdir_p($directory_path);

        // Save content to a file (e.g., within the plugin directory)
        $file_path = $directory_path . 'wodaabe_rental_' . $product_id . '.ics';

        // Check if the file exists and delete it if it does
        if (file_exists($file_path)) {
            unlink($file_path);
            //wrs_log($file_path . " existed and was deleted.");
        }

        $file_size = file_put_contents($file_path, $icalendar_content);
        //wrs_log($file_path . " created with size " . $file_size);
    }

    public function update_reservation_calendars() {
        $products = $this->get_products();

        foreach ($products as $product) {
            //wrs_log("Updating product reservation calendar for " . $product['id']);
            $this->generate_and_store_product_icalendar($product['id']);
        }
    }
}