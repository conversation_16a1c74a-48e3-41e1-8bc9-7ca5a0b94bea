<?php
// Promo codes

class Prices_And_Promos_Page {

    private $table_name;
    private $products_table_name;
    private $promo_products_table_name;
    
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'wrs_promotional_codes';
        $this->products_table_name = $wpdb->prefix . 'wrs_products';
        $this->promo_products_table_name = $wpdb->prefix . 'wrs_promotional_code_products';
    }

    public function list_table_page() {
        if (isset($_GET['action'])) {
            $action = $_GET['action'];
            if ('show' == $action) {
                return $this->show_details_page();
            } else if ('new' == $action) {
                return $this->show_create_page();
            } else if ('delete' == $action) {
                return $this->do_delete();
            } else if ('edit' == $action) {
                return $this->show_edit_page();
            }
        }

        $this->handle_form_submissions();

        $promotional_codes = $this->get_promotional_codes();
        $new_link = admin_url('admin.php?page=prices-and-promos&action=new');
        ?>
        <div class="wrap">
            <h2 style="display: inline-block;">Promotional Codes</h2>
            <a style="display: inline-block;" href="<?php echo $new_link; ?>" class="page-title-action">Add New</a>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Code</th>
                        <th>Value</th>
                        <th>Discount Type</th>
                        <th>Description</th>
                        <th>Expiry Date</th>
                        <th>Date Added</th>
                        <th>Date Updated</th>
                        <th>Linked Products</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $this->render_promotional_codes($promotional_codes); ?>
                </tbody>
            </table>
        </div>
        <?php
    }

    private function show_create_page() {
        if (isset($_POST['action']) && $_POST['action'] === 'create') {
            $code = sanitize_text_field($_POST['code']);
            $value = sanitize_text_field($_POST['value']);
            $discount_type = sanitize_text_field($_POST['discount_type']);
            $date_expiry = sanitize_text_field($_POST['date_expiry']);
            $description = sanitize_textarea_field($_POST['description']);
            $product_ids = isset($_POST['product_ids']) ? array_map('intval', $_POST['product_ids']) : array();

            $promo_id = $this->create_promotional_code($code, $value, $discount_type, $date_expiry, $description);
            $this->link_products_to_promo($promo_id, $product_ids);
            wp_redirect(admin_url('admin.php?page=prices-and-promos'));
            exit;
        }
        
        $products = $this->get_all_products();
        ?>
        <div class="wrap">
            <h1>Add New Promotional Code</h1>
            <form method="post" id="add-code-form">
                <input type="hidden" name="action" value="create">
                <table class="form-table">
                    <tr>
                        <th><label for="code">Code:</label></th>
                        <td><input type="text" name="code" id="code" required></td>
                    </tr>
                    <tr>
                        <th><label for="value">Value:</label></th>
                        <td><input type="number" name="value" id="value" step="0.01" required></td>
                    </tr>
                    <tr>
                        <th><label for="discount_type">Discount Type:</label></th>
                        <td>
                            <select name="discount_type" id="discount_type" required>
                                <option value="percentage">Percentage</option>
                                <option value="flat_amount" selected>Flat Amount</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="date_expiry">Expiry date:</label></th>
                        <td><input type="date" name="date_expiry" id="date_expiry" required></td>
                    </tr>
                    <tr>
                        <th><label for="description">Description:</label></th>
                        <td><textarea name="description" id="description"></textarea></td>
                    </tr>
                    <tr>
                        <th><label for="product_ids">Applicable Products:</label></th>
                        <td>
                            <select name="product_ids[]" id="product_ids" multiple style="width: 100%; max-width: 25em;" required>
                                <?php foreach ($products as $product): ?>
                                    <option value="<?php echo esc_attr($product->id); ?>"><?php echo esc_html($product->title); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description">Hold Ctrl (Windows) or Cmd (Mac) to select multiple products.</p>
                        </td>
                    </tr>
                </table>
                <input type="submit" value="Add Code" class="button button-primary">
            </form>
        </div>
        <?php
        $this->enqueue_select2();
    }

    private function show_edit_page() {
        if (isset($_GET['id'])) {
            $code_id = intval($_GET['id']);
            $code_data = $this->get_promotional_code($code_id);
            $linked_product_ids = $this->get_linked_product_ids($code_id);

            if ($code_data) {
                if (isset($_POST['action']) && $_POST['action'] === 'update') {
                    $code = sanitize_text_field($_POST['code']);
                    $value = sanitize_text_field($_POST['value']);
                    $discount_type = sanitize_text_field($_POST['discount_type']);
                    $date_expiry = sanitize_text_field($_POST['date_expiry']);
                    $description = sanitize_textarea_field($_POST['description']);
                    $product_ids = isset($_POST['product_ids']) ? array_map('intval', $_POST['product_ids']) : array();

                    $this->update_promotional_code($code_id, $code, $value, $discount_type, $date_expiry, $description);
                    $this->update_linked_products($code_id, $product_ids);
                    wp_redirect(admin_url('admin.php?page=prices-and-promos'));
                    exit;
                }
                
                $products = $this->get_all_products();
                ?>
                <div class="wrap">
                    <h1>Edit Promotional Code</h1>
                    <form method="post" id="edit-code-form">
                        <input type="hidden" name="action" value="update">
                        <table class="form-table">
                            <tr>
                                <th><label for="code">Code:</label></th>
                                <td><input type="text" name="code" id="code" value="<?php echo esc_attr($code_data->code); ?>" required></td>
                            </tr>
                            <tr>
                                <th><label for="value">Value:</label></th>
                                <td><input type="number" name="value" id="value" value="<?php echo esc_attr($code_data->value); ?>" step="0.01" required></td>
                            </tr>
                            <tr>
                                <th><label for="discount_type">Discount Type:</label></th>
                                <td>
                                    <select name="discount_type" id="discount_type" required>
                                        <option value="percentage" <?php selected($code_data->discount_type, 'percentage'); ?>>Percentage</option>
                                        <option value="flat_amount" <?php selected($code_data->discount_type, 'flat_amount'); ?>>Flat Amount</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th><label for="date_expiry">Expiry date:</label></th>
                                <td><input type="date" name="date_expiry" id="date_expiry" value="<?php echo esc_attr($code_data->date_expiry); ?>" required></td>
                            </tr>
                            <tr>
                                <th><label for="description">Description:</label></th>
                                <td><textarea name="description" id="description"><?php echo esc_textarea($code_data->description); ?></textarea></td>
                            </tr>
                            <tr>
                                <th><label for="product_ids">Applicable Products:</label></th>
                                <td>
                                    <select name="product_ids[]" id="product_ids" multiple style="width: 100%; max-width: 25em;" required>
                                        <?php foreach ($products as $product): ?>
                                            <option value="<?php echo esc_attr($product->id); ?>" <?php selected(in_array($product->id, $linked_product_ids)); ?>><?php echo esc_html($product->title); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <p class="description">Hold Ctrl (Windows) or Cmd (Mac) to select multiple products.</p>
                                </td>
                            </tr>
                        </table>
                        <input type="submit" value="Update Code" class="button button-primary">
                    </form>
                </div>
                <?php
                $this->enqueue_select2();
            } else {
                echo '<div class="wrap"><h1>Invalid Promotional Code ID</h1></div>';
            }
        } else {
            echo '<div class="wrap"><h1>No Promotional Code ID provided</h1></div>';
        }
    }

    private function do_delete() {
        if (isset($_GET['id'])) {
            $code_id = intval($_GET['id']);
            $this->delete_promotional_code($code_id);
            wp_redirect(admin_url('admin.php?page=prices-and-promos'));
            exit;
        }
    }
    

    private function handle_form_submissions() {
        if (isset($_POST['action'])) {
            $code = sanitize_text_field($_POST['code']);
            $value = sanitize_text_field($_POST['value']);
            $discount_type = sanitize_text_field($_POST['discount_type']);
            $date_expiry = sanitize_text_field($_POST['date_expiry']);
            $description = sanitize_textarea_field($_POST['description']);

            switch ($_POST['action']) {
                case 'create':
                    $this->create_promotional_code($code, $value, $discount_type, $date_expiry, $description);
                    break;
                case 'update':
                    $this->update_promotional_code($_POST['id'], $code, $value, $discount_type, $date_expiry, $description);
                    break;
            }
        }
    }

    private function get_promotional_codes() {
        global $wpdb;
        return $wpdb->get_results("SELECT * FROM $this->table_name");
    }

    private function get_promotional_code($id) {
        global $wpdb;
        return $wpdb->get_row("SELECT * FROM $this->table_name WHERE id = $id");
    }
    
    private function get_all_products() {
        global $wpdb;
        return $wpdb->get_results("SELECT id, title FROM $this->products_table_name");
    }

    private function render_promotional_codes($codes) {
        foreach ($codes as $code) {
            $edit_link = admin_url('admin.php?page=prices-and-promos&action=edit&id=' . $code->id);
            $delete_link = admin_url('admin.php?page=prices-and-promos&action=delete&id=' . $code->id);
            $linked_products = $this->get_linked_products($code->id);
            ?>
            <tr>
                <td><?php echo esc_html($code->code); ?></td>
                <td><?php echo esc_html($code->value); ?></td>
                <td><?php echo esc_html($code->discount_type); ?></td>
                <td><?php echo esc_html($code->description); ?></td>
                <td><?php echo esc_html($code->date_expiry); ?></td>
                <td><?php echo esc_html($code->date_add); ?></td>
                <td><?php echo esc_html($code->date_upd); ?></td>
                <td>
                    <?php
                    if (!empty($linked_products)) {
                        echo '<ul>';
                        foreach ($linked_products as $product) {
                            echo '<li>' . esc_html($product->title) . '</li>';
                        }
                        echo '</ul>';
                    } else {
                        echo 'No linked products';
                    }
                    ?>
                </td>
                <td>
                    <a class="page-title-action" href="<?php echo $edit_link; ?>">Edit</a>
                    <a class="page-title-action" href="<?php echo $delete_link; ?>" onclick="return confirm('Are you sure you want to delete this promotional code?');">Delete</a>
                </td>
            </tr>
            <?php
        }
    }

    private function create_promotional_code($code, $value, $discount_type, $date_expiry, $description) {
        global $wpdb;
        $wpdb->insert(
            $this->table_name,
            array(
                'code' => $code,
                'value' => $value,
                'discount_type' => $discount_type,
                'description' => $description,
                'date_expiry' => $date_expiry,
                'date_add' => current_time('mysql'),
                'date_upd' => current_time('mysql'),
            )
        );
        return $wpdb->insert_id;
    }

    private function update_promotional_code($id, $code, $value, $discount_type, $date_expiry, $description) {
        global $wpdb;
        $wpdb->update(
            $this->table_name,
            array(
                'code' => $code,
                'value' => $value,
                'discount_type' => $discount_type,
                'description' => $description,
                'date_expiry' => $date_expiry,
                'date_upd' => current_time('mysql'),
            ),
            array('id' => intval($id))
        );
    }

    private function delete_promotional_code($id) {
        global $wpdb;
        $wpdb->delete($this->table_name, array('id' => intval($id)));
        $wpdb->delete($this->promo_products_table_name, array('promo_code_id' => intval($id)));
    }
    
    private function link_products_to_promo($promo_id, $product_ids) {
        global $wpdb;
        foreach ($product_ids as $product_id) {
            $wpdb->insert(
                $this->promo_products_table_name,
                array(
                    'promo_code_id' => $promo_id,
                    'product_id' => $product_id,
                )
            );
        }
    }

    private function update_linked_products($promo_id, $product_ids) {
        global $wpdb;
        $wpdb->delete($this->promo_products_table_name, array('promo_code_id' => $promo_id));
        $this->link_products_to_promo($promo_id, $product_ids);
    }

    private function get_linked_product_ids($promo_id) {
        global $wpdb;
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT product_id FROM $this->promo_products_table_name WHERE promo_code_id = %d",
            $promo_id
        ));
        return wp_list_pluck($results, 'product_id');
    }

    private function get_linked_products($promo_id) {
        global $wpdb;
        return $wpdb->get_results($wpdb->prepare(
            "SELECT p.id, p.title 
            FROM $this->products_table_name p 
            JOIN $this->promo_products_table_name pp ON p.id = pp.product_id 
            WHERE pp.promo_code_id = %d",
            $promo_id
        ));
    }

    private function enqueue_select2() {
        wp_enqueue_style('select2', 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css');
        wp_enqueue_script('select2', 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js', array('jquery'), null, true);
        ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                $('#product_ids').select2({
                    placeholder: 'Select products',
                    width: '100%'
                });
            });
        </script>
        <?php
    }
}