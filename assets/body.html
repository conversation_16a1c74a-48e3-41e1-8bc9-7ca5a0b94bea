<style>
  .owl-carousel,
  .owl-carousel .owl-item {
    -webkit-tap-highlight-color: transparent;
    position: relative;
  }

  .owl-carousel {
    display: none;
    width: 100%;
    z-index: 1;
  }

  .owl-carousel .owl-stage {
    position: relative;
    touch-action: manipulation;
    -moz-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .owl-carousel .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0;
  }

  .owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  .owl-carousel .owl-item,
  .owl-carousel .owl-wrapper {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  .owl-carousel .owl-item {
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-touch-callout: none;
    backface-visibility: hidden;
  }

  .owl-carousel .owl-item img {
    display: block;
    width: 100%;
  }

  .owl-carousel .owl-dots.disabled,
  .owl-carousel .owl-nav.disabled {
    display: none;
  }

  .no-js .owl-carousel,
  .owl-carousel.owl-loaded {
    display: block;
  }

  .owl-carousel .owl-dot,
  .owl-carousel .owl-nav .owl-next,
  .owl-carousel .owl-nav .owl-prev {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .owl-carousel .owl-nav button.owl-next,
  .owl-carousel .owl-nav button.owl-prev,
  .owl-carousel button.owl-dot {
    background: 0 0;
    color: inherit;
    border: none;
    padding: 0 !important;
    font: inherit;
  }

  .owl-carousel.owl-loading {
    opacity: 0;
    display: block;
  }

  .owl-carousel.owl-hidden {
    opacity: 0;
  }

  .owl-carousel.owl-refresh .owl-item {
    visibility: hidden;
  }

  .owl-carousel.owl-drag .owl-item {
    touch-action: pan-y;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .owl-carousel.owl-grab {
    cursor: move;
    cursor: -webkit-grab;
    cursor: grab;
  }

  .owl-carousel.owl-rtl {
    direction: rtl;
  }

  .owl-carousel.owl-rtl .owl-item {
    float: right;
  }

  .owl-carousel .animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
  }

  .owl-carousel .owl-animated-in {
    z-index: 0;
  }

  .owl-carousel .owl-animated-out {
    z-index: 1;
  }

  .owl-carousel .fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut;
  }

  @-webkit-keyframes fadeOut {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @keyframes fadeOut {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  .owl-height {
    transition: height 0.5s ease-in-out;
  }

  .owl-carousel .owl-item .owl-lazy {
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  .owl-carousel .owl-item .owl-lazy:not([src]),
  .owl-carousel .owl-item .owl-lazy[src^=""] {
    max-height: 0;
  }

  .owl-carousel .owl-item img.owl-lazy {
    transform-style: preserve-3d;
  }

  .owl-carousel .owl-video-wrapper {
    position: relative;
    height: 100%;
    background: #000;
  }

  .owl-carousel .owl-video-play-icon {
    position: absolute;
    height: 80px;
    width: 80px;
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    background: url(owl.video.play.png) no-repeat;
    cursor: pointer;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    transition: transform 0.1s ease;
    backface-visibility: hidden;
  }

  .owl-carousel .owl-video-play-icon:hover {
    transform: scale(1.3, 1.3);
  }

  .owl-carousel .owl-video-playing .owl-video-play-icon,
  .owl-carousel .owl-video-playing .owl-video-tn {
    display: none;
  }

  .owl-carousel .owl-video-tn {
    opacity: 0;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    transition: opacity 0.4s ease;
  }

  .owl-carousel .owl-video-frame {
    position: relative;
    z-index: 1;
    height: 100%;
    width: 100%;
  }

  html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    overflow-y: visible;
    overflow-x: hidden;
  }

  body {
    margin: 0;
  }

  h1 {
    font-size: 2rem;
    margin: 0.67em 0;
  }

  hr {
    height: 0;
    overflow: visible;
  }

  pre {
    font-family: monospace, monospace;
    font-size: 1rem;
  }

  a {
    background-color: transparent;
  }

  abbr[title] {
    border-bottom: none;
    text-decoration: underline;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  b,
  strong {
    font-weight: bolder;
  }

  code,
  kbd,
  samp {
    font-family: monospace, monospace;
    font-size: 1rem;
  }

  small {
    font-size: 80%;
  }

  sub,
  sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  sub {
    bottom: -0.25rem;
  }

  sup {
    top: -0.5rem;
  }

  img {
    border-style: none;
  }

  button,
  input,
  optgroup,
  select,
  textarea {
    font-family: inherit;
    font-size: 100%;
    line-height: 1.15;
    margin: 0;
  }

  button,
  input {
    overflow: visible;
  }

  button,
  select {
    text-transform: none;
  }

  [type="button"],
  [type="reset"],
  [type="submit"],
  button {
    -webkit-appearance: button;
    font-size: 8px;
  }

  [type="button"]::-moz-focus-inner,
  [type="reset"]::-moz-focus-inner,
  [type="submit"]::-moz-focus-inner,
  button::-moz-focus-inner {
    border-style: none;
    padding: 0;
  }

  [type="button"]:-moz-focusring,
  [type="reset"]:-moz-focusring,
  [type="submit"]:-moz-focusring,
  button:-moz-focusring {
    outline: 1px dotted ButtonText;
  }

  fieldset {
    padding: 0.35rem 0.75rem 0.625rem;
  }

  legend {
    color: inherit;
    display: table;
    max-width: 100%;
    padding: 0;
    white-space: normal;
  }

  progress {
    vertical-align: baseline;
  }

  textarea {
    overflow: auto;
  }

  [type="checkbox"],
  [type="radio"] {
    padding: 0;
  }

  [type="number"]::-webkit-inner-spin-button,
  [type="number"]::-webkit-outer-spin-button {
    height: auto;
  }

  [type="search"] {
    -webkit-appearance: textfield;
    outline-offset: -2px;
  }

  [type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit;
  }

  details {
    display: block;
  }

  summary {
    display: list-item;
  }

  template {
    display: none;
  }

  [hidden] {
    display: none;
  }

  *,
  :after,
  :before {
    box-sizing: border-box;
  }

  body {
    background: #fff;
  }

  hr {
    background-color: #828282;
    border: 0;
    height: 1px;
    margin-bottom: 1.5rem;
  }

  .screen-reader-text {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    -webkit-clip-path: inset(50%);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute !important;
    width: 1px;
    word-wrap: normal !important;
  }

  .screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    box-shadow: 0 0 2px 2px #000;
    clip: auto !important;
    -webkit-clip-path: none;
    clip-path: none;
    color: #000;
    display: block;
    font-size: 14px;
    font-size: 0.875rem;
    font-weight: 600;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000;
  }

  #content[tabindex="-1"]:focus {
    outline: 0;
  }

  body {
    color: #000;
    font-family: MessinaSansWeb, sans-serif;
    font-size: 16px;
    font-size: 1rem;
    line-height: 1.45;
  }

  .h1,
  .h2,
  .h3,
  .h4,
  .h5,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    clear: both;
    color: #111;
    font-weight: 600;
    margin: 1rem 0;
    font-weight: 400;
  }

  .h1,
  h1 {
    font-size: 1.4rem;
    line-height: 1.25;
    font-family: MessinaSansWeb, sans-serif;
    font-weight: 400;
    margin: 0 0 1rem 0;
  }

  .h2,
  h2 {
    font-size: 3.75rem;
    line-height: 1.25;
    font-family: ClassicoURW-Reg, sans-serif;
    font-weight: 600;
  }

  @media only screen and (max-width: 1025px) {

    .h2,
    h2 {
      font-size: 2.5rem;
    }
  }

  .h3,
  h3 {
    font-size: 2.875rem;
    line-height: 1.25;
    font-family: ClassicoURW-Reg, sans-serif;
    font-weight: 600;
  }

  @media only screen and (max-width: 1025px) {

    .h3,
    h3 {
      font-size: 2.2rem;
      line-height: 1.25;
    }
  }

  .h4,
  h4 {
    font-size: 1.75rem;
    line-height: 1.45;
    font-family: ClassicoURW-Reg, sans-serif;
    font-weight: 600;
  }

  .h5,
  h5 {
    font-size: 1.5rem;
    line-height: 1.75;
    font-family: MessinaSansWeb, sans-serif;
    font-weight: 400;
  }

  .h6,
  h6 {
    font-size: 1.4rem;
    line-height: 1.75;
    font-family: MessinaSansWeb, sans-serif;
    font-weight: 400;
  }

  .h7 {
    font-size: 1.2rem;
    line-height: 1.75;
    font-family: MessinaSansWeb, sans-serif;
    font-weight: 600;
  }

  .wp-block-buttons,
  p {
    margin: 0 0 1.25rem 0;
    line-height: 1.45;
  }

  cite,
  dfn,
  em,
  i {
    font-style: italic;
  }

  blockquote,
  blockquote.wp-block-quote {
    border-left: none;
    padding-top: 2rem;
    padding-bottom: 2rem;
    font-size: 1.5rem;
    text-align: center;
    font-style: normal;
  }

  blockquote p,
  blockquote.wp-block-quote p {
    margin: 0 auto 1.25rem auto;
    max-width: 520px;
    font-style: normal;
  }

  blockquote cite,
  blockquote.wp-block-quote cite {
    font-style: normal;
    color: #4f4f4f;
  }

  blockquote.is-style-large p,
  blockquote.wp-block-quote.is-style-large p {
    font-style: normal;
    margin: 2rem auto 2rem auto;
  }

  address {
    margin: 0 0 1.5rem;
  }

  pre {
    background: #eee;
    font-family: "Courier 10 Pitch", Courier, monospace;
    font-size: 15px;
    font-size: 0.9375rem;
    line-height: 1.6;
    margin-bottom: 1.6rem;
    max-width: 100%;
    overflow: auto;
    padding: 1.6rem;
  }

  code,
  kbd,
  tt,
  var {
    font-family: Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
    font-size: 15px;
    font-size: 0.9375rem;
  }

  abbr,
  acronym {
    border-bottom: 1px dotted #666;
    cursor: help;
  }

  ins,
  mark {
    background: #fff9c0;
    text-decoration: none;
  }

  big {
    font-size: 125%;
  }

  ol,
  ul {
    margin: 0 0 1.25rem 0;
  }

  ul {
    list-style: disc;
  }

  ol {
    list-style: decimal;
  }

  li>ol,
  li>ul {
    margin-bottom: 0;
    margin-left: 0rem;
  }

  dt {
    font-weight: 700;
  }

  dd {
    margin: 0 1.5rem 1.5rem;
  }

  img {
    height: auto;
    max-width: 100%;
  }

  figure {
    margin: 0;
  }

  table {
    margin: 0 0 1.5rem;
    width: 100%;
  }

  td,
  th {
    text-align: left;
  }

  a {
    color: #000;
    text-decoration: underline;
  }

  a:active,
  a:focus,
  a:hover {
    color: #0f2b46;
    outline: 0;
  }

  a:focus {
    outline: thin dotted;
  }

  .wp-caption {
    margin-bottom: 1.5rem;
    max-width: 100%;
  }

  .wp-caption .wp-caption-text {
    margin: 0.375rem 0;
  }

  .wp-caption img[class*="wp-image-"] {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }

  .wp-caption-text {
    text-align: center;
    font-style: italic;
  }

  ol.has-large-font-size,
  p.has-large-font-size,
  ul.has-large-font-size {
    font-size: 1.5rem;
  }

  ol.is_smaller,
  p.is_smaller ul.is_smaller {
    font-size: 0.9rem;
  }

  .is_one-line {
    white-space: nowrap;
  }

  .entry-content ul {
    list-style: none;
    padding: 0;
  }

  .entry-content ul li {
    position: relative;
    padding-left: 2rem;
    line-height: 1.45;
    margin: 1rem 0;
  }

  .entry-content ul li:after {
    content: "";
    background-color: #f5eae8;
    width: 10px;
    height: 10px;
    position: absolute;
    left: 2px;
    top: 8px;
    border-radius: 5px;
  }

  .entry-content ol li {
    padding-left: 1rem;
    margin: 0.5rem 0;
    margin-left: 17px;
  }

  .entry-content a:hover img {
    opacity: 0.6;
  }

  .container {
    position: relative;
    margin: 0 auto;
    padding: 0 10.9375rem;
    max-width: 1440px;
  }

  @media only screen and (max-width: 1440px) {
    .container {
      padding: 0 2rem;
      max-width: 1281px;
    }
  }

  @media only screen and (max-width: 1281px) {
    .container {
      padding: 0 2rem;
      max-width: 72.5rem;
    }
  }

  @media only screen and (max-width: 480px) {
    .container {
      padding: 0 1rem;
    }
  }

  .alignleft {
    text-align: left;
  }

  .alignright {
    text-align: right;
  }

  .aligncenter {
    text-align: center;
  }

  .site-main .comment-navigation,
  .site-main .post-navigation,
  .site-main .posts-navigation {
    margin: 0 0 1.5rem;
    overflow: hidden;
  }

  .comment-navigation .nav-previous,
  .post-navigation .nav-previous,
  .posts-navigation .nav-previous {
    width: 50%;
    float: left;
  }

  .comment-navigation .nav-next,
  .post-navigation .nav-next,
  .posts-navigation .nav-next {
    width: 50%;
    text-align: right;
    float: right;
  }

  @media only screen and (max-width: 1281px) {
    .no-scroll {
      overflow: hidden;
    }
  }

  @media only screen and (max-width: 768px) {
    .wp-block-media-text {
      align-items: top;
    }
  }

  .wp-block-media-text .wp-block-media-text__content {
    padding-left: 15%;
    padding-right: 0;
  }

  @media only screen and (max-width: 1281px) {
    .wp-block-media-text .wp-block-media-text__content {
      padding-left: 0;
    }
  }

  @media only screen and (max-width: 768px) {
    .wp-block-media-text .wp-block-media-text__content {
      padding-left: 2rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .wp-block-media-text .wp-block-media-text__content {
      padding-left: 0rem;
    }
  }

  .wp-block-media-text .wp-block-media-text__content> :first-child {
    margin-top: 0;
  }

  .wp-block-media-text .wp-block-media-text__media {
    padding-right: 10%;
  }

  @media only screen and (max-width: 768px) {
    .wp-block-media-text .wp-block-media-text__media {
      padding-right: 0;
      padding-bottom: 2rem;
      align-self: flex-start;
    }
  }

  @media only screen and (max-width: 600px) {
    .wp-block-media-text .wp-block-media-text__media {
      padding-bottom: 2rem;
    }
  }

  .wp-block-media-text .wp-block-media-text__media img {
    max-height: 900px;
  }

  .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {
    padding-left: 0;
    padding-right: 15%;
  }

  @media only screen and (max-width: 1025px) {
    .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {
      padding-right: 0;
    }
  }

  .wp-block-media-text.has-media-on-the-right .wp-block-media-text__media {
    padding-left: 10%;
    padding-right: 0;
  }

  @media only screen and (max-width: 768px) {
    .wp-block-media-text.has-media-on-the-right .wp-block-media-text__media {
      padding-left: 0;
    }
  }

  .wp-block-image.size-large {
    margin: 0;
  }

  .wp-block-image.size-large img {
    width: 100%;
  }

  .wp-block-image.size-large figcaption {
    margin: 1rem 0;
  }

  @media only screen and (max-width: 768px) {
    .entry-content>.wp-block-image.size-large {
      margin: 1.875rem 0;
    }
  }

  .entry-content>.wp-block-image.size-large {
    margin: 3.75rem 0;
    margin-left: auto;
    margin-right: auto;
    max-width: 1281px;
  }

  @media only screen and (max-width: 768px) {
    .entry-content>.wp-block-image.size-large {
      margin: 1.875rem auto;
    }
  }

  .entry-content>.wp-block-image.size-large img {
    width: 100%;
  }

  .entry-content>.wp-block-image.size-large figcaption {
    position: relative;
    margin: 1rem auto;
    padding: 0 1rem;
  }

  .entry-content>.wp-block-image.size-full {
    margin: 3.75rem 0;
  }

  @media only screen and (max-width: 768px) {
    .entry-content>.wp-block-image.size-full {
      margin: 1.875rem auto;
    }
  }

  .entry-content>.wp-block-image.size-full img {
    width: 100%;
  }

  .entry-content>.wp-block-image.size-full figcaption {
    position: relative;
    margin: 1rem auto;
    padding: 0 1rem;
  }

  .wp-block-button.is-style-read-more .wp-block-button__link {
    position: relative;
    display: flex;
    padding-left: 0;
    padding-right: 24px;
    border: none;
    text-transform: uppercase;
    color: #000;
    background: url(../../svg/circle.svg) right center no-repeat;
    background-color: #fff;
    background-size: 16px 16px;
    letter-spacing: 0.1em;
  }

  .wp-block-button.is-style-read-more .wp-block-button__link:before {
    content: "";
    position: absolute;
    bottom: 0.8rem;
    left: 0;
    width: 0;
    height: 1px;
    margin-right: 1.2rem;
    background: #000;
    transition: all 0.2s ease;
  }

  .wp-block-button.is-style-read-more .wp-block-button__link:after {
    content: "";
    position: absolute;
    display: block;
    top: 19px;
    right: 7px;
    width: 5px;
    height: 5px;
    border-right: 1.5px solid;
    border-bottom: 1.5px solid;
    transition: all 0.2s ease;
    transform: rotate(-45deg);
    border-color: #fff;
    transition: all 0.2s ease;
  }

  .wp-block-button.is-style-read-more .wp-block-button__link:hover:before {
    width: calc(100% - 24px);
  }

  .entry-content>.wp-block-columns,
  .entry-content>.wp-block-media-text {
    margin: 0rem auto;
    padding: 3.75rem 10.9375rem;
    max-width: 1440px;
  }

  @media only screen and (max-width: 1440px) {

    .entry-content>.wp-block-columns,
    .entry-content>.wp-block-media-text {
      padding: 2rem;
    }
  }

  @media only screen and (max-width: 768px) {

    .entry-content>.wp-block-columns,
    .entry-content>.wp-block-media-text {
      margin: 0 auto 1.875rem auto;
    }
  }

  @media only screen and (max-width: 600px) {

    .entry-content>.wp-block-columns,
    .entry-content>.wp-block-media-text {
      padding: 1rem;
    }
  }

  .entry-content>.wp-block-cover,
  .entry-content>.wp-block-gallery,
  .entry-content>.wp-block-quote,
  .entry-content>.wp-block-table,
  .entry-content>h1,
  .entry-content>h2,
  .entry-content>h3,
  .entry-content>h4,
  .entry-content>h5,
  .entry-content>h6,
  .entry-content>ol,
  .entry-content>p,
  .entry-content>ul {
    margin-left: auto;
    margin-right: auto;
    padding-left: 10.9375rem;
    padding-right: 10.9375rem;
    max-width: 1440px;
  }

  @media only screen and (max-width: 1281px) {

    .entry-content>.wp-block-cover,
    .entry-content>.wp-block-gallery,
    .entry-content>.wp-block-quote,
    .entry-content>.wp-block-table,
    .entry-content>h1,
    .entry-content>h2,
    .entry-content>h3,
    .entry-content>h4,
    .entry-content>h5,
    .entry-content>h6,
    .entry-content>ol,
    .entry-content>p,
    .entry-content>ul {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 600px) {

    .entry-content>.wp-block-cover,
    .entry-content>.wp-block-gallery,
    .entry-content>.wp-block-quote,
    .entry-content>.wp-block-table,
    .entry-content>h1,
    .entry-content>h2,
    .entry-content>h3,
    .entry-content>h4,
    .entry-content>h5,
    .entry-content>h6,
    .entry-content>ol,
    .entry-content>p,
    .entry-content>ul {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  .entry-content>.wp-block-cover:last-child,
  .entry-content>.wp-block-gallery:last-child,
  .entry-content>.wp-block-quote:last-child,
  .entry-content>.wp-block-table:last-child,
  .entry-content>h1:last-child,
  .entry-content>h2:last-child,
  .entry-content>h3:last-child,
  .entry-content>h4:last-child,
  .entry-content>h5:last-child,
  .entry-content>h6:last-child,
  .entry-content>ol:last-child,
  .entry-content>p:last-child,
  .entry-content>ul:last-child {
    padding-bottom: 3.75rem;
  }

  .entry-content>.wp-block-separator {
    margin-top: 3.75rem;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 3.75rem;
    max-width: 768px;
  }

  @media only screen and (max-width: 768px) {
    .entry-content>.wp-block-separator {
      margin: 1.875rem auto;
    }
  }

  .wp-block-gallery {
    margin-top: 3.75rem;
    margin-bottom: 3.75rem;
  }

  @media only screen and (max-width: 768px) {
    .wp-block-gallery {
      margin: 1.875rem auto;
    }
  }

  .wp-block-cover {
    margin-top: 3.75rem;
    margin-bottom: 3.75rem;
    mix-blend-mode: multiply;
  }

  @media only screen and (max-width: 768px) {
    .wp-block-cover {
      margin: 1.875rem auto;
    }
  }

  .wp-block-cover.has-yellow-background-color:before {
    background-color: rgba(247, 167, 111, 0.6);
  }

  .wp-block-cover.has-blue-background-color:before {
    background-color: rgba(174, 216, 237, 0.6);
  }

  .wp-block-cover.has-green-background-color:before {
    background-color: rgba(147, 198, 147, 0.6);
  }

  .wp-block-cover.has-pink-background-color:before {
    background-color: rgba(255, 217, 234, 0.6);
  }

  .wp-block-cover.has-sand-background-color:before {
    background-color: rgba(245, 234, 232, 0.6);
  }

  .wp-block-cover.has-petrol-background-color:before {
    background-color: rgba(29, 110, 135, 0.6);
  }

  .wp-block-cover.has-white-background-color:before {
    background-color: rgba(255, 255, 255, 0.6);
  }

  .wp-block-cover.has-black-background-color:before {
    background-color: rgba(0, 0, 0, 0.6);
  }

  .entry-content>p {
    padding-right: 23.4375rem;
  }

  @media only screen and (max-width: 1281px) {
    .entry-content>p {
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .entry-content>p {
      padding-right: 1rem;
    }
  }

  .entry-content>.wp-block-table {
    margin-top: 1.875rem;
    margin-bottom: 1.875rem;
  }

  .entry-content>.wp-block-table table {
    border-collapse: collapse;
  }

  .entry-content>.wp-block-table table td,
  .entry-content>.wp-block-table table th {
    padding: 1rem 1rem 1rem 0;
  }

  .entry-content>.wp-block-table.is-style-stripes {
    border: none;
  }

  .entry-content>.wp-block-table.is-style-stripes td,
  .entry-content>.wp-block-table.is-style-stripes th {
    padding-left: 1rem;
  }

  .wp-block-columns {
    align-items: flex-start;
  }

  .wp-block-columns h3 a,
  .wp-block-columns h4 a,
  .wp-block-columns h5 a {
    text-decoration: none;
  }

  .wp-block-columns h3 a:hover,
  .wp-block-columns h4 a:hover,
  .wp-block-columns h5 a:hover {
    color: #000;
    text-decoration: underline;
  }

  @media only screen and (max-width: 768px) {
    .wp-block-column:not(:only-child) {
      margin-left: 0;
      flex-basis: 100% !important;
    }
  }

  @media only screen and (min-width: 768px) {
    .wp-block-column:nth-child(2):last-child {
      padding-left: 0;
    }
  }

  @media only screen and (min-width: 1025px) {
    .wp-block-column:nth-child(2):last-child {
      padding-left: 15%;
    }
  }

  .button,
  .wp-block-button__link,
  a.button,
  button,
  input[type="button"],
  input[type="reset"],
  input[type="submit"] {
    display: inline-block;
    padding: 0.9rem 1.1rem;
    border: solid 2px #3d6e84 !important;
    border-radius: 2px;
    font-size: 0.8rem;
    line-height: 1.25;
    font-weight: 600;
    text-align: center;
    text-transform: uppercase;
    color: #fff;
    background-color: #3d6e84 !important;
    text-decoration: none;
    cursor: pointer;
    letter-spacing: 0.25em;
  }

  .button:active,
  .button:focus,
  .button:hover,
  .wp-block-button__link:active,
  .wp-block-button__link:focus,
  .wp-block-button__link:hover,
  a.button:active,
  a.button:focus,
  a.button:hover,
  button:active,
  button:focus,
  button:hover,
  input[type="button"]:active,
  input[type="button"]:focus,
  input[type="button"]:hover,
  input[type="reset"]:active,
  input[type="reset"]:focus,
  input[type="reset"]:hover,
  input[type="submit"]:active,
  input[type="submit"]:focus,
  input[type="submit"]:hover {
    border: solid 2px #50575e;
    color: #fff;
    background-color: #50575e;
    outline: 0;
  }

  .button--light,
  .button--light:visited,
  .is-style-button--light .button,
  .is-style-button--light .wp-block-button__link,
  .is-style-button--light a.button,
  .is-style-button--light button,
  .is-style-button--light:visited .button,
  .is-style-button--light:visited .wp-block-button__link,
  .is-style-button--light:visited a.button,
  .is-style-button--light:visited button,
  .is-style-button-external .button,
  .is-style-button-external .wp-block-button__link,
  .is-style-button-external a.button,
  .is-style-button-external button,
  .is-style-button-external:visited .button,
  .is-style-button-external:visited .wp-block-button__link,
  .is-style-button-external:visited a.button,
  .is-style-button-external:visited button,
  .wp-block-button__link--light,
  .wp-block-button__link--light:visited,
  a.button--light,
  a.button--light:visited,
  button--light,
  button--light:visited {
    color: #000;
    background-color: transparent;
  }

  .button--light:active,
  .button--light:focus,
  .button--light:hover,
  .button--light:visited:active,
  .button--light:visited:focus,
  .button--light:visited:hover,
  .is-style-button--light .button:active,
  .is-style-button--light .button:focus,
  .is-style-button--light .button:hover,
  .is-style-button--light .wp-block-button__link:active,
  .is-style-button--light .wp-block-button__link:focus,
  .is-style-button--light .wp-block-button__link:hover,
  .is-style-button--light a.button:active,
  .is-style-button--light a.button:focus,
  .is-style-button--light a.button:hover,
  .is-style-button--light button:active,
  .is-style-button--light button:focus,
  .is-style-button--light button:hover,
  .is-style-button--light:visited .button:active,
  .is-style-button--light:visited .button:focus,
  .is-style-button--light:visited .button:hover,
  .is-style-button--light:visited .wp-block-button__link:active,
  .is-style-button--light:visited .wp-block-button__link:focus,
  .is-style-button--light:visited .wp-block-button__link:hover,
  .is-style-button--light:visited a.button:active,
  .is-style-button--light:visited a.button:focus,
  .is-style-button--light:visited a.button:hover,
  .is-style-button--light:visited button:active,
  .is-style-button--light:visited button:focus,
  .is-style-button--light:visited button:hover,
  .is-style-button-external .button:active,
  .is-style-button-external .button:focus,
  .is-style-button-external .button:hover,
  .is-style-button-external .wp-block-button__link:active,
  .is-style-button-external .wp-block-button__link:focus,
  .is-style-button-external .wp-block-button__link:hover,
  .is-style-button-external a.button:active,
  .is-style-button-external a.button:focus,
  .is-style-button-external a.button:hover,
  .is-style-button-external button:active,
  .is-style-button-external button:focus,
  .is-style-button-external button:hover,
  .is-style-button-external:visited .button:active,
  .is-style-button-external:visited .button:focus,
  .is-style-button-external:visited .button:hover,
  .is-style-button-external:visited .wp-block-button__link:active,
  .is-style-button-external:visited .wp-block-button__link:focus,
  .is-style-button-external:visited .wp-block-button__link:hover,
  .is-style-button-external:visited a.button:active,
  .is-style-button-external:visited a.button:focus,
  .is-style-button-external:visited a.button:hover,
  .is-style-button-external:visited button:active,
  .is-style-button-external:visited button:focus,
  .is-style-button-external:visited button:hover,
  .wp-block-button__link--light:active,
  .wp-block-button__link--light:focus,
  .wp-block-button__link--light:hover,
  .wp-block-button__link--light:visited:active,
  .wp-block-button__link--light:visited:focus,
  .wp-block-button__link--light:visited:hover,
  a.button--light:active,
  a.button--light:focus,
  a.button--light:hover,
  a.button--light:visited:active,
  a.button--light:visited:focus,
  a.button--light:visited:hover,
  button--light:active,
  button--light:focus,
  button--light:hover,
  button--light:visited:active,
  button--light:visited:focus,
  button--light:visited:hover {
    border: solid 2px #3d6e84 !important;
    color: #fff;
    background-color: #3d6e84 !important;
  }

  .button--read-more,
  .wp-block-button__link--read-more,
  a.button--read-more,
  button--read-more {
    position: relative;
    padding: 0.8rem 1.1rem;
    padding-left: 0;
    padding-right: 24px;
    border: none;
    text-transform: uppercase;
    color: #3d6e84 !important;
    background: url(../../svg/circle.svg) right center no-repeat;
    background-color: #fff;
    background-size: 16px 16px;
    letter-spacing: 0.1em;
  }

  .button--read-more:before,
  .wp-block-button__link--read-more:before,
  a.button--read-more:before,
  button--read-more:before {
    content: "";
    position: absolute;
    bottom: 0.8rem;
    left: 0;
    width: 0;
    height: 1px;
    margin-right: 1.2rem;
    background: #3d6e84 !important;
    transition: all 0.2s ease;
  }

  .button--read-more:after,
  .wp-block-button__link--read-more:after,
  a.button--read-more:after,
  button--read-more:after {
    content: "";
    position: absolute;
    display: block;
    top: 18px;
    right: 7px;
    width: 5px;
    height: 5px;
    border-right: 1.5px solid;
    border-bottom: 1.5px solid;
    transition: all 0.2s ease;
    transform: rotate(-45deg);
    border-color: #fff;
    transition: all 0.2s ease;
  }

  .button--read-more:focus,
  .button--read-more:hover,
  .wp-block-button__link--read-more:focus,
  .wp-block-button__link--read-more:hover,
  a.button--read-more:focus,
  a.button--read-more:hover,
  button--read-more:focus,
  button--read-more:hover {
    border: none;
    color: #3d6e84 !important;
    background-color: transparent;
    text-decoration: none;
  }

  .button--read-more:focus:before,
  .button--read-more:hover:before,
  .wp-block-button__link--read-more:focus:before,
  .wp-block-button__link--read-more:hover:before,
  a.button--read-more:focus:before,
  a.button--read-more:hover:before,
  button--read-more:focus:before,
  button--read-more:hover:before {
    width: calc(100% - 24px);
  }

  .is-style-button-external .wp-block-button__link {
    position: relative;
    padding-right: 2.5rem;
  }

  .is-style-button-external .wp-block-button__link:after,
  .is-style-button-external .wp-block-button__link:before {
    content: "";
    position: absolute;
    top: 0.7rem;
    right: 1rem;
    width: 18px;
    height: 18px;
  }

  .is-style-button-external .wp-block-button__link:after {
    background-image: url(../../svg/arrow-square-out-bold.svg);
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
  }

  .is-style-button-external .wp-block-button__link:before {
    background-image: url(../../svg/arrow-square-out-bold-white.svg);
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
  }

  .is-style-button-external .wp-block-button__link:hover:after {
    display: none;
  }

  .is-style-button-external .wp-block-button__link:hover:before {
    display: block;
  }

  input[type="color"],
  input[type="date"],
  input[type="datetime-local"],
  input[type="datetime"],
  input[type="email"],
  input[type="month"],
  input[type="number"],
  input[type="password"],
  input[type="range"],
  input[type="search"],
  input[type="tel"],
  input[type="text"],
  input[type="time"],
  input[type="url"],
  input[type="week"],
  select,
  textarea {
    font-size: 1rem;
    line-height: 1.45;
    border: 1px solid #000;
    padding: 0.5rem 1rem;
    border-radius: 0;
    width: 100%;
    max-width: 100%;
    display: block;
    margin: 0.1rem 0;
    background-color: rgba(245, 234, 232, 0.6);
    color: #000;
  }

  input[type="color"].filled,
  input[type="color"]:focus,
  input[type="date"].filled,
  input[type="date"]:focus,
  input[type="datetime-local"].filled,
  input[type="datetime-local"]:focus,
  input[type="datetime"].filled,
  input[type="datetime"]:focus,
  input[type="email"].filled,
  input[type="email"]:focus,
  input[type="month"].filled,
  input[type="month"]:focus,
  input[type="number"].filled,
  input[type="number"]:focus,
  input[type="password"].filled,
  input[type="password"]:focus,
  input[type="range"].filled,
  input[type="range"]:focus,
  input[type="search"].filled,
  input[type="search"]:focus,
  input[type="tel"].filled,
  input[type="tel"]:focus,
  input[type="text"].filled,
  input[type="text"]:focus,
  input[type="time"].filled,
  input[type="time"]:focus,
  input[type="url"].filled,
  input[type="url"]:focus,
  input[type="week"].filled,
  input[type="week"]:focus,
  select.filled,
  select:focus,
  textarea.filled,
  textarea:focus {
    border: 1px solid #93c693;
    background-color: #fff !important;
    outline: 0;
  }

  textarea {
    width: 100%;
  }

  .floating-label {
    display: none;
    position: absolute;
    top: 0;
    z-index: 9;
    left: 0.75rem;
    font-size: 0.67rem;
    transform: translate(0, -50%);
    padding: 0 0.75rem;
    background: #fff;
    line-height: 1.25;
  }

  .breadcrumbs {
    position: absolute;
    top: 20px;
    left: 0;
    font-size: 0.7rem;
    text-align: right;
    text-transform: uppercase;
    color: #828282;
    letter-spacing: 0.05em;
    transform: rotate(90deg);
    transform-origin: left;
    -webkit-text-orientation: sideways;
    text-orientation: sideways;
    max-width: 28.75rem;
  }

  @media only screen and (max-width: 1440px) {
    .breadcrumbs {
      left: 2.5rem;
    }
  }

  @media only screen and (max-width: 1025px) {
    .breadcrumbs {
      max-width: 37.5rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .breadcrumbs {
      position: relative;
      top: auto;
      left: auto;
      margin-bottom: 2rem;
      transform: none;
      max-width: 100%;
    }
  }

  .breadcrumbs__list {
    transform: rotate(180deg);
  }

  @media only screen and (max-width: 768px) {
    .breadcrumbs__list {
      transform: none;
    }
  }

  .breadcrumbs__list a {
    color: #828282;
    text-decoration: none;
  }

  .breadcrumbs__list a:hover {
    color: #0f2b46;
  }

  .breadcrumbs__list>span:after {
    content: "";
    display: inline-block;
    width: 6px;
    height: 6px;
    margin: 0 0.3rem;
    border-right: 0.1rem solid;
    border-bottom: 0.1rem solid;
    transform: rotate(-45deg);
    border-color: #828282;
  }

  .breadcrumbs__list>span:last-child {
    color: #333;
  }

  .breadcrumbs__list>span:last-child:after {
    display: none;
  }

  .booking {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    font-size: 1.3rem;
    font-weight: 400;
    color: #fff;
    background-color: #3D6E84 !important;
    z-index: 2;
    transform: translateY(0);
  }

  @media only screen and (max-width: 1025px) {
    .booking {
      font-size: 0.9rem;
    }
  }

  .booking--scrolled {
    top: 0;
    bottom: auto;
  }

  @media only screen and (max-width: 1025px) {
    .booking--scrolled {
      top: auto;
      bottom: 0;
    }
  }

  .booking--hide {
    top: 0;
    bottom: auto;
    transform: translateY(-100px);
  }

  @media only screen and (max-width: 1025px) {
    .booking--hide {
      top: auto;
      bottom: 0;
      transform: translateY(0);
    }
  }

  .booking .container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding-top: 1.3rem;
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 1.3rem;
    max-width: 1281px;
  }

  @media only screen and (max-width: 1440px) {
    .booking .container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .booking .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  .booking svg {
    width: auto;
    height: 20px;
    margin-right: 0.5rem;
  }

  @media screen and (-ms-high-contrast: active),
  (-ms-high-contrast: none) {
    .booking svg {
      width: 20px;
      height: 20px;
    }
  }

  @media only screen and (max-width: 1025px) {
    .booking svg {
      height: 15px;
      margin-right: 0.3rem;
    }
  }

  @media only screen and (max-width: 1025px) and (-ms-high-contrast: active),
  only screen and (max-width: 1025px) and (-ms-high-contrast: none) {
    .booking svg {
      width: 15px;
      height: 15px;
    }
  }

  .booking path {
    stroke: #fff;
  }

  .booking .button {
    background-color: #fff;
    border-color: #fff;
  }

  @media only screen and (max-width: 768px) {
    .booking .button {
      width: 100%;
    }
  }

  .booking .button:hover {
    background-color: #0f2b46;
    border-color: #0f2b46;
    text-decoration: none;
  }

  .booking__address {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    color: #fff;
    text-decoration: none;
  }

  @media only screen and (max-width: 768px) {
    .booking__address {
      width: 100%;
      padding-bottom: 1rem;
    }
  }

  .booking__address:hover {
    color: #fff;
    text-decoration: none;
  }

  .booking__address__building {
    display: inline-block;
    margin-right: 2rem;
  }

  @media only screen and (max-width: 768px) {
    .booking__address__building {
      margin-right: 1rem;
    }
  }

  .booking--room .booking__address__building {
    margin-right: 0.7rem;
  }

  .booking__address__building path,
  .booking__address__building svg {
    stroke-width: 4;
  }

  .booking__address__street {
    display: inline-block;
  }

  .booking__address__room {
    position: relative;
    padding-left: 1.2rem;
  }

  .booking__address__room:before {
    content: "";
    position: absolute;
    top: 0.7rem;
    left: 0;
    width: 8px;
    height: 8px;
    border-radius: 4px;
    background-color: #fff;
  }

  @media only screen and (max-width: 1025px) {
    .booking__address__room:before {
      top: 0.4rem;
    }
  }

  .booking__address__size {
    position: relative;
    margin-left: 2rem;
    margin-right: 2rem;
    padding-left: 2rem;
  }

  @media only screen and (max-width: 1025px) {
    .booking__address__size {
      padding: 0.4rem 0 0.4rem 1.5rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .booking__address__size {
      width: 100%;
      margin-left: 0;
      margin-right: 0;
    }
  }

  .booking__address__size:before {
    content: "";
    position: absolute;
    top: 0.3rem;
    left: 0;
    width: 18px;
    height: 18px;
    border: solid 2px #fff;
    border-radius: 2px;
  }

  @media only screen and (max-width: 1025px) {
    .booking__address__size:before {
      top: 0.6rem;
      width: 12px;
      height: 12px;
      border: solid 1px #fff;
    }
  }

  .site-header {
    position: relative;
    top: 0;
    z-index: 99999;
    transform: translateY(0);
  }

  @media only screen and (max-width: 1281px) {
    .site-header {
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
  }

  @media only screen and (min-width: 1025px) {
    .site-header.submenu-open {
      background-color: #fff;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
  }

  .admin-bar .site-header {
    top: 32px;
  }

  .site-header--hide {
    transform: translateY(-155px);
  }

  .menu-open .site-header {
    z-index: 0;
  }

  .site-header__container {
    position: relative;
    padding-top: 1rem;
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 1rem;
    max-width: 1281px;
    transition: all 0.2s ease;
    z-index: 1;
    min-height: 155px;
  }

  @media only screen and (max-width: 1025px) {
    .site-header--scrolled .site-header__container {
      padding-top: 0.8rem;
      padding-bottom: 1rem;
      min-height: 64px;
    }
  }

  @media only screen and (max-width: 1440px) {
    .site-header__container {
      padding-right: 1rem;
    }
  }

  @media only screen and (max-width: 1281px) {
    .site-header__container {
      padding-top: 1.3rem;
      padding-bottom: 1.3rem;
      background-color: #fff;
      min-height: 64px;
    }

    .menu-open .site-header__container {
      background-color: transparent;
    }
  }

  @media only screen and (max-width: 768px) {
    .site-header__container {
      padding-top: 0.8rem;
      padding-bottom: 1rem;
    }
  }

  .site-header--scrolled .site-header__container {
    min-height: 100px;
  }

  @media only screen and (max-width: 1025px) {
    .site-header--scrolled .site-header__container {
      min-height: 64px;
    }
  }

  .site-header__branding {
    position: absolute;
    top: 2rem;
    left: 0;
    line-height: 0;
    z-index: 10;
  }

  @media only screen and (max-width: 1440px) {
    .site-header__branding {
      top: 1.5rem;
      left: 2rem;
    }

    .site-header--scrolled .site-header__branding {
      top: 1rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .site-header__branding {
      top: 1rem;
      left: 1rem;
    }
  }

  .site-header--scrolled .site-header__branding {
    top: 1.5rem;
  }

  @media only screen and (max-width: 1025px) {
    .site-header--scrolled .site-header__branding {
      top: 1rem;
    }
  }

  .site-header__title {
    display: block;
  }

  @media only screen and (max-width: 1281px) {
    .site-header__title {
      display: none;
    }
  }

  .site-header__title svg {
    height: 85px;
    max-width: 120px;
  }

  .site-header--scrolled .site-header__title svg {
    height: 50px;
    max-width: 70px;
  }

  .site-header__title:hover {
    opacity: 0.6;
  }

  .site-header__title--mobile {
    display: none;
  }

  @media only screen and (max-width: 1281px) {
    .site-header__title--mobile {
      display: block;
    }
  }

  .site-header__title--mobile svg {
    height: auto;
    max-width: 100px;
  }

  .site-header .language-navigation {
    position: relative;
    display: block;
    width: 100%;
    margin-bottom: 1rem;
    text-align: right;
    z-index: 0;
  }

  @media only screen and (max-width: 1281px) {
    .site-header .language-navigation {
      display: none;
      margin-bottom: 0;
    }
  }

  .site-header .language-navigation ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }

  .site-header .language-navigation ul li {
    margin: 0;
    padding: 0;
  }

  .site-header .language-navigation ul li.current-lang {
    display: none;
  }

  .site-header .language-navigation ul a {
    font-size: 0.7rem;
    text-transform: uppercase;
    color: #828282;
    letter-spacing: 0.25em;
    text-decoration: none;
  }

  .site-header .language-navigation ul a:hover {
    color: #4f4f4f;
    text-decoration: underline;
  }

  .site-header .language-navigation--small {
    display: none;
    width: auto;
    height: 40px;
    font-size: 0.9rem;
    font-weight: 600;
  }

  @media only screen and (max-width: 1281px) {
    .site-header .language-navigation--small {
      display: flex;
      align-items: center;
      margin-right: 3.5rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .site-header .language-navigation--small {
      display: none;
    }
  }

  .site-header .language-navigation--small svg {
    width: 32px;
    height: 32px;
    margin-right: 0.5rem;
  }

  .site-header .language-navigation--small path {
    stroke: #828282;
  }

  .site-header .language-navigation--small.show {
    display: flex;
    align-items: center;
  }

  @media only screen and (min-width: 1025px) {
    .site-header .language-navigation--small.show {
      display: none;
    }
  }

  .site-header--scrolled .language-navigation {
    margin-bottom: 0.5rem;
  }

  .site-header__overlay {
    position: fixed;
    top: 0;
    right: -110%;
    width: 100%;
    height: 100%;
    background-color: #fff;
    z-index: 3;
    transition: all 0.4s ease;
  }

  .menu-open .site-header__overlay {
    right: 0;
  }

  .site {
    width: 100%;
    overflow: hidden;
  }

  .site__bg {
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .home .site__bg {
    display: block;
  }

  .show-graphic .site__bg {
    position: fixed;
    display: block;
    background-color: #fff;
    z-index: 9;
  }

  .site__bg__graphic1 svg,
  .site__bg__graphic2 svg,
  .site__bg__graphic3 svg,
  .site__bg__graphic4 svg {
    width: 100%;
    height: auto;
  }

  .site__bg__graphic1 {
    position: absolute;
    top: 11rem;
    left: 0;
    width: 18%;
    z-index: 0;
    mix-blend-mode: multiply;
  }

  @media only screen and (max-width: 1025px) {
    .site__bg__graphic1 {
      top: 15%;
    }
  }

  @media only screen and (max-width: 600px) {
    .site__bg__graphic1 {
      top: 8rem;
    }
  }

  .show-graphic .site__bg__graphic1 {
    top: 60px;
    left: -20%;
    width: 50%;
    z-index: 2;
    transform: rotate(80deg);
  }

  .site__bg__graphic2 {
    position: absolute;
    top: 0;
    left: 0;
    width: 33%;
    z-index: 0;
    mix-blend-mode: multiply;
  }

  @media only screen and (max-width: 1025px) {
    .site__bg__graphic2 {
      top: 64px;
    }
  }

  .show-graphic .site__bg__graphic2 {
    top: 0rem;
    left: -2rem;
    width: 50%;
    z-index: 2;
  }

  .site__bg__graphic3 {
    position: absolute;
    top: 0;
    left: 20%;
    width: 31%;
    z-index: 0;
    mix-blend-mode: multiply;
  }

  @media only screen and (max-width: 1281px) {
    .site__bg__graphic3 {
      left: 20%;
    }
  }

  @media only screen and (max-width: 1025px) {
    .site__bg__graphic3 {
      top: 64px;
    }
  }

  .show-graphic .site__bg__graphic3 {
    display: none;
  }

  .site__bg__graphic4 {
    position: absolute;
    top: 30rem;
    left: 0;
    width: 15%;
    z-index: 3;
    mix-blend-mode: multiply;
  }

  @media only screen and (max-width: 1281px) {
    .site__bg__graphic4 {
      left: -5%;
      z-index: 1;
    }
  }

  @media only screen and (max-width: 1025px) {
    .site__bg__graphic4 {
      top: 25rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .site__bg__graphic4 {
      top: 30rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .site__bg__graphic4 {
      top: 24rem;
    }
  }

  @media only screen and (max-width: 480px) {
    .site__bg__graphic4 {
      top: 18rem;
      width: 20%;
    }
  }

  .show-graphic .site__bg__graphic4 {
    display: none;
  }

  .site__bg__graphic4 svg {
    opacity: 0.8;
  }

  .site__bg__graphic5 {
    display: none;
  }

  .show-graphic .site__bg__graphic5 {
    position: fixed;
    display: block;
    right: -10%;
    bottom: -10%;
    width: 50%;
    z-index: 2;
    transform: rotate(180deg);
  }

  .show-graphic .site__bg__graphic5 svg {
    width: 100%;
    height: auto;
  }

  .show-graphic .site__bg__graphic5 path {
    fill: #aed8ed;
  }

  .hero {
    padding: 2rem 0;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    position: relative;
  }

  @media only screen and (max-width: 1025px) {
    .hero {
      padding: 1rem 0;
      z-index: 0;
    }
  }

  .hero .container {
    max-width: 1281px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding-left: 0;
    padding-right: 0;
  }

  @media only screen and (max-width: 1281px) {
    .hero .container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .hero .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  .hero__title {
    margin: 0;
  }

  .hero__image {
    width: 50%;
  }

  @media only screen and (max-width: 768px) {
    .hero__image {
      width: 100%;
    }
  }

  .hero__image img {
    width: 100%;
  }

  .hero__text {
    width: 50%;
    padding-left: 6rem;
    padding-right: 6rem;
  }

  @media only screen and (max-width: 1281px) {
    .hero__text {
      padding-left: 4rem;
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 1025px) {
    .hero__text {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .hero__text {
      width: 100%;
      padding: 2rem 0 1rem 0;
    }
  }

  .hero__text p {
    margin-bottom: 3rem;
  }

  @media only screen and (max-width: 1281px) {
    .hero__text p {
      min-height: 0;
      margin-bottom: 2rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .hero__text p {
      margin-bottom: 3rem;
    }
  }

  .hero__text .button {
    margin-right: 0.5rem;
  }

  @media only screen and (max-width: 768px) {
    .hero__text .button {
      margin-bottom: 1rem;
    }
  }

  .hero-sub {
    position: relative;
    margin: 0 auto;
    padding: 0 6.25rem;
    max-width: 1281px;
  }

  @media only screen and (max-width: 1440px) {
    .hero-sub {
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .hero-sub {
      padding: 0 2rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .hero-sub {
      padding: 0 1rem;
    }
  }

  .hero-sub .container {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    max-width: 100%;
  }

  .hero-sub__image {
    width: 15.625rem;
  }

  @media only screen and (max-width: 768px) {
    .hero-sub__image {
      width: auto;
    }
  }

  .hero-sub__image svg {
    width: 250px;
    height: auto;
    transform: rotate(180deg);
  }

  @media only screen and (max-width: 768px) {
    .hero-sub__image svg {
      position: absolute;
      top: 0;
      left: -3rem;
      width: 180px;
    }
  }

  .hero-sub--color-sand path {
    fill: #f5eae8;
  }

  .hero-sub--color-blue path {
    fill: #aed8ed;
  }

  .hero-sub--color-petrol path {
    fill: #1d6e87;
  }

  .hero-sub--color-pink path {
    fill: #ffd9ea;
  }

  .hero-sub--color-orange path {
    fill: #0f2b46;
  }

  .hero-sub--color-yellow path {
    fill: #f9de85;
  }

  .hero-sub--color-green path {
    fill: #93c693;
  }

  .hero-sub h1 {
    margin-bottom: 1.5rem;
  }

  @media only screen and (max-width: 1281px) {
    .hero-sub h1 {
      margin-bottom: 0.5rem;
    }
  }

  .hero-sub h2 {
    margin-bottom: 0;
    line-height: 1.1;
  }

  @media only screen and (max-width: 1025px) {
    .hero-sub h2 {
      font-size: 3.75rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .hero-sub h2 {
      margin-bottom: 2rem;
      font-size: 2.5rem;
    }
  }

  .hero-sub__title {
    position: relative;
    width: 70%;
    margin-left: -160px;
    padding-top: 1.5rem;
  }

  @media only screen and (max-width: 768px) {
    .hero-sub__title {
      width: 100%;
      margin-left: 0;
      padding-top: 0.5rem;
    }
  }

  .hero-sub__title__wrapper {
    min-height: 250px;
  }

  @media only screen and (max-width: 768px) {
    .hero-sub__title__wrapper {
      min-height: 180px;
    }
  }

  .hero-sub__title a {
    color: #4f4f4f;
    text-decoration: none;
  }

  .hero-sub__title a:hover {
    text-decoration: underline;
  }

  .hero-sub__title p {
    margin: 0;
  }

  @media only screen and (max-width: 1025px) {
    .hero-sub__title p {
      display: flex;
      flex-wrap: wrap;
      margin-top: 1rem;
      white-space: nowrap;
    }
  }

  .hero-sub__building {
    position: relative;
    margin-right: 1rem;
    padding-left: 25px;
  }

  .hero-sub__building:before {
    content: "";
    position: absolute;
    top: 1px;
    left: 0;
    width: 15px;
    height: 15px;
    margin-right: 1rem;
    background-image: url(../../svg/house.svg);
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
  }

  .hero-sub__map-link {
    position: relative;
    padding-left: 23px;
  }

  .hero-sub__map-link:before {
    content: "";
    position: absolute;
    top: 1px;
    left: 0;
    width: 15px;
    height: 15px;
    margin-right: 1rem;
    background-image: url(../../svg/mappin.svg);
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
  }

  .site-footer {
    overflow: hidden;
    position: relative;
    margin: 0;
    background: #f1f1f1;
    background-color: #fff;
    background-image: url(/wp-content/reservations/footer2.svg);
    background-position: center bottom;
    background-repeat: no-repeat;
    background-size: 40% auto;
  }

  @media only screen and (max-width: 768px) {
    .site-footer {
      background-image: url(../../images/footer-mobile.png);
      background-position: -500px bottom;
      background-size: auto;
    }
  }

  @media only screen and (max-width: 600px) {
    .site-footer {
      background-position: -600px bottom;
      background-size: auto;
    }
  }

  .site-footer__wrapper {
    background-image: url(../../svg/footer1.svg);
    background-position: left bottom;
    background-repeat: no-repeat;
    background-size: 55% auto;
    mix-blend-mode: multiply;
  }

  @media only screen and (max-width: 768px) {
    .site-footer__wrapper {
      background: 0 0;
    }
  }

  .site-footer__holder {
    padding: 7.5rem 0 3.75rem 0;
    background-image: url(../../svg/footer3.svg);
    background-position: right bottom;
    background-repeat: no-repeat;
    background-size: 40% auto;
    mix-blend-mode: multiply;
  }

  @media only screen and (max-width: 1025px) {
    .site-footer__holder {
      padding: 3.75rem 0;
    }
  }

  @media only screen and (max-width: 768px) {
    .site-footer__holder {
      padding: 3.75rem 0 0 0;
      background: 0 0;
    }
  }

  .site-footer .container {
    max-width: 1281px;
    padding-left: 0;
    padding-right: 0;
    z-index: 1;
  }

  @media only screen and (max-width: 1440px) {
    .site-footer .container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .site-footer .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  .site-footer__row {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 4rem;
  }

  @media only screen and (max-width: 1025px) {
    .site-footer__row {
      padding-bottom: 0;
    }
  }

  .site-footer__row:last-child {
    padding: 0;
    margin-top: -6rem;
  }

  @media only screen and (max-width: 1025px) {
    .site-footer__row:last-child {
      margin-top: 0;
    }

    .site-footer__row:last-child .site-footer__col1 {
      order: 1;
    }

    .site-footer__row:last-child .site-footer__col3 {
      order: 0;
    }
  }

  .site-footer__row a:hover {
    text-decoration: none;
    color: #000;
  }

  .site-footer__col1 {
    width: 35%;
  }

  @media only screen and (max-width: 1281px) {
    .site-footer__col1 {
      width: 30%;
    }
  }

  @media only screen and (max-width: 1025px) {
    .site-footer__col1 {
      width: 100%;
      margin-bottom: 2rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .site-footer__col1 {
      margin-bottom: 3rem;
    }
  }

  .site-footer__col1 img {
    margin-right: 4rem;
    margin-bottom: 2rem;
  }

  @media only screen and (max-width: 1025px) {
    .site-footer__col1 img {
      display: block;
    }
  }

  .site-footer__col1 img:last-child {
    margin-right: 0;
  }

  .site-footer__col2 {
    width: 40%;
    padding: 0 2rem;
  }

  @media only screen and (max-width: 1025px) {
    .site-footer__col2 {
      width: 100%;
      padding: 0;
    }
  }

  .site-footer__col3 {
    display: flex;
    width: 25%;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
  }

  @media only screen and (max-width: 1281px) {
    .site-footer__col3 {
      width: 30%;
    }
  }

  @media only screen and (max-width: 1025px) {
    .site-footer__col3 {
      width: 100%;
      justify-content: flex-start;
    }
  }

  .site-footer__main-logo {
    line-height: 0;
    font-size: 0;
  }

  .site-footer__main-logo svg {
    width: 200px;
    height: auto;
  }

  @media only screen and (max-width: 768px) {
    .site-footer__main-logo svg {
      width: 180px;
    }
  }

  .site-footer__contact1 {
    font-size: 1.4rem;
  }

  @media only screen and (max-width: 768px) {
    .site-footer__contact1 {
      font-size: 1.2rem;
      margin-bottom: 3rem;
    }
  }

  .site-footer__contact2 {
    font-size: 1rem;
  }

  @media only screen and (max-width: 768px) {
    .site-footer__contact2 {
      margin-bottom: 2rem;
    }
  }

  .site-footer__menu {
    display: block;
    list-style: none;
    margin: 0;
    padding: 0 0 2rem 0;
    font-size: 0.9rem;
    width: 50%;
  }

  @media only screen and (max-width: 1025px) {
    .site-footer__menu {
      width: auto;
      min-width: 35%;
      padding-bottom: 1rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .site-footer__menu:first-child {
      padding-right: 1rem;
    }
  }

  @media only screen and (max-width: 480px) {
    .site-footer__menu {
      min-width: 50%;
    }
  }

  @media only screen and (max-width: 360px) {
    .site-footer__menu {
      min-width: 100%;
    }
  }

  .site-footer__menu li {
    padding: 0.2rem 0;
  }

  .site-footer__menu--list {
    width: 100% !important;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    color: #828282;
    padding: 2rem 0 0 0;
  }

  @media only screen and (max-width: 1025px) {
    .site-footer__menu--list {
      justify-content: flex-start;
      padding-bottom: 2rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .site-footer__menu--list {
      padding-top: 1rem;
      padding-bottom: 3rem;
    }
  }

  .site-footer__menu--list li {
    display: block;
    width: 50%;
  }

  @media only screen and (max-width: 1025px) {
    .site-footer__menu--list li:nth-child(odd) {
      width: 35%;
    }

    .site-footer__menu--list li:nth-child(even) {
      width: 60%;
    }
  }

  @media only screen and (max-width: 600px) {
    .site-footer__menu--list li:nth-child(odd) {
      width: 35%;
    }

    .site-footer__menu--list li:nth-child(even) {
      width: 60%;
    }
  }

  @media only screen and (max-width: 480px) {

    .site-footer__menu--list li:nth-child(even),
    .site-footer__menu--list li:nth-child(odd) {
      width: 50%;
    }
  }

  @media only screen and (max-width: 360px) {

    .site-footer__menu--list li:nth-child(even),
    .site-footer__menu--list li:nth-child(odd) {
      width: 100%;
    }
  }

  .site-footer__menu--list a {
    color: #828282;
  }

  .topscroll {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
    background: #7e7b7b;
    cursor: pointer;
    padding: 0;
    transition: all 0.2s ease;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
  }

  .topscroll span {
    background: #fff;
    width: 12px;
    height: 3px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-3px, -50%) rotate(45deg);
    transition: all 0.2s ease;
  }

  .topscroll span:last-of-type {
    transform: translate(-10px, -50%) rotate(-45deg);
  }

  .topscroll:hover span {
    top: 40%;
  }

  .topscroll--show {
    opacity: 1;
    visibility: visible;
  }

  .footer-block {
    background-color: #f5eae8;
    padding: 3.75rem 0;
    text-align: center;
  }

  .footer-block h3 {
    margin-top: 0;
  }

  .footer-block__text {
    margin: 0 auto 2rem auto;
    max-width: 60%;
    font-size: 1.5rem;
  }

  @media only screen and (max-width: 1025px) {
    .footer-block__text {
      max-width: 80%;
    }
  }

  @media only screen and (max-width: 768px) {
    .footer-block__text {
      max-width: 100%;
      font-size: 1.2rem;
    }
  }

  .footer-block__or {
    margin: 0 2rem;
    font-size: 0.8rem;
    text-transform: uppercase;
  }

  @media only screen and (max-width: 768px) {
    .footer-block__or {
      margin: 0 0.5rem;
    }
  }

  @media only screen and (max-width: 360px) {
    .footer-block__or {
      margin: 0.5rem 0;
      width: 100%;
      display: inline-block;
    }
  }

  .main-navigation {
    position: relative;
    display: flex;
    justify-content: flex-end;
    width: 100%;
    z-index: 9;
  }

  .main-navigation .menu {
    display: flex;
    align-items: flex-start;
    margin: 0;
    padding-left: 0;
    font-size: 1rem;
    list-style: none;
  }

  @media only screen and (max-width: 1281px) {
    .main-navigation .menu {
      margin-right: 2rem;
    }
  }

  .main-navigation .menu__item {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    padding: 0.7rem 1.2rem;
  }

  @media only screen and (max-width: 1281px) {
    .main-navigation .menu__item {
      display: none;
    }

    .main-navigation .menu__item.menu-item-32 {
      display: flex;
    }
  }

  @media only screen and (max-width: 1281px) and (max-width: 768px) {
    .main-navigation .menu__item.menu-item-32 {
      display: none;
    }
  }

  .main-navigation .menu__item__wrapper {
    display: flex;
  }

  .main-navigation .menu__item.focus>ul {
    opacity: 1;
    visibility: visible;
    transition-delay: 0s;
  }

  .main-navigation .menu__item-lvl-1:hover ul {
    top: 0;
    left: 100%;
    transform: none;
  }

  .main-navigation .menu__item-lvl-1.menu__item--has-children {
    margin: 0;
  }

  .main-navigation .menu__item-lvl-1.menu__item--has-children .menu__dropdown-toggle {
    right: 0.5rem;
    transform: translate(0, -50%) rotate(-90deg);
  }

  .main-navigation .menu__item-lvl-1.menu__item--has-children .menu__dropdown-toggle-icon:after {
    border-color: #fff;
  }

  .main-navigation .menu__link {
    position: relative;
    display: block;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #111;
    text-decoration: none;
    transition: all 0.2s ease;
    letter-spacing: 0.25em;
  }

  .main-navigation .menu__link:before {
    content: "";
    position: absolute;
    bottom: -0.2rem;
    left: 0;
    width: 0;
    height: 1px;
    margin-right: 1.2rem;
    background: #000;
    transition: all 0.2s ease;
  }

  .main-navigation .menu__link:focus,
  .main-navigation .menu__link:hover {
    color: #000;
  }

  .main-navigation .menu__link:focus:before,
  .main-navigation .menu__link:hover:before {
    width: 100%;
  }

  .main-navigation .menu .current_page_item .menu__link {
    color: #000;
  }

  .main-navigation .menu .current_page_item .menu__link:before {
    width: 100%;
  }

  .main-navigation .menu .current_page_item li .menu__link:before {
    width: 0;
  }

  .main-navigation .menu__item:last-child.menu__item-lvl-0 {
    margin-left: 1rem;
    padding: 0;
  }

  @media only screen and (max-width: 1281px) {
    .main-navigation .menu__item:last-child.menu__item-lvl-0 {
      display: inline-block;
    }
  }

  @media only screen and (max-width: 768px) {
    .main-navigation .menu__item:last-child.menu__item-lvl-0 {
      display: none;
    }
  }

  .main-navigation .menu__item:last-child.menu__item-lvl-0 .menu__item__wrapper {
    justify-content: center;
    width: 100%;
  }

  .main-navigation .menu__item:last-child.menu__item-lvl-0 .menu__link {
    padding: 0.7rem 1.2rem;
    border-radius: 2px;
    color: #fff;
    background-color: #3D6E84 !important;
  }

  .main-navigation .menu__item:last-child.menu__item-lvl-0 .menu__link:before {
    display: none;
  }

  .main-navigation .menu__item:last-child.menu__item-lvl-0 .menu__link:hover {
    background-color: #0f2b46;
  }

  .main-navigation .menu__sub {
    display: block;
    flex-direction: column;
    width: 100%;
    height: 0;
    margin: 1rem 0 0 0;
    padding: 0;
    list-style-type: none;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease;
    overflow: hidden;
  }

  .main-navigation .menu__sub--toggled {
    height: auto;
    opacity: 1;
    visibility: visible;
    transition-delay: 0s;
  }

  .main-navigation .menu__sub .menu__item {
    display: block;
    padding: 0;
  }

  .main-navigation .menu__sub a {
    width: auto;
    padding: 0.3rem 0rem;
    font-size: 0.875rem;
    font-weight: 400;
    text-transform: none;
    color: #000;
    letter-spacing: 0.02em;
  }

  .main-navigation .menu__sub .current-menu-item a,
  .main-navigation .menu__sub a:hover {
    text-decoration: underline;
  }

  .main-navigation .menu__sub .current-menu-item a:before,
  .main-navigation .menu__sub a:hover:before {
    display: none;
  }

  .main-navigation .menu__lvl-2 {
    top: 0;
    left: -999rem;
    box-shadow: 0 0 1rem rgba(0, 0, 0, 0.2);
  }

  .main-navigation .menu__lvl-2.menu__sub--toggled {
    top: 0;
    left: 100%;
    transform: none;
  }

  .main-navigation .menu__dropdown-toggle {
    display: inline-block;
    margin-left: 0.5rem;
    padding: 0;
    border: none;
    line-height: 0;
    color: transparent;
    background-color: transparent;
    transition: all 0.2s ease;
    outline-style: none;
  }

  .main-navigation .menu__dropdown-toggle-icon {
    position: relative;
    display: block;
  }

  .main-navigation .menu__dropdown-toggle-icon svg {
    width: 16px;
    height: 16px;
  }

  .main-navigation .menu__dropdown-toggle-icon:after {
    content: "";
    position: absolute;
    display: block;
    top: 4px;
    left: 5px;
    width: 5px;
    height: 5px;
    border-right: 1.5px solid;
    border-bottom: 1.5px solid;
    transition: all 0.2s ease;
    transform: rotate(45deg);
    border-color: #fff;
    transition: all 0.2s ease;
  }

  .main-navigation .menu__dropdown-toggle:hover {
    border: none;
    background-color: transparent;
    opacity: 0.6;
  }

  .main-navigation .menu__dropdown-toggle--toggled .menu__dropdown-toggle-icon {
    transform: rotate(180deg);
  }

  .menu-show {
    position: absolute;
    display: none;
    top: 0;
    right: 0;
    width: 40px;
    height: 40px;
    padding: 0;
    border: none;
    background: 0 0;
    outline: 0;
    z-index: 999999;
  }

  @media only screen and (max-width: 1281px) {
    .menu-show {
      display: inline-block;
    }
  }

  @media only screen and (max-width: 768px) {
    .menu-show {
      top: 0.3rem;
    }
  }

  .menu-show:focus,
  .menu-show:hover {
    border: none;
    background: 0 0;
    opacity: 0.6;
  }

  .menu-show__label {
    display: none;
    color: #111;
  }

  .menu-show__inner {
    position: absolute;
    display: block;
    top: 50%;
    right: 0;
    width: 2rem;
    height: 4px;
    border-radius: 2px;
    background-color: #111;
    transform: translate(0, -50%);
    transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    transition-duration: 0.22s;
    transition-property: transform;
  }

  .menu-show__inner:before {
    content: "";
    position: absolute;
    display: block;
    top: -10px;
    width: 2rem;
    height: 4px;
    border-radius: 2px;
    background-color: #111;
    transition: top 0.1s ease-in 0.25s, opacity 0.1s ease-in;
  }

  .menu-show__inner:after {
    content: "";
    position: absolute;
    display: block;
    bottom: -10px;
    width: 2rem;
    height: 4px;
    border-radius: 2px;
    background-color: #111;
    transition: bottom 0.1s ease-in 0.25s,
      transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  .nav-mobile {
    position: fixed;
    top: 0;
    right: -110%;
    width: 100%;
    height: 100vh;
    padding: 0;
    background-color: #fff;
    overflow: hidden;
    transition: all 0.2s ease;
    z-index: 99999;
  }

  .menu-open .nav-mobile {
    right: 0;
  }

  .nav-mobile__content {
    position: relative;
    width: 100%;
    padding: 6rem 2rem 0 2rem;
    z-index: 1;
    transition: all 0.2s ease;
    overflow: hidden;
    max-height: 100%;
  }

  @media only screen and (max-width: 768px) {
    .nav-mobile__content {
      padding: 4rem 2rem 0 2rem;
    }
  }

  .nav-mobile .menu {
    display: block;
    margin: 0;
    max-height: 100%;
  }

  @media only screen and (max-width: 600px) {
    .nav-mobile .menu {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 2rem 1rem 2rem 1rem;
    }
  }

  .nav-mobile .menu__item {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: center;
    width: 100%;
    padding: 0.7rem 1.2rem;
    text-align: center;
  }

  .nav-mobile .menu__item__wrapper {
    display: flex;
    padding-left: 1rem;
  }

  .nav-mobile .menu.focus>ul {
    opacity: 1;
    visibility: visible;
    transition-delay: 0s;
  }

  .nav-mobile .menu-lvl-1:hover ul {
    top: 0;
    left: 0;
    transform: none;
  }

  .nav-mobile .menu-lvl-1.menu__item--has-children {
    margin: 0;
    border: none;
  }

  .nav-mobile .menu-lvl-1.menu__item--has-children .menu__dropdown-toggle {
    top: 0;
    right: 0;
  }

  .nav-mobile .menu-lvl-1.menu__item--has-children .menu__dropdown-toggle-icon:after {
    border-color: #fff;
  }

  .nav-mobile .menu__link {
    position: relative;
    display: inline-block;
    width: auto;
    font-size: 1.5rem;
    font-weight: 400;
    color: #000;
    text-decoration: none;
    transition: all 0.2s ease;
  }

  .nav-mobile .menu__link:before {
    content: "";
    position: absolute;
    bottom: -0.2rem;
    left: 0;
    width: 0;
    height: 1px;
    margin-right: 1.2rem;
    background: #000;
    transition: all 0.2s ease;
  }

  .nav-mobile .menu__link:focus,
  .nav-mobile .menu__link:hover {
    color: #000;
  }

  .nav-mobile .menu__link:focus:before,
  .nav-mobile .menu__link:hover:before {
    width: 100%;
  }

  .nav-mobile .menu__item:last-child.menu__item-lvl-0 {
    margin-top: 1rem;
    padding: 0;
    text-align: center;
  }

  .nav-mobile .menu__item:last-child.menu__item-lvl-0 .menu__item__wrapper {
    justify-content: center;
    width: 100%;
  }

  .nav-mobile .menu__item:last-child.menu__item-lvl-0 .menu__link {
    display: inline-block;
    width: 100%;
    padding: 1rem 1.2rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #fff;
    background-color: #3D6E84 !important;
    max-width: 240px;
    letter-spacing: 0.25em;
  }

  .nav-mobile .menu__item:last-child.menu__item-lvl-0 .menu__link:before {
    display: none;
  }

  .nav-mobile .menu__item:last-child.menu__item-lvl-0 .menu__link:hover {
    background-color: #0f2b46;
  }

  .nav-mobile .menu__sub {
    position: relative;
    flex-direction: column;
    width: 100%;
    height: 0;
    margin: 0;
    padding: 0 0 0 1rem;
    background: 0 0;
    list-style-type: none;
    z-index: 999;
    visibility: hidden;
    transition: opacity 0.2s ease;
    overflow: hidden;
    transform: none;
    opacity: 1;
    max-height: 0;
  }

  .nav-mobile .menu__sub--toggled {
    height: auto;
    margin: 1rem 0 0 0;
    opacity: 1;
    visibility: visible;
    transition-delay: 0s;
    max-height: none;
  }

  .nav-mobile .menu__sub.menu__lvl-1:before,
  .nav-mobile .menu__sub:before {
    content: none;
  }

  .nav-mobile .menu__sub .menu__item {
    padding: 0;
  }

  .nav-mobile .menu__sub a {
    width: auto;
    padding: 0.3rem 0;
    font-size: 1.2rem;
    font-weight: 400;
    text-transform: none;
    color: #000;
    letter-spacing: 0.02em;
  }

  .nav-mobile .menu__sub .current-menu-item a,
  .nav-mobile .menu__sub a:hover {
    text-decoration: underline;
  }

  .nav-mobile .menu__sub .current-menu-item a:before,
  .nav-mobile .menu__sub a:hover:before {
    display: none;
  }

  .nav-mobile .menu__lvl-2 {
    top: 0;
    left: auto;
  }

  .nav-mobile .menu__lvl-2.menu__sub--toggled {
    top: 0;
    left: auto;
    transform: none;
  }

  .nav-mobile .menu__dropdown-toggle {
    display: inline-block;
    top: 0.6rem;
    right: 0;
    width: 2.25rem;
    height: 2.25rem;
    margin-left: 0.5rem;
    padding: 0;
    border: none;
    line-height: 0;
    color: transparent;
    background-color: transparent;
    transition: all 0.2s ease;
    outline-style: none;
    transform: none;
  }

  .nav-mobile .menu__dropdown-toggle-icon {
    position: relative;
    display: block;
  }

  .nav-mobile .menu__dropdown-toggle-icon svg {
    width: 20px;
    height: auto;
  }

  .nav-mobile .menu__dropdown-toggle-icon:after {
    content: "";
    position: absolute;
    display: block;
    top: 6px;
    left: 15px;
    width: 6px;
    height: 6px;
    border-right: 1.5px solid;
    border-bottom: 1.5px solid;
    transition: all 0.2s ease;
    transform: rotate(45deg);
    border-color: #fff;
    transition: all 0.2s ease;
  }

  .nav-mobile .menu__dropdown-toggle:hover {
    border: none;
    background-color: transparent;
    opacity: 0.6;
  }

  .nav-mobile .menu__dropdown-toggle--toggled {
    transform: rotate(180deg);
  }

  .nav-mobile .menu-close {
    position: absolute;
    display: block;
    top: 1.3rem;
    right: 1.5rem;
    width: 40px;
    height: 40px;
    border: none;
    background: 0 0;
    outline: 0;
    z-index: 999999;
  }

  @media only screen and (max-width: 768px) {
    .nav-mobile .menu-close {
      top: 0.8rem;
    }
  }

  .nav-mobile .menu-close:focus,
  .nav-mobile .menu-close:hover {
    border: none;
    background: 0 0;
    opacity: 0.6;
  }

  .nav-mobile .menu-close__label {
    display: none;
    color: #111;
  }

  .nav-mobile .menu-close__inner {
    position: absolute;
    display: block;
    top: 50%;
    right: 0;
    width: 2rem;
    height: 3px;
    border-radius: 2px;
    background-color: #111;
    transition-duration: 0.22s;
    transition-property: transform;
    transition-delay: 0.12s;
    transition-timing-function: cubic-bezier(0.22, 0.61, 0.36, 1);
    transform: rotate(225deg);
  }

  .nav-mobile .menu-close__inner:before {
    content: "";
    position: absolute;
    display: block;
    top: 0;
    width: 2rem;
    height: 3px;
    border-radius: 2px;
    background-color: #111;
    transition: top 0.1s ease-out, opacity 0.1s ease-out 0.12s;
    opacity: 0;
  }

  .nav-mobile .menu-close__inner:after {
    content: "";
    position: absolute;
    display: block;
    bottom: 0;
    width: 2rem;
    height: 3px;
    border-radius: 2px;
    background-color: #111;
    transition: bottom 0.1s ease-out,
      transform 0.22s cubic-bezier(0.215, 0.61, 0.355, 1) 0.12s;
    transform: rotate(-90deg);
  }

  .nav-mobile__logo {
    position: absolute;
    top: 1.4rem;
    left: 2rem;
  }

  @media only screen and (max-width: 768px) {
    .nav-mobile__logo {
      top: 1rem;
      left: 1rem;
    }
  }

  .nav-mobile__logo svg {
    height: auto;
    max-width: 100px;
  }

  .nav-mobile__languages {
    position: absolute;
    display: flex;
    align-items: center;
    top: 1.3rem;
    right: 5rem;
    width: auto;
    height: 40px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    font-weight: 600;
    text-align: right;
    z-index: 0;
  }

  @media only screen and (max-width: 768px) {
    .nav-mobile__languages {
      top: 0.8rem;
    }
  }

  .nav-mobile__languages svg {
    width: 32px;
    height: 32px;
    margin-right: 0.5rem;
  }

  .nav-mobile__languages svg path {
    stroke: #828282;
  }

  .nav-mobile__languages ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }

  .nav-mobile__languages ul li {
    margin: 0;
    padding: 0;
  }

  .nav-mobile__languages ul li.current-lang {
    display: none;
  }

  .nav-mobile__languages ul a {
    font-size: 0.7rem;
    text-transform: uppercase;
    color: #828282;
    letter-spacing: 0.25em;
    text-decoration: none;
  }

  .nav-mobile__languages ul a:hover {
    color: #4f4f4f;
    text-decoration: underline;
  }

  .nav-mobile__graphic1,
  .nav-mobile__graphic2,
  .nav-mobile__graphic3 {
    position: absolute;
    z-index: 0;
  }

  .nav-mobile__graphic1 svg,
  .nav-mobile__graphic2 svg,
  .nav-mobile__graphic3 svg {
    width: 100%;
    height: auto;
  }

  .nav-mobile__graphic1 {
    top: 0;
    left: -20%;
    width: 50%;
    mix-blend-mode: multiply;
    transform: rotate(90deg);
  }

  @media only screen and (max-width: 768px) {
    .nav-mobile__graphic1 {
      top: 30px;
      left: -75%;
      width: 100%;
      transform: rotate(80deg);
    }
  }

  .nav-mobile__graphic2 {
    top: 0rem;
    left: -2rem;
    width: 50%;
    mix-blend-mode: multiply;
  }

  @media only screen and (max-width: 768px) {
    .nav-mobile__graphic2 {
      width: 80%;
    }
  }

  .nav-mobile__graphic3 {
    right: -10%;
    bottom: -10%;
    width: 55%;
    transform: rotate(180deg);
  }

  @media only screen and (max-width: 768px) {
    .nav-mobile__graphic3 {
      right: -10%;
      bottom: -10%;
      width: 90%;
    }
  }

  .nav-mobile__graphic3 path {
    fill: #aed8ed;
  }

  .overview {
    margin: 3.75rem auto;
  }

  @media only screen and (max-width: 1025px) {
    .overview {
      margin-bottom: 1.875rem;
    }
  }

  @media only screen and (max-width: 1440px) {
    .overview {
      max-width: 1281px;
    }
  }

  @media only screen and (max-width: 1440px) {
    .overview__container {
      padding: 0 6.25rem;
      max-width: 100%;
    }
  }

  @media only screen and (max-width: 768px) {
    .overview__container {
      padding: 0 2rem;
    }
  }

  .overview__cols {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .overview__col {
    padding: 1.5rem 3rem;
    border-left: solid 1px #000;
    flex-grow: 1;
    flex-basis: 0;
  }

  @media only screen and (max-width: 1025px) {
    .overview__col {
      width: 50%;
      margin-left: -1px;
      margin-bottom: 1rem;
      padding: 1rem 1rem;
      border-right: solid 1px #000;
      flex-basis: auto;
    }
  }

  @media only screen and (max-width: 768px) {
    .overview__col--wide {
      width: 100%;
      margin-left: 0;
    }
  }

  @media only screen and (max-width: 360px) {
    .overview__col .is_large {
      font-size: 1rem;
    }
  }

  @media only screen and (max-width: 320px) {
    .overview__col {
      width: 100%;
      flex-basis: auto;
    }
  }

  .overview__col:last-child {
    border-right: solid 1px #000;
  }

  .overview__col .is_large {
    font-size: 1.2rem;
  }

  .overview__col h6 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 400;
    text-transform: uppercase;
  }

  .overview__col p:last-child {
    margin-bottom: 0;
  }

  .overview__col svg {
    width: auto;
    height: 45px;
    margin-bottom: 1rem;
  }

  @media only screen and (max-width: 768px) {
    .overview__col svg {
      height: 35px;
      margin-bottom: 0.5rem;
    }
  }

  .overview__col__map-link:after {
    content: url(../../svg/external-link.svg);
    display: inline-block;
    padding-left: 0.5rem;
  }

  .entry-content>.gform_wrapper {
    margin-top: 3.75rem;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 7.5rem;
    padding-left: 10.9375rem;
    padding-right: 10.9375rem;
    max-width: 1440px;
  }

  @media only screen and (max-width: 1281px) {
    .entry-content>.gform_wrapper {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .entry-content>.gform_wrapper {
      margin-top: 1.875rem;
      margin-bottom: 1.875rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .entry-content>.gform_wrapper {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  .entry-content>.gform_wrapper .gform_heading {
    padding: 6.25rem 7.5rem 0 7.5rem;
    background-color: rgba(245, 234, 232, 0.4);
  }

  @media only screen and (max-width: 1281px) {
    .entry-content>.gform_wrapper .gform_heading {
      padding: 4rem 4rem 0 4rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .entry-content>.gform_wrapper .gform_heading {
      padding: 2rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .entry-content>.gform_wrapper .gform_heading {
      padding: 1rem;
    }
  }

  .entry-content>.gform_wrapper form {
    display: flex;
    flex-direction: column;
    padding: 0 7.5rem;
    background-color: rgba(245, 234, 232, 0.4);
  }

  @media only screen and (max-width: 1281px) {
    .entry-content>.gform_wrapper form {
      padding: 0 4rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .entry-content>.gform_wrapper form {
      padding: 0 2rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .entry-content>.gform_wrapper form {
      padding: 2rem 1rem;
    }
  }

  .entry-content>.gform_wrapper form>div {
    max-width: 60%;
  }

  @media only screen and (max-width: 1025px) {
    .entry-content>.gform_wrapper form>div {
      max-width: 100%;
    }
  }

  .gform_wrapper .gform_heading {
    order: 0;
  }

  .gform_wrapper .gform_title {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 400;
  }

  .gform_wrapper .gsection_title {
    margin: 2rem 0 1.5rem 0;
    font-size: 1.4rem;
    font-family: MessinaSansWeb, sans-serif;
    font-weight: 400;
  }

  .gform_wrapper .instruction {
    font-size: 0.8rem;
  }

  .gform_wrapper .gform_body ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 0;
    padding: 0;
    list-style: none;
  }

  .gform_wrapper .gform_body ul li {
    width: 100%;
    margin: 0;
    padding: 0;
    background: 0 0;
  }

  .gform_wrapper .gform_body ul li.field-date,
  .gform_wrapper .gform_body ul li.field-number {
    width: 42%;
  }

  @media only screen and (max-width: 600px) {

    .gform_wrapper .gform_body ul li.field-date,
    .gform_wrapper .gform_body ul li.field-number {
      width: 51%;
    }
  }

  @media only screen and (max-width: 360px) {

    .gform_wrapper .gform_body ul li.field-date,
    .gform_wrapper .gform_body ul li.field-number {
      width: 100%;
    }
  }

  .gform_wrapper .gform_body ul li.field-date input,
  .gform_wrapper .gform_body ul li.field-number input {
    text-align: center;
  }

  .gform_wrapper .gform_body ul li:after {
    display: none;
  }

  .gform_wrapper .gfield {
    margin: 0 0 1rem 0;
  }

  .gform_wrapper .gfield_label {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.2em;
  }

  .gform_wrapper .gfield_label:after {
    content: ":";
  }

  .gform_wrapper .field-checkbox .gfield_label {
    display: inline-block;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
    text-transform: none;
    letter-spacing: 0;
  }

  .gform_wrapper .ginput_container {
    position: relative;
    margin-bottom: 1rem;
  }

  .gform_wrapper .hidden_label label.gfield_label {
    display: none;
  }

  .gform_wrapper .gform_footer {
    margin-top: 2rem;
  }

  .gform_wrapper .gform_footer__privacy {
    float: right;
  }

  @media only screen and (max-width: 480px) {
    .gform_wrapper .gform_footer__privacy {
      display: block;
      width: 100%;
      margin-top: 1rem;
      float: none;
    }
  }

  .gform_wrapper .gform_ajax_spinner {
    margin-left: 1rem;
  }

  .gform_wrapper .gfield_checkbox li {
    width: 100%;
  }

  .gform_wrapper .gfield_checkbox li,
  .gform_wrapper .gfield_radio li,
  .gform_wrapper .ginput_container_consent {
    position: relative;
    display: inline-block;
    margin: 0 2rem 0.75rem 0;
    padding: 0;
    cursor: pointer;
  }

  .gform_wrapper .gfield_checkbox li input,
  .gform_wrapper .gfield_radio li input,
  .gform_wrapper .ginput_container_consent input {
    position: absolute;
    right: 0;
    width: auto;
    margin-left: 6px;
    opacity: 0;
  }

  .gform_wrapper .gfield_checkbox li label,
  .gform_wrapper .gfield_radio li label,
  .gform_wrapper .ginput_container_consent label {
    position: relative;
    display: inline-block;
    margin: 0.3rem 0;
    padding: 0 0 0 2.25rem;
    cursor: pointer;
    z-index: 1;
  }

  .gform_wrapper .gfield_checkbox li label:before,
  .gform_wrapper .gfield_radio li label:before,
  .gform_wrapper .ginput_container_consent label:before {
    content: "";
    position: absolute;
    display: inline-block;
    top: 50%;
    left: 0;
    width: 1rem;
    height: 1rem;
    margin: 0;
    border: 1px solid #000;
    border-radius: 0;
    background: 0 0;
    background-size: 14px 12px;
    background-position: center center;
    background-repeat: no-repeat;
    cursor: pointer;
    transform: translate(0, -50%);
    transition: all 0.1s ease;
  }

  .gform_wrapper .gfield_checkbox li label:after,
  .gform_wrapper .gfield_radio li label:after,
  .gform_wrapper .ginput_container_consent label:after {
    transition: all 0.1s ease;
  }

  .gform_wrapper .ginput_container_consent {
    display: block;
  }

  .gform_wrapper .gfield_checkbox li input:focus+label::before,
  .gform_wrapper .gfield_checkbox li:hover label:before,
  .gform_wrapper .gfield_radio li input[type="radio"]:focus+label::before,
  .gform_wrapper .gfield_radio li:hover label:before,
  .gform_wrapper .ginput_container_consent input:focus+label::before,
  .gform_wrapper .ginput_container_consent:hover label:before {
    border: 1px solid #93c693;
  }

  .gform_wrapper .gfield_checkbox input:checked,
  .gform_wrapper .gfield_radio input:checked,
  .gform_wrapper .ginput_container_consent input:checked {
    background: #fff;
  }

  .gform_wrapper .gfield_checkbox input:checked+label:before,
  .gform_wrapper .gfield_radio input:checked+label:before,
  .gform_wrapper .ginput_container_consent input:checked+label:before {
    border: 1px solid #93c693;
    background-color: #93c693;
  }

  .gform_wrapper .gfield_checkbox li:after,
  .gform_wrapper .gfield_radio li:after,
  .gform_wrapper .ginput_container_consent:after {
    content: "";
    position: absolute;
    top: 0.9rem;
    left: 0;
    width: 1.3rem;
    height: 1.3rem;
    background-image: none;
    background-size: 12px 10px;
    background-position: center center;
    background-repeat: no-repeat;
    transform: translate(0, -50%);
  }

  .gform_wrapper .gfield_checkbox input:checked+label:before,
  .gform_wrapper .gfield_radio input:checked+label:before,
  .gform_wrapper .ginput_container_consent input:checked+label:before {
    background-image: url(../../svg/check.svg);
  }

  .gform_wrapper .gfield_radio li label:before {
    border-radius: 50%;
  }

  .gform_wrapper .gfield_required {
    padding: 0 0.1rem;
    color: #000;
  }

  .gform_wrapper .validation_error {
    display: flex;
    align-items: center;
    margin: 1rem 0;
    padding: 1rem 0 1rem 2rem;
    color: #000;
    background: url(../../svg/info.svg) left center no-repeat;
    order: 1;
  }

  .gform_wrapper .validation_message {
    display: block;
    margin-top: -0.7rem;
    padding-bottom: 0.5rem;
    font-size: 0.6rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #eb5757;
  }

  .gform_wrapper .gfield_error input {
    border: 2px solid #eb5757;
    background-color: #fff;
  }

  .gform_wrapper .gform_validation_container,
  .gform_wrapper .gform_wrapper .gform_validation_container,
  .gform_wrapper body .gform_wrapper .gform_body ul.gform_fields li.gfield.gform_validation_container,
  .gform_wrapper body .gform_wrapper li.gform_validation_container,
  .gform_wrapper body .gform_wrapper ul.gform_fields li.gfield.gform_validation_container {
    position: absolute !important;
    display: none !important;
    left: -9000px;
  }

  .gform_confirmation_wrapper {
    width: 100%;
    margin-top: 3.75rem;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 7.5rem;
    padding-left: 10.9375rem;
    padding-right: 10.9375rem;
    max-width: 1440px;
  }

  @media only screen and (max-width: 1440px) {
    .gform_confirmation_wrapper {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .gform_confirmation_wrapper {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  .gform_confirmation_wrapper .gform_confirmation_message {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin: 1rem 0;
    padding: 3.75rem 7.5rem;
    font-size: 1.6rem;
    color: #000;
    background-color: rgba(245, 234, 232, 0.4);
  }

  @media only screen and (max-width: 1281px) {
    .gform_confirmation_wrapper .gform_confirmation_message {
      padding: 4rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .gform_confirmation_wrapper .gform_confirmation_message {
      padding: 2rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .gform_confirmation_wrapper .gform_confirmation_message {
      padding: 2rem 1rem;
    }
  }

  .gform_confirmation_wrapper .gform_confirmation_message h3,
  .gform_confirmation_wrapper .gform_confirmation_message h4 {
    margin-bottom: 0rem;
  }

  .ui-datepicker {
    width: 260px;
    padding: 0.5rem;
    border-radius: 0;
    background-color: #fff;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  .ui-datepicker .ui-datepicker-title {
    display: flex;
  }

  .ui-datepicker .ui-datepicker-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    border: none;
    min-height: 40px;
  }

  .ui-datepicker .ui-datepicker-next,
  .ui-datepicker .ui-datepicker-prev {
    position: relative;
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 15px;
    background-color: #f5eae8;
  }

  .ui-datepicker .ui-datepicker-next:before,
  .ui-datepicker .ui-datepicker-prev:before {
    content: "";
    position: absolute;
    display: inline-block;
    top: 14px;
    left: 9px;
    width: 7px;
    height: 7px;
    border-style: solid;
    border-color: #000;
    border-right-width: 2px;
    border-bottom-width: 2px;
    border-left-width: 0;
    border-top-width: 0;
    transform-origin: 0 0;
    transition: all 0.2s ease;
  }

  .ui-datepicker .ui-datepicker-next span,
  .ui-datepicker .ui-datepicker-prev span {
    display: none;
  }

  .ui-datepicker .ui-datepicker-next.ui-datepicker-prev,
  .ui-datepicker .ui-datepicker-prev.ui-datepicker-prev {
    order: 0;
  }

  .ui-datepicker .ui-datepicker-next.ui-datepicker-prev:before,
  .ui-datepicker .ui-datepicker-prev.ui-datepicker-prev:before {
    left: 20px;
    transform: rotate(135deg);
  }

  .ui-datepicker .ui-datepicker-next .ui-datepicker-title,
  .ui-datepicker .ui-datepicker-prev .ui-datepicker-title {
    order: 1;
  }

  .ui-datepicker .ui-datepicker-next.ui-datepicker-next,
  .ui-datepicker .ui-datepicker-prev.ui-datepicker-next {
    order: 2;
  }

  .ui-datepicker .ui-datepicker-next.ui-datepicker-next::before,
  .ui-datepicker .ui-datepicker-prev.ui-datepicker-next::before {
    transform: rotate(-45deg);
  }

  .ui-datepicker .ui-datepicker-next:hover,
  .ui-datepicker .ui-datepicker-prev:hover {
    border: none;
    background-color: #fff;
  }

  .ui-datepicker .ui-datepicker-next:hover:before,
  .ui-datepicker .ui-datepicker-prev:hover:before {
    border-color: #fff;
  }

  .ui-datepicker .ui-datepicker-calendar {
    margin: 0;
    border-collapse: collapse;
  }

  .ui-datepicker .ui-datepicker-calendar td,
  .ui-datepicker .ui-datepicker-calendar th {
    padding: 0;
    text-align: center;
  }

  .ui-datepicker .ui-datepicker-calendar td.ui-datepicker-week-end,
  .ui-datepicker .ui-datepicker-calendar th.ui-datepicker-week-end {
    background-color: #f1f1f1;
  }

  .ui-datepicker .ui-datepicker-calendar td.ui-datepicker-today,
  .ui-datepicker .ui-datepicker-calendar th.ui-datepicker-today {
    background-color: #828282;
  }

  .ui-datepicker .ui-datepicker-calendar a {
    display: inline-block;
    padding: 0.3rem 0.5rem;
    min-width: 34px;
    text-decoration: none;
  }

  .ui-datepicker .ui-datepicker-calendar a:hover {
    color: #000;
    background-color: #93c693;
  }

  .site-header {
    position: fixed;
    width: 100%;
    transition: all 0.2s ease;
    z-index: 999;
  }

  .site-header--scrolled {
    background: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .site-header--scrolled .site-header__container,
  .site-header--scrolled .site-header__title {
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
  }

  .site-header--scrolled .sivututka-header-logo {
    height: 2.5rem;
    width: 2.5rem;
  }

  .hero {
    padding-top: 200px;
  }

  @media only screen and (max-width: 1025px) {
    .hero {
      padding-top: 94px;
    }
  }

  @media only screen and (max-width: 600px) {
    .hero {
      padding-top: 84px;
    }
  }

  .hero-sub {
    margin-top: 200px;
  }

  @media only screen and (max-width: 1281px) {
    .hero-sub {
      margin-top: 105px;
    }
  }

  @media only screen and (max-width: 768px) {
    .hero-sub {
      margin-top: 74px;
    }
  }

  .sticky {
    display: block;
  }

  .hentry {
    margin: 0;
  }

  .updated:not(.published) {
    display: none;
  }

  @media only screen and (max-width: 768px) {
    .entry-content> :first-child {
      margin-top: 0;
    }
  }

  .entry-content>.buildings--bgcolor:last-child,
  .entry-content>.testimonials:last-child {
    margin-bottom: 0;
  }

  .entry-content>.wp-block-media-text:last-child {
    margin-bottom: 1.875rem;
  }

  .page-links {
    clear: both;
    margin: 0 0 1.5rem;
  }

  .post {
    margin: 5rem 0;
  }

  .post-list {
    margin: 5rem 0;
  }

  .post-list__header {
    margin: 0 0 1rem 0;
  }

  .post-list__title {
    margin: 0.3rem 0 0 0;
  }

  .post-list__thumb img {
    display: block;
  }

  .error-404 .page-content,
  .no-results,
  .page-search {
    text-align: center;
  }

  .search-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  .search-form input[type="search"] {
    margin: 0;
  }

  @media only screen and (max-width: 480px) {
    .search-form input[type="search"] {
      width: 100%;
      margin: 0 0 0.5rem 0;
    }
  }

  .search-form input[type="submit"] {
    margin: 0 0 0 1rem;
    padding: 0.5rem 2rem;
    border-radius: 3px;
  }

  @media only screen and (max-width: 480px) {
    .search-form input[type="submit"] {
      width: 100%;
      margin: 0;
      padding: 1rem 2rem;
    }
  }

  @media only screen and (max-width: 480px) {
    .page-search .search-form label {
      width: 100%;
    }
  }

  .page-search .search-form {
    margin-bottom: 5rem;
  }

  .page-search .entry-summary {
    margin: 0;
  }

  .page-search h2 {
    margin: 0.5rem 0;
  }

  .page-search article {
    margin: 5rem 0;
  }

  .page-search article:first-of-type {
    margin-top: 0;
  }

  .page-search .page-search .posts-navigation {
    margin: 3rem 0 0 0;
  }

  .entry-content.page-search {
    text-align: left;
  }

  .entry-content figure figcaption {
    font-size: 0.875rem;
    color: #000;
  }

  .error-404 h3 {
    margin-bottom: 0;
    margin-top: 4rem;
  }

  .error-404 .buildings {
    margin-top: 2rem;
  }

  .buildings {
    margin: 3.75rem 0;
  }

  @media only screen and (max-width: 1025px) {
    .buildings {
      margin: 1.875rem 0;
    }
  }

  @media only screen and (max-width: 768px) {
    .buildings {
      margin: 2rem 0;
    }
  }

  .buildings--bgcolor {
    padding: 3.75rem 0;
    background-color: rgba(245, 234, 232, 0.4);
  }

  @media only screen and (max-width: 768px) {
    .buildings--bgcolor {
      padding: 1.875rem 0;
    }
  }

  .buildings--bgcolor .button {
    background-color: transparent;
  }

  .buildings h3 {
    text-align: center;
  }

  .buildings__list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .buildings__list:after {
    content: "";
    width: 31%;
    height: 0;
  }

  .buildings__list__item {
    position: relative;
    width: 30.5%;
    margin-top: 2rem;
    text-decoration: none;
    z-index: 0;
  }

  @media only screen and (max-width: 1281px) {
    .buildings__list__item {
      width: 31%;
      max-width: 100%;
    }
  }

  @media only screen and (max-width: 1025px) {
    .buildings__list__item {
      width: 48%;
    }
  }

  @media only screen and (max-width: 768px) {
    .buildings__list__item {
      margin-top: 0rem;
    }
  }

  @media only screen and (max-width: 480px) {
    .buildings__list__item {
      width: 100%;
    }
  }

  .buildings__list__item h4 {
    margin-bottom: 0;
  }

  .buildings__list__item img {
    width: 100%;
  }

  .buildings__list__item .button {
    margin-top: 1rem;
    background: #3d6e84 !important;
    border: #3d6e84 !important;
  }

  @media only screen and (max-width: 480px) {
    .buildings__list__item .button {
      margin-bottom: 1.25rem;
      background: #3d6e84 !important;
      border: #3d6e84 !important;
    }
  }

  .buildings__list__item .button--read-more {
    margin-top: 0;
  }

  @media only screen and (max-width: 480px) {
    .buildings__list__item .button--read-more {
      margin-bottom: 0.25rem;
    }
  }

  .buildings__list__item__size {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 96px;
    text-align: center;
    z-index: 1;
  }

  .buildings__list__item__size span {
    position: relative;
    font-size: 1.2rem;
    line-height: 96px;
    color: #000;
  }

  .buildings__list__item__size svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 0;
  }

  .buildings__list__item__size svg path {
    fill: #aed8ed;
  }

  .buildings__list__item a {
    text-decoration: none;
  }

  .buildings__list__item a:hover img,
  .buildings__list__itema:hover img {
    opacity: 0.6;
  }

  .buildings__list__item a:hover .button--read-more:before,
  .buildings__list__itema:hover .button--read-more:before {
    width: calc(100% - 24px);
  }

  .buildings__list__item a:hover .button--read-more:after,
  .buildings__list__itema:hover .button--read-more:after {
    opacity: 0.6;
  }

  .buildings__list__item a:hover .buildings__list__item__size,
  .buildings__list__itema:hover .buildings__list__item__size {
    color: #000;
  }

  .buildings__view-all {
    margin-top: 2rem;
    text-align: center;
  }

  .testimonials {
    margin: 3.75rem 0;
    padding-bottom: 3.75rem;
  }

  @media only screen and (max-width: 768px) {
    .testimonials {
      margin-top: 1.875rem;
      padding-bottom: 1.875rem;
      overflow: hidden;
    }
  }

  .testimonials .container {
    max-width: 1281px;
    padding-left: 0;
    padding-right: 0;
  }

  @media only screen and (max-width: 1440px) {
    .testimonials .container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .testimonials .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  .testimonials--bg {
    padding: 6.25rem 0 3.75rem 0;
    background-color: rgba(245, 234, 232, 0.4);
  }

  @media only screen and (max-width: 768px) {
    .testimonials--bg {
      padding: 2.5rem 0;
    }
  }

  .testimonials h3 {
    margin-top: 0;
    margin-bottom: 3rem;
  }

  @media only screen and (max-width: 768px) {
    .testimonials h3 {
      margin-bottom: 0;
    }
  }

  .testimonials__list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .testimonials__list:after {
    content: "";
    width: 31%;
    height: 0;
  }

  .testimonials__list__col {
    width: 28%;
    text-decoration: none;
    margin-top: 2rem;
    position: relative;
  }

  @media only screen and (max-width: 1025px) {
    .testimonials__list__col {
      width: 50%;
      padding: 0 5%;
    }
  }

  @media only screen and (max-width: 768px) {
    .testimonials__list__col {
      width: 48%;
      padding: 0;
    }
  }

  @media only screen and (max-width: 600px) {
    .testimonials__list__col {
      width: 100%;
    }
  }

  .testimonials__list__col img {
    margin-bottom: 1rem;
  }

  .testimonials__name {
    text-transform: uppercase;
    font-weight: 600;
  }

  .testimonials p:last-child {
    font-size: 1.1rem;
  }

  @media only screen and (max-width: 768px) {
    .testimonials__carousel {
      display: flex;
      flex-direction: column;
    }
  }

  .testimonials__carousel__slider {
    display: block;
    position: relative;
    width: 64%;
  }

  @media only screen and (max-width: 768px) {
    .testimonials__carousel__slider {
      width: 80%;
      order: 1;
    }
  }

  .testimonials__carousel__slider .owl-item {
    padding: 0;
    transition: all 0.4s ease;
  }

  .testimonials__carousel__slider .owl-item.active {
    z-index: 1;
  }

  .testimonials__carousel__slider .owl-stage-outer {
    overflow: visible;
  }

  .testimonials__carousel__slider .owl-nav {
    display: none;
  }

  .testimonials__carousel__slide {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .testimonials__carousel__slide__image {
    width: 40%;
  }

  @media only screen and (max-width: 1025px) {
    .testimonials__carousel__slide__image {
      width: 100%;
    }
  }

  .testimonials__carousel__slide__image img {
    width: 100%;
  }

  .testimonials__carousel__slide__text {
    width: 50%;
    padding-left: 5rem;
  }

  @media only screen and (max-width: 1025px) {
    .testimonials__carousel__slide__text {
      width: 100%;
      padding-left: 0;
      padding-top: 1rem;
    }
  }

  .testimonials__carousel__nav {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 2rem;
  }

  @media only screen and (max-width: 768px) {
    .testimonials__carousel__nav {
      margin-bottom: 0.5rem;
    }
  }

  .testimonials__carousel__nav__numbers {
    font-size: 1.4rem;
  }

  .testimonials__carousel__nav .button {
    display: inline-block;
    padding: 0;
    background: 0 0;
    border: none;
    margin-left: 1rem;
  }

  .testimonials__carousel__nav .button:hover {
    background: 0 0;
    border: none;
    opacity: 0.6;
  }

  .google-map {
    margin-bottom: 2.5rem;
  }

  .google-map__map {
    display: inline-block;
    width: 100%;
    height: 480px;
  }

  .google-map__map div {
    cursor: default !important;
  }

  .google-map__map div .gm-style-pbc div {
    cursor: -webkit-grab !important;
    cursor: grab !important;
  }

  .google-map__map iframe {
    width: 100%;
    height: 100%;
  }

  .image-carousel {
    margin: 3.75rem 0;
  }

  @media only screen and (max-width: 768px) {
    .image-carousel {
      margin: 1.875rem 0;
      overflow: hidden;
    }
  }

  .image-carousel .container {
    padding-left: 0;
    padding-right: 0;
    max-width: 1281px;
  }

  @media only screen and (max-width: 1025px) {
    .image-carousel .container {
      display: flex;
      flex-direction: column;
    }
  }

  .image-carousel__slider {
    position: relative;
    display: block;
    width: 98%;
  }

  @media only screen and (max-width: 1440px) {
    .image-carousel__slider {
      width: 93%;
    }
  }

  @media only screen and (max-width: 1025px) {
    .image-carousel__slider {
      width: 85%;
      order: 2;
    }
  }

  .image-carousel__slider .owl-stage-outer {
    overflow: visible;
  }

  .image-carousel__slider .owl-nav {
    display: none;
  }

  .image-carousel__slide {
    height: 43.75rem;
    overflow: hidden;
  }

  @media only screen and (max-width: 1440px) {
    .image-carousel__slide {
      height: 40rem;
    }
  }

  @media only screen and (max-width: 1025px) {
    .image-carousel__slide {
      height: 27.5rem;
    }
  }

  @media only screen and (max-width: 768px) {
    .image-carousel__slide {
      height: 21.25rem;
    }
  }

  @media only screen and (max-width: 600px) {
    .image-carousel__slide {
      height: 15rem;
    }
  }

  @media only screen and (max-width: 480px) {
    .image-carousel__slide {
      height: 11.25rem;
    }
  }

  .image-carousel__slide__image {
    height: 100%;
  }

  .image-carousel__slide img {
    display: inline-block !important;
    width: auto !important;
    height: 100%;
    max-width: 125rem;
  }

  .image-carousel__slide--vertical-image {
    background-color: #f5eae8;
  }

  .image-carousel__slide--vertical-image .image-carousel__slide__image {
    text-align: center;
  }

  .image-carousel__title-mobile {
    display: none;
  }

  @media only screen and (max-width: 600px) {
    .image-carousel__title-mobile {
      display: block;
      margin-top: 1rem;
      margin-bottom: 1rem;
      padding: 0 1rem;
      font-size: 0.9rem;
      order: 1;
    }
  }

  .image-carousel__nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: 2rem;
  }

  @media only screen and (max-width: 1440px) {
    .image-carousel__nav {
      padding: 0 2rem;
    }
  }

  @media only screen and (max-width: 1025px) {
    .image-carousel__nav {
      margin-top: 0rem;
      margin-bottom: 0.5rem;
      padding: 0 1rem;
      order: 0;
    }
  }

  .image-carousel__nav__numbers {
    padding-right: 3rem;
    font-size: 1.4rem;
  }

  .image-carousel__nav__title {
    font-size: 0.9rem;
    flex: 1;
  }

  @media only screen and (max-width: 600px) {
    .image-carousel__nav__title {
      display: none;
    }
  }

  .image-carousel__nav .button {
    display: inline-block;
    margin-left: 1rem;
    padding: 0;
    border: none;
    background: 0 0;
  }

  .image-carousel__nav .button:hover {
    border: none;
    background: 0 0;
    opacity: 0.6;
  }

  figure.wp-block-image.size-large {
    margin: auto !important;
  }

  a.wp-block-button__link:hover {
    border: none !important;
  }

  .container {
    padding: 0 6.8rem;
  }

  .buildings__list__item>a>p {
    display: none;
  }

  .entry-content ul li:after {
    content: "";
    background-color: transparent;
  }

  .buildings__list__item a:hover img,
  .buildings__list__itema:hover img {
    opacity: 1;
  }

  .buildings__list__item a {
    cursor: default;
  }

  .ast-custom-button-link {
    text-decoration: none;
  }
</style>
<div id="content" class="site-content">
  <main id="main" class="site-main">
    <div id="post-424" class="post-424 page type-page status-publish hentry">
      <div class="entry-content">
        <div class="wp-block-media-text alignwide has-media-on-the-right is-stacked-on-mobile">
          <figure class="wp-block-media-text__media">
            <img loading="lazy" width="1024" height="1024"
              src="https://unihome.fi/wp-content/uploads/unihome-kaytavassa-nelio-1024x1024.jpg" alt=""
              class="wp-image-2957 size-full" srcset="
                https://unihome.fi/wp-content/uploads/unihome-kaytavassa-nelio-1024x1024.jpg 1024w,
                https://unihome.fi/wp-content/uploads/unihome-kaytavassa-nelio-300x300.jpg    300w,
                https://unihome.fi/wp-content/uploads/unihome-kaytavassa-nelio-150x150.jpg    150w,
                https://unihome.fi/wp-content/uploads/unihome-kaytavassa-nelio-768x768.jpg    768w,
                https://unihome.fi/wp-content/uploads/unihome-kaytavassa-nelio-640x640.jpg    640w,
                https://unihome.fi/wp-content/uploads/unihome-kaytavassa-nelio-475x475.jpg    475w,
                https://unihome.fi/wp-content/uploads/unihome-kaytavassa-nelio.jpg           1333w
              " sizes="(max-width: 1024px) 100vw, 1024px" />
          </figure>
          <div class="wp-block-media-text__content">
            <h3>Book online or contact us</h3>

            <p>●&nbsp; Some of the apartments are booked online.</p>

            <p>
              ●&nbsp; The reservation always requires a valid credit card. The
              card will be charged for 20% of the price of the accommodation
              automatically a day before the arrival.
            </p>

            <p>
              ●&nbsp; For university prices please ask your host institution to
              make a reservation.
            </p>

            <p>
              ●&nbsp; The individuals checking in must be 18 years old or older.
              Children with a guardian are welcome.
            </p>
          </div>
        </div>

        <section class="buildings">
          <div class="container">
            <h3>Select location</h3>
            <div id="buildings_list" class="buildings__list"></div>
          </div>
        </section>
      </div>
    </div>
    <!-- Post end -->
  </main>
  <!-- #main -->
</div>