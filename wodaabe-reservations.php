<?php
/**
 * @package Wodaabe_Reservations
 * @version 1.2.0
 */
/*
Plugin Name: Wodaabe Reservations System
Plugin URI: https://fsli-group.com
Description: This plugin is used in order to implement a reservation system on the wodaabe website.
Author: FSLI Group
Version: 1.2.0
Author URI: https://fsli-group.com
 */

if (!defined('ABSPATH')) {
  exit; // Exit if accessed directly.
}
// Plugin Folder URL.
if (!defined('WODAABE_RESERVATIONS_PLUGIN_URL')) {
  define('WODAABE_RESERVATIONS_PLUGIN_URL', plugin_dir_url(__FILE__));
}

// Plugin Folder Path.
if (!defined('WODAABE_RESERVATIONS_PLUGIN_PATH')) {
  define('WODAABE_RESERVATIONS_PLUGIN_PATH', plugin_dir_path(__FILE__));
}

require_once WODAABE_RESERVATIONS_PLUGIN_PATH . 'src/includes/constants.php';
require_once WODAABE_RESERVATIONS_PLUGIN_PATH . 'src/includes/functions.php';
require_once WODAABE_RESERVATIONS_PLUGIN_PATH . 'src/includes/class-wrs-products-list.php';
require_once WODAABE_RESERVATIONS_PLUGIN_PATH . 'src/includes/class-wrs-locations-list.php';
require_once WODAABE_RESERVATIONS_PLUGIN_PATH . 'src/includes/class-wrs-reservations-list.php';
require_once WODAABE_RESERVATIONS_PLUGIN_PATH . 'src/includes/class-wrs-icalendar.php';
require_once WODAABE_RESERVATIONS_PLUGIN_PATH . 'src/includes/class-wrs-prices-and-promos.php';
require_once WODAABE_RESERVATIONS_PLUGIN_PATH . 'src/includes/class-wodaabe-reservations.php';
require_once WODAABE_RESERVATIONS_PLUGIN_PATH . 'src/includes/class-wodaabe-reservations-product-blocking.php';

require_once WODAABE_RESERVATIONS_PLUGIN_PATH . '/vendor/autoload.php';

function wrs_creates_tables() {
  Wodaabe_Reservations::createTables();
}
register_activation_hook(__FILE__, 'wrs_creates_tables');

$wrsModule = new Wodaabe_Reservations();

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();