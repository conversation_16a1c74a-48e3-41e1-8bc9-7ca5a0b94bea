<?php

$constants = array(
  array(
    "name" => "WODAABE_RESERVATIONS_ADMIN_CSS_URL",
    "value" => WODAABE_RESERVATIONS_PLUGIN_URL . 'assets/css/admin.css',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_ADMIN_JS_URL",
    "value" => WODAABE_RESERVATIONS_PLUGIN_URL . 'assets/js/admin.js',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_FRONT_JS_URL",
    "value" => WODAABE_RESERVATIONS_PLUGIN_URL . 'assets/js/front.js',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_JS_URL",
    "value" => WODAABE_RESERVATIONS_PLUGIN_URL . 'assets/js/all.js',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_ASSETS_VERSION",
    "value" => '1.0.8',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_PRODUCT_TABLE",
    "value" => 'wrs_products',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_LOCATION_TABLE",
    "value" => 'wrs_locations',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_CURRENCY_TABLE",
    "value" => 'wrs_currencies',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_LANGUAGE_TABLE",
    "value" => 'wrs_languages',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_CONFIGURATION_TABLE",
    "value" => 'wrs_configurations',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_PROMOTIONAL_CODE_TABLE",
    "value" => 'wrs_promotional_codes',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_PROMOTIONAL_CODE_PRODUCTS_TABLE",
    "value" => 'wrs_promotional_code_products',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_PRODUCTS_BLOCK_TABLE",
    "value" => 'wrs_product_block',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_PERSON_TABLE",
    "value" => 'wrs_persons',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_TABLE",
    "value" => 'wrs_reservations',
  ),

  array(
    "name" => "WODAABE_RESERVATIONS_ITEM_TABLE",
    "value" => 'wrs_reservation_items',
  ),
  array(
    "name" => "WODAABE_RESERVATIONS_PAYMENT_TABLE",
    "value" => 'wrs_reservation_payments',
  ),
);

foreach ($constants as $value) {
  if (!defined($value['name'])) {
    define($value['name'], $value['value']);
  }
}
