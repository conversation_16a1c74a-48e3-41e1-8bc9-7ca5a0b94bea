
[2023-01-23 03:01:41] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-23 06:02:42] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-23 06:19:57] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-23 06:20:12] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-24","date_departure":"2023-01-26","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:20:43] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:21:32] INFO Processing new AJAX Request width payload {"email":"<EMAIL>","wrs_action":"sent_code","action":"wrs_process_ajax"}
[2023-01-23 06:21:36] INFO Processing new AJAX Request width payload {"code":"9827","wrs_action":"verify_code","action":"wrs_process_ajax"}
[2023-01-23 06:21:41] INFO Processing new AJAX Request width payload {"id":"null","locationId":"1","currency_id":"1","language_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","promocodevalue":"undefined","product_id":"6","adult_count":"1","child_count":"0","first_name":"kongnuy","last_name":"victorien","email":"<EMAIL>","phone":"0690210677","country":"FR","notes":"Nothing sp\u00e9cial","buyer_first_name":"","buyer_last_name":"","buyer_email":"","buyer_phone":"","is_guess":"undefined","agree_policy":"false","receive_marketing_update":"false","quantity":"1","wrs_action":"save_order","action":"wrs_process_ajax"}
[2023-01-23 06:22:37] INFO Processing new AJAX Request width payload {"status":"COMPLETED","transaction_id":"7ED8785644788764W","id":"72","wrs_action":"update_payment_status","action":"wrs_process_ajax"}
[2023-01-23 06:22:37] INFO {"Booking ID":"72","UUID":"acd9c6c3-d142-4517-832d-1ceaee9924a3","Date arrival":"2023-01-27","Date departure":"2023-01-28","Adult count":"1","Child count":"0","Product":"Test Leo","Location":"Paris","Language":"English","Currency":"EURO","Payment Status":"COMPLETED","Payment ID":"7ED8785644788764W","Client Name":"kongnuy victorien","Client Email":"<EMAIL>","Client Phone":"0690210677"}
[2023-01-23 06:22:37] INFO =========
[2023-01-23 06:22:37] INFO 
   <!--[if gte mso 15]>
	<xml>
		<o:OfficeDocumentSettings>
		<o:AllowPNG/>
		<o:PixelsPerInch>96</o:PixelsPerInch>
		</o:OfficeDocumentSettings>
	</xml>
	<![endif]-->
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Wodaabe Stay</title>
    <style type="text/css">
		p{
			margin:10px 0;
			padding:0;
		}
		table{
			border-collapse:collapse;
		}
		h1,h2,h3,h4,h5,h6{
			display:block;
			margin:0;
			padding:0;
		}
		img,a img{
			border:0;
			height:auto;
			outline:none;
			text-decoration:none;
		}
		body,#bodyTable,#bodyCell{
			height:100%;
			margin:0;
			padding:0;
			width:100%;
		}
		#outlook a{
			padding:0;
		}
		img{
			-ms-interpolation-mode:bicubic;
		}
		table{
			mso-table-lspace:0pt;
			mso-table-rspace:0pt;
		}
		.ReadMsgBody{
			width:100%;
		}
		.ExternalClass{
			width:100%;
		}
		p,a,li,td,blockquote{
			mso-line-height-rule:exactly;
		}
		a[href^=tel],a[href^=sms]{
			color:inherit;
			cursor:default;
			text-decoration:none;
		}
		p,a,li,td,body,table,blockquote{
			-ms-text-size-adjust:100%;
			-webkit-text-size-adjust:100%;
		}
		.ExternalClass,.ExternalClass p,.ExternalClass td,.ExternalClass div,.ExternalClass span,.ExternalClass font{
			line-height:100%;
		}
		a[x-apple-data-detectors]{
			color:inherit !important;
			text-decoration:none !important;
			font-size:inherit !important;
			font-family:inherit !important;
			font-weight:inherit !important;
			line-height:inherit !important;
		}
		#bodyCell{
			padding:50px 50px;
		}
		.templateContainer{
			max-width:600px !important;
			border:0;
		}
		a.mcnButton{
			display:block;
		}
		.mcnTextContent{
			word-break:break-word;
		}
		.mcnTextContent img{
			height:auto !important;
		}
		.mcnDividerBlock{
			table-layout:fixed !important;
		}
		/***** Make theme edits below if needed *****/
		/* Page - Background Style */
		body,#bodyTable{
			background-color:#e9eaec;
		}
		/* Page - Heading 1 */
		h1{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:26px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 2 */
		h2{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:22px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 3 */
		h3{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:20px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 4 */
		h4{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:18px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Header - Header Style */
		#templateHeader{
			border-top:0;
			border-bottom:0;
			padding-top:0;
			padding-bottom:20px;
			text-align: center;
		}
		/* Body - Body Style */
		#templateBody{
			background-color:#FFFFFF;
			border-top:0;
			border: 1px solid #c1c1c1;
			padding-top:0;
			padding-bottom:0px;
		}
		/* Body -Body Text */
		#templateBody .mcnTextContent,
		#templateBody .mcnTextContent p{
			color:#555555;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:14px;
			line-height:150%;
		}
		/* Body - Body Link */
		#templateBody .mcnTextContent a,
		#templateBody .mcnTextContent p a{
			color:#ff7f50;
			font-weight:normal;
			text-decoration:underline;
		}
		/* Footer - Footer Style */
		#templateFooter{
			background-color:#e9eaec;
			border-top:0;
			border-bottom:0;
			padding-top:12px;
			padding-bottom:12px;
		}
		/* Footer - Footer Text */
		#templateFooter .mcnTextContent,
		#templateFooter .mcnTextContent p{
			color:#cccccc;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:12px;
			line-height:150%;
			text-align:center;
		}
		/* Footer - Footer Link */
		#templateFooter .mcnTextContent a,
		#templateFooter .mcnTextContent p a{
			color:#cccccc;
			font-weight:normal;
			text-decoration:underline;
		}
		@media only screen and (min-width:768px){
			.templateContainer{
				width:600px !important;
			}
		}
		@media only screen and (max-width: 480px){
			body,table,td,p,a,li,blockquote{
				-webkit-text-size-adjust:none !important;
			}
		}
		@media only screen and (max-width: 480px){
			body{
				width:100% !important;
				min-width:100% !important;
			}
		}
		@media only screen and (max-width: 680px){
			#bodyCell{
				padding:20px 20px !important;
			}
		}
		@media only screen and (max-width: 480px){
			.mcnTextContentContainer{
				max-width:100% !important;
				width:100% !important;
			}
		}
	</style>
	<center>
		<table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;background-color: #e9eaec;">
			<tbody><tr>
				<td align="center" valign="top" id="bodyCell" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 50px 50px;width: 100%;">
					<!-- BEGIN TEMPLATE // -->
					<!--[if gte mso 9]>
					<table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
					<tr>
					<td align="center" valign="top" width="600" style="width:600px;">
					<![endif]-->
					<table border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;border: 0;max-width: 600px !important;">
												<tbody><tr>
							<td valign="top" id="templateBody" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #FFFFFF;border-top: 0;border: 1px solid #c1c1c1;padding-top: 0;padding-bottom: 0px;">
								<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
									<tbody class="mcnTextBlockOuter">
										<tr>
											<td valign="top" class="mcnTextBlockInner" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
												<table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer">
													<tbody>
													    
														<tr>
															<td valign="top" style="padding-top: 30px;padding-right: 30px;padding-bottom: 30px;padding-left: 30px;" class="mcnTextContent">
															 <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
															    <tboby>
															        <tr>
														    <td>

														        Dear kongnuy victorien,  <br><br>
                                                                Thank you for choosing Wodaabe stay for your stay! Would you have your estimated arrival time already?  <br> <br>
                                                                <b style="font-size: 2rem;">Booking details</b>  <br>
														        <br>
														    </td>
														</tr>
															    </tboby>
															    
															</table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                	
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Booking ID</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">72</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                	
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Date arrival</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">2023-01-27</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                	
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Date departure</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">2023-01-28</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                	
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Adult count</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">1</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                	
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Child count</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">0</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                	
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Product</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">Test Leo</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                	
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Location</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">Paris</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                	
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Payment Status</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">COMPLETED</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                	
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Client Name</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">kongnuy victorien</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                            
															</td>
														</tr>
														<tr>
														    <td>
														       
														        <br>
														        <br>
														        <center>


														        By e-mail you can reach us from the following address: <EMAIL>  <br>
                                                                Welcome to Wodaabe Stay!  <br> <br>
                                                                Kind regards,  <br>
                                                                Léo / Wodaabe Stay<br/><br/>
                                                                </center>
														    </td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr>
							<td valign="top" id="templateFooter" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #e9eaec;border-top: 0;border-bottom: 0;padding-top: 12px;padding-bottom: 12px;">
								<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
									<tbody class="mcnTextBlockOuter">
										<tr>
											<td valign="top" class="mcnTextBlockInner" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
												<table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer">
													<tbody>
														<tr>
															<td valign="top" class="mcnTextContent" style="padding-top: 9px;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #aaa;font-family: Helvetica;font-size: 12px;line-height: 150%;text-align: center;">

																<!-- Footer content -->
																Copyright <a href="https://wodaabe-stays.com" style="color:#bbbbbb;">wodaabe-stays.com</a> 2023															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</tbody></table>
					<!--[if gte mso 9]>
					</td>
					</tr>
					</table>
					<![endif]-->
					<!-- // END TEMPLATE -->
					</td>
				</tr>
			</tbody></table>
		</center>

[2023-01-23 06:22:37] INFO =========
[2023-01-23 06:22:37] INFO     <<EMAIL>>
    From: Wodaabe Stay <<EMAIL>>
    Reply-To: <EMAIL>
    Content-Type: text/html; charset=utf-8
    <<EMAIL>>

[2023-01-23 06:22:37] INFO ========= result 2 === 1
[2023-01-23 06:23:07] INFO =========
[2023-01-23 06:23:07] INFO 
   <!--[if gte mso 15]>
	<xml>
		<o:OfficeDocumentSettings>
		<o:AllowPNG/>
		<o:PixelsPerInch>96</o:PixelsPerInch>
		</o:OfficeDocumentSettings>
	</xml>
	<![endif]-->
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Wodaabe Stay</title>
    <style type="text/css">
		p{
			margin:10px 0;
			padding:0;
		}
		table{
			border-collapse:collapse;
		}
		h1,h2,h3,h4,h5,h6{
			display:block;
			margin:0;
			padding:0;
		}
		img,a img{
			border:0;
			height:auto;
			outline:none;
			text-decoration:none;
		}
		body,#bodyTable,#bodyCell{
			height:100%;
			margin:0;
			padding:0;
			width:100%;
		}
		#outlook a{
			padding:0;
		}
		img{
			-ms-interpolation-mode:bicubic;
		}
		table{
			mso-table-lspace:0pt;
			mso-table-rspace:0pt;
		}
		.ReadMsgBody{
			width:100%;
		}
		.ExternalClass{
			width:100%;
		}
		p,a,li,td,blockquote{
			mso-line-height-rule:exactly;
		}
		a[href^=tel],a[href^=sms]{
			color:inherit;
			cursor:default;
			text-decoration:none;
		}
		p,a,li,td,body,table,blockquote{
			-ms-text-size-adjust:100%;
			-webkit-text-size-adjust:100%;
		}
		.ExternalClass,.ExternalClass p,.ExternalClass td,.ExternalClass div,.ExternalClass span,.ExternalClass font{
			line-height:100%;
		}
		a[x-apple-data-detectors]{
			color:inherit !important;
			text-decoration:none !important;
			font-size:inherit !important;
			font-family:inherit !important;
			font-weight:inherit !important;
			line-height:inherit !important;
		}
		#bodyCell{
			padding:50px 50px;
		}
		.templateContainer{
			max-width:600px !important;
			border:0;
		}
		a.mcnButton{
			display:block;
		}
		.mcnTextContent{
			word-break:break-word;
		}
		.mcnTextContent img{
			height:auto !important;
		}
		.mcnDividerBlock{
			table-layout:fixed !important;
		}
		/***** Make theme edits below if needed *****/
		/* Page - Background Style */
		body,#bodyTable{
			background-color:#e9eaec;
		}
		/* Page - Heading 1 */
		h1{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:26px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 2 */
		h2{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:22px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 3 */
		h3{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:20px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 4 */
		h4{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:18px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Header - Header Style */
		#templateHeader{
			border-top:0;
			border-bottom:0;
			padding-top:0;
			padding-bottom:20px;
			text-align: center;
		}
		/* Body - Body Style */
		#templateBody{
			background-color:#FFFFFF;
			border-top:0;
			border: 1px solid #c1c1c1;
			padding-top:0;
			padding-bottom:0px;
		}
		/* Body -Body Text */
		#templateBody .mcnTextContent,
		#templateBody .mcnTextContent p{
			color:#555555;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:14px;
			line-height:150%;
		}
		/* Body - Body Link */
		#templateBody .mcnTextContent a,
		#templateBody .mcnTextContent p a{
			color:#ff7f50;
			font-weight:normal;
			text-decoration:underline;
		}
		/* Footer - Footer Style */
		#templateFooter{
			background-color:#e9eaec;
			border-top:0;
			border-bottom:0;
			padding-top:12px;
			padding-bottom:12px;
		}
		/* Footer - Footer Text */
		#templateFooter .mcnTextContent,
		#templateFooter .mcnTextContent p{
			color:#cccccc;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:12px;
			line-height:150%;
			text-align:center;
		}
		/* Footer - Footer Link */
		#templateFooter .mcnTextContent a,
		#templateFooter .mcnTextContent p a{
			color:#cccccc;
			font-weight:normal;
			text-decoration:underline;
		}
		@media only screen and (min-width:768px){
			.templateContainer{
				width:600px !important;
			}
		}
		@media only screen and (max-width: 480px){
			body,table,td,p,a,li,blockquote{
				-webkit-text-size-adjust:none !important;
			}
		}
		@media only screen and (max-width: 480px){
			body{
				width:100% !important;
				min-width:100% !important;
			}
		}
		@media only screen and (max-width: 680px){
			#bodyCell{
				padding:20px 20px !important;
			}
		}
		@media only screen and (max-width: 480px){
			.mcnTextContentContainer{
				max-width:100% !important;
				width:100% !important;
			}
		}
	</style>
	<center>
		<table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;background-color: #e9eaec;">
			<tbody><tr>
				<td align="center" valign="top" id="bodyCell" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 50px 50px;width: 100%;">
					<!-- BEGIN TEMPLATE // -->
					<!--[if gte mso 9]>
					<table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
					<tr>
					<td align="center" valign="top" width="600" style="width:600px;">
					<![endif]-->
					<table border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;border: 0;max-width: 600px !important;">
												<tbody><tr>
							<td valign="top" id="templateBody" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #FFFFFF;border-top: 0;border: 1px solid #c1c1c1;padding-top: 0;padding-bottom: 0px;">
								<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
									<tbody class="mcnTextBlockOuter">
										<tr>
											<td valign="top" class="mcnTextBlockInner" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
												<table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer">
													<tbody>
													    
														<tr>
															<td valign="top" style="padding-top: 30px;padding-right: 30px;padding-bottom: 30px;padding-left: 30px;" class="mcnTextContent">
															 <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
															    <tboby>
															        <tr>
														   <td>

														        Dear Administrator,  <br><br>
                                                                There's a new reservation available  <br> <br>
                                                                <b style="font-size: 2rem;">Booking details</b>  <br>
														        <br>
														    </td>
														</tr>
															    </tboby>
															    
															</table>

								
                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Booking ID</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">72</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>UUID</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">acd9c6c3-d142-4517-832d-1ceaee9924a3</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Date arrival</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">2023-01-27</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Date departure</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">2023-01-28</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Adult count</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">1</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Child count</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">0</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Product</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">Test Leo</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Location</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">Paris</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Language</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">English</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Currency</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">EURO</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Payment Status</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">COMPLETED</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Payment ID</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">7ED8785644788764W</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Client Name</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">kongnuy victorien</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Client Email</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;"><EMAIL></td>
                                            </tr>
                                            </tbody>
                                            </table>

                                                                                              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>
                                                
                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong>Client Phone</strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;">0690210677</td>
                                            </tr>
                                            </tbody>
                                            </table>

                                            
															</td>
														</tr>
														
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr>
							<td valign="top" id="templateFooter" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #e9eaec;border-top: 0;border-bottom: 0;padding-top: 12px;padding-bottom: 12px;">
								<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
									<tbody class="mcnTextBlockOuter">
										<tr>
											<td valign="top" class="mcnTextBlockInner" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
												<table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer">
													<tbody>
														<tr>
															<td valign="top" class="mcnTextContent" style="padding-top: 9px;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #aaa;font-family: Helvetica;font-size: 12px;line-height: 150%;text-align: center;">

																<!-- Footer content -->
																Copyright <a href="https://wodaabe-stays.com" style="color:#bbbbbb;">wodaabe-stays.com</a> 2023															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</tbody></table>
					<!--[if gte mso 9]>
					</td>
					</tr>
					</table>
					<![endif]-->
					<!-- // END TEMPLATE -->
					</td>
				</tr>
			</tbody></table>
		</center>

[2023-01-23 06:23:07] INFO =========
[2023-01-23 06:23:07] INFO     <<EMAIL>>
    From: Wodaabe Stay <<EMAIL>>
    Reply-To: <EMAIL>
    Content-Type: text/html; charset=utf-8
    <<EMAIL>>

[2023-01-23 06:23:07] INFO ========= result 1 === 1
[2023-01-23 06:25:21] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-23 06:26:34] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:31:46] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:36:58] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-23 06:37:08] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:40:30] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:40:51] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:43:07] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:46:22] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-23 06:46:27] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-23 06:47:58] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:49:45] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:51:05] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-28","date_departure":"2023-01-29","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:51:49] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-28","date_departure":"2023-01-29","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:52:27] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}
[2023-01-23 06:53:14] INFO Processing new AJAX Request width payload {"wrs_action":"get_locations","action":"wrs_process_ajax"}
[2023-01-23 06:53:29] INFO Processing new AJAX Request width payload {"location_id":"1","date_arrive":"2023-01-27","date_departure":"2023-01-28","wrs_action":"get_products","action":"wrs_process_ajax"}